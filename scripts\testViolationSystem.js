// scripts/testViolationSystem.js
const mongoose = require('mongoose');
const User = require('../models/User');
const Class = require('../models/Class');
const Violation = require('../models/Violation');
const StudentConduct = require('../models/StudentConduct');
const { VIOLATION_TYPES, VIOLATION_STATUS } = require('../constants/violationConstants');
const { ROLES } = require('../constants/roleConstants');
const { sendAllViolationNotifications } = require('../services/violationNotificationService');

// Kết nối database
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/school_management');
    console.log('✅ Đã kết nối MongoDB');
  } catch (error) {
    console.error('❌ Lỗi kết nối MongoDB:', error);
    process.exit(1);
  }
};

// Tạo dữ liệu test
const createTestData = async () => {
  console.log('\n📝 Tạo dữ liệu test...');

  // Tạo admin user
  const admin = await User.findOneAndUpdate(
    { email: '<EMAIL>' },
    {
      name: 'Admin Test',
      email: '<EMAIL>',
      password: '$2a$10$example', // Mật khẩu đã hash
      role: [ROLES.ADMIN],
      phoneNumber: '0123456789'
    },
    { upsert: true, new: true }
  );

  // Tạo teacher user
  const teacher = await User.findOneAndUpdate(
    { email: '<EMAIL>' },
    {
      name: 'Nguyễn Văn Giáo',
      email: '<EMAIL>',
      password: '$2a$10$example',
      role: [ROLES.TEACHER],
      phoneNumber: '0987654321'
    },
    { upsert: true, new: true }
  );

  // Tạo student user
  const student = await User.findOneAndUpdate(
    { email: '<EMAIL>' },
    {
      name: 'Trần Văn Học',
      email: '<EMAIL>',
      password: '$2a$10$example',
      role: [ROLES.STUDENT],
      studentId: 'HS001',
      phoneNumber: '0123456788',
      parentPhone: '0987654322'
    },
    { upsert: true, new: true }
  );

  // Tạo class
  const testClass = await Class.findOneAndUpdate(
    { name: '10A1', schoolYear: '2024-2025' },
    {
      name: '10A1',
      schoolYear: '2024-2025',
      homeroomTeacher: teacher._id,
      totalStudents: 30
    },
    { upsert: true, new: true }
  );

  // Tạo StudentConduct record
  const studentConduct = await StudentConduct.findOneAndUpdate(
    { student: student._id, schoolYear: '2024-2025' },
    {
      student: student._id,
      class: testClass._id,
      schoolYear: '2024-2025',
      currentPoints: 50,
      totalViolations: 0,
      pointHistory: []
    },
    { upsert: true, new: true }
  );

  console.log('✅ Đã tạo dữ liệu test');
  return { admin, teacher, student, testClass, studentConduct };
};

// Test tạo vi phạm
const testCreateViolation = async (testData) => {
  console.log('\n🧪 Test tạo vi phạm...');
  
  const { teacher, student, testClass } = testData;

  try {
    // Tạo vi phạm
    const violation = new Violation({
      student: student._id,
      class: testClass._id,
      schoolYear: '2024-2025',
      violationType: VIOLATION_TYPES.LATE,
      description: 'Đi học muộn 15 phút không có lý do chính đáng',
      violationDate: new Date(),
      reportedBy: teacher._id,
      status: VIOLATION_STATUS.PENDING
    });

    await violation.save();
    await violation.processViolation(teacher._id);

    console.log('✅ Tạo vi phạm thành công:', violation._id);
    return violation;
  } catch (error) {
    console.error('❌ Lỗi tạo vi phạm:', error);
    throw error;
  }
};

// Test hệ thống thông báo
const testNotificationSystem = async (violation, testData) => {
  console.log('\n📨 Test hệ thống thông báo...');
  
  const { teacher, student } = testData;

  try {
    // Lấy thông tin điểm thi đua
    const studentConduct = await StudentConduct.findOne({
      student: student._id,
      schoolYear: '2024-2025'
    });

    // Test gửi thông báo
    const notificationResults = await sendAllViolationNotifications(
      violation,
      studentConduct,
      teacher
    );

    console.log('✅ Kết quả gửi thông báo:', {
      homeroomSent: !!notificationResults.homeroomNotification,
      parentSent: !!notificationResults.parentNotification,
      principalSent: !!notificationResults.principalNotification,
      errors: notificationResults.errors
    });

    return notificationResults;
  } catch (error) {
    console.error('❌ Lỗi test thông báo:', error);
    throw error;
  }
};

// Test khiếu nại vi phạm
const testAppealSystem = async (violation, testData) => {
  console.log('\n⚖️ Test hệ thống khiếu nại...');
  
  const { teacher, student } = testData;

  try {
    // Student khiếu nại
    await violation.appealViolation('Tôi có lý do chính đáng cho việc đi muộn do kẹt xe');
    console.log('✅ Học sinh khiếu nại thành công');

    // Teacher xử lý khiếu nại
    await violation.processAppeal('approved', teacher._id);
    console.log('✅ Giáo viên xử lý khiếu nại thành công');

    // Hoàn tác điểm thi đua
    const studentConduct = await StudentConduct.findOne({
      student: student._id,
      schoolYear: '2024-2025'
    });

    if (studentConduct) {
      await studentConduct.revertViolation(violation._id, teacher._id);
      console.log('✅ Hoàn tác điểm thi đua thành công');
    }

    return true;
  } catch (error) {
    console.error('❌ Lỗi test khiếu nại:', error);
    throw error;
  }
};

// Test thống kê
const testStatistics = async () => {
  console.log('\n📊 Test thống kê...');

  try {
    // Test thống kê tổng quan
    const overallStats = await Violation.aggregate([
      {
        $group: {
          _id: null,
          totalViolations: { $sum: 1 },
          totalPointsDeducted: { $sum: '$pointsDeducted' },
          uniqueStudents: { $addToSet: '$student' }
        }
      }
    ]);

    console.log('📈 Thống kê tổng quan:', overallStats[0] || { totalViolations: 0 });

    // Test thống kê theo loại vi phạm
    const typeStats = await Violation.aggregate([
      {
        $group: {
          _id: '$violationType',
          count: { $sum: 1 },
          totalPoints: { $sum: '$pointsDeducted' }
        }
      },
      { $sort: { count: -1 } }
    ]);

    console.log('📈 Thống kê theo loại vi phạm:', typeStats);

    // Test thống kê điểm thi đua
    const conductStats = await StudentConduct.aggregate([
      {
        $group: {
          _id: null,
          avgPoints: { $avg: '$currentPoints' },
          minPoints: { $min: '$currentPoints' },
          maxPoints: { $max: '$currentPoints' },
          totalStudents: { $sum: 1 }
        }
      }
    ]);

    console.log('📈 Thống kê điểm thi đua:', conductStats[0] || { totalStudents: 0 });

    return true;
  } catch (error) {
    console.error('❌ Lỗi test thống kê:', error);
    throw error;
  }
};

// Chạy tất cả test
const runAllTests = async () => {
  console.log('🚀 Bắt đầu test hệ thống vi phạm...');

  try {
    await connectDB();

    // Tạo dữ liệu test
    const testData = await createTestData();

    // Test tạo vi phạm
    const violation = await testCreateViolation(testData);

    // Test hệ thống thông báo
    await testNotificationSystem(violation, testData);

    // Test hệ thống khiếu nại
    await testAppealSystem(violation, testData);

    // Test thống kê
    await testStatistics();

    console.log('\n🎉 Tất cả test đã hoàn thành thành công!');
    
  } catch (error) {
    console.error('\n💥 Test thất bại:', error);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Đã đóng kết nối database');
  }
};

// Chạy script nếu được gọi trực tiếp
if (require.main === module) {
  runAllTests();
}

module.exports = {
  runAllTests,
  createTestData,
  testCreateViolation,
  testNotificationSystem,
  testAppealSystem,
  testStatistics
};
