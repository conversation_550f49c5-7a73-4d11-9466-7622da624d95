/**
 * Constants Index - Centralized export of all constants
 * Provides a single entry point for importing constants across the application
 */

// Import all constants
const MESSAGES = require('./messages');
const ROLES = require('./roleConstants');
const { HTTP_STATUS, createSuccessR<PERSON>ponse, createErrorResponse, createPaginatedResponse } = require('./httpConstants');
const { VALIDATION_PATTERNS, VALIDATION_RULES, VALIDATION_MESSAGES, validateRequiredFields, validateObjectId, validatePhoneNumber, validateStudentId, validatePassword, validateName } = require('./validationConstants');
const { ANNOUNCEMENT_TYPES, ANNOUNCEMENT_STATUS, ANNOUNCEMENT_TYPE_LABELS, ANNOUNCEMENT_STATUS_LABELS, DEPARTMENTS } = require('./announcementConstants');
const { GENDER, STUDENT_SPECIFIC_ROLES, TEACHER_SPECIFIC_ROLES, DISPLAY_ROLES, DEFAULT_SPECIFIC_ROLES } = require('./userConstants');
const { ATTENDANCE_STATUS, ATTENDANCE_TYPE, ATTENDANCE_STATUS_LABELS, ATTENDANCE_TYPE_LABELS } = require('./attendanceConstants');
const { VALID_DEPARTMENT_GROUPS, getDepartmentGroupBySubject } = require('./departments');

// Export all constants
module.exports = {
  // Messages
  MESSAGES,
  
  // Roles
  ROLES,
  
  // HTTP
  HTTP_STATUS,
  createSuccessResponse,
  createErrorResponse,
  createPaginatedResponse,
  
  // Validation
  VALIDATION_PATTERNS,
  VALIDATION_RULES,
  VALIDATION_MESSAGES,
  validateRequiredFields,
  validateObjectId,
  validatePhoneNumber,
  validateStudentId,
  validatePassword,
  validateName,
  
  // Announcements
  ANNOUNCEMENT_TYPES,
  ANNOUNCEMENT_STATUS,
  ANNOUNCEMENT_TYPE_LABELS,
  ANNOUNCEMENT_STATUS_LABELS,
  DEPARTMENTS,
  
  // Users
  GENDER,
  STUDENT_SPECIFIC_ROLES,
  TEACHER_SPECIFIC_ROLES,
  DISPLAY_ROLES,
  DEFAULT_SPECIFIC_ROLES,
  
  // Attendance
  ATTENDANCE_STATUS,
  ATTENDANCE_TYPE,
  ATTENDANCE_STATUS_LABELS,
  ATTENDANCE_TYPE_LABELS,
  
  // Departments
  VALID_DEPARTMENT_GROUPS,
  getDepartmentGroupBySubject
}; 