const ExamSupervision = require('../models/ExamSupervision');
const User = require('../models/User');
const { sortSessions } = require('../utils/dateHelpers');
const ROLES = require('../constants/roleConstants');

// @desc    Lấy danh sách phân công coi thi (Admin)
exports.getExamSupervisions = async (req, res) => {
  try {
    const { schoolYear, examType, teacher, status } = req.query;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    let query = {};
    if (schoolYear) query.schoolYear = schoolYear;
    if (examType) query.examType = examType;
    if (teacher) query.teacher = teacher;
    if (status) query['sessions.status'] = status;

    const supervisions = await ExamSupervision.find(query)
      .populate('teacher', 'name email')
      .populate('createdBy', 'name')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    const total = await ExamSupervision.countDocuments(query);

    // Sắp xếp sessions trong mỗi supervision
    supervisions.forEach(supervision => {
      supervision.sessions = sortSessions(supervision.sessions);
    });

    res.json({
      supervisions,
      pagination: {
        current: page,
        pages: Math.ceil(total / limit),
        total
      }
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ msg: 'Lỗi server' });
  }
};

// @desc    Lấy phân công coi thi của giáo viên đang đăng nhập
exports.getMyExamAssignments = async (req, res) => {
  try {
    const { schoolYear, examType, status } = req.query;

    const query = { teacher: req.user.id };
    if (schoolYear) query.schoolYear = schoolYear;
    if (examType) query.examType = examType;
    if (status) query['sessions.status'] = status;

    const supervisions = await ExamSupervision.find(query)
      .populate('teacher', 'name email')
      .sort({ createdAt: -1 });

    // Sắp xếp sessions và thêm sessionNumber
    supervisions.forEach(supervision => {
      supervision.sessions = sortSessions(supervision.sessions);
      
      // Filter sessions theo status nếu có
      if (status) {
        supervision.sessions = supervision.sessions.filter(session => session.status === status);
      }
      
      supervision.sessions = supervision.sessions.map((session, index) => ({
        ...session.toObject(),
        sessionNumber: index + 1
      }));
    });

    // Loại bỏ các supervisions không có sessions nào sau khi filter
    const filteredSupervisions = supervisions.filter(supervision => supervision.sessions.length > 0);

    res.json(filteredSupervisions);
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ msg: 'Lỗi server' });
  }
};

// @desc    Tạo phân công coi thi mới
exports.createExamSupervision = async (req, res) => {
  try {
    const { examType, schoolYear, teacher, sessions, notes } = req.body;
    console.log('Request body:', req.body);

    // Kiểm tra giáo viên tồn tại
    const teacherExists = await User.findById(teacher);
    console.log('Teacher exists:', teacherExists);

    if (!teacherExists || !teacherExists.role.includes(ROLES.TEACHER)) {
      return res.status(404).json({ msg: 'Giáo viên không tồn tại' });
    }

    // Kiểm tra trùng lặp phân công
    const existingSupervision = await ExamSupervision.findOne({
      examType,
      schoolYear,
      teacher,
    });
    console.log('Existing supervision:', existingSupervision);

    if (existingSupervision) {
      return res.status(400).json({ msg: 'Giáo viên đã được phân công cho kỳ thi này' });
    }

    // Sắp xếp sessions theo thời gian
    const sortedSessions = sortSessions(sessions);
    console.log('Sorted sessions:', sortedSessions);

    const supervision = new ExamSupervision({
      examType,
      schoolYear,
      teacher,
      sessions: sortedSessions,
      notes,
      createdBy: req.user.id,
    });

    await supervision.save();
    console.log('Saved supervision:', supervision);

    await supervision.populate([
      { path: 'teacher', select: 'name email' },
      { path: 'createdBy', select: 'name' },
    ]);

    res.status(201).json(supervision);
  } catch (err) {
    console.error('Error in createExamSupervision:', err);
    res.status(500).json({ msg: 'Lỗi server', error: err.message });
  }
};

// @desc    Cập nhật phân công coi thi
// @route   PUT /api/exam-supervisions/:id
// @access  Private (Admin only)
exports.updateExamSupervision = async (req, res) => {
  try {
    const { sessions, notes } = req.body;

    const supervision = await ExamSupervision.findById(req.params.id);
    if (!supervision) {
      return res.status(404).json({ msg: 'Không tìm thấy phân công coi thi' });
    }

    // Cập nhật notes nếu có
    if (notes !== undefined) {
      supervision.notes = notes;
    }

    // Cập nhật sessions nếu có
    if (sessions && Array.isArray(sessions)) {
      const updatedSessions = [];

      for (const session of sessions) {
        if (session._id) {
          // Cập nhật session cũ
          const existingSession = supervision.sessions.id(session._id);
          if (!existingSession) {
            return res.status(404).json({ msg: `Không tìm thấy session với ID: ${session._id}` });
          }

          // Cập nhật các trường được phép
          if (session.date) existingSession.date = session.date;
          if (session.timeSlot) {
            if (!['morning', 'afternoon'].includes(session.timeSlot)) {
              return res.status(400).json({ msg: 'Ca thi không hợp lệ' });
            }
            existingSession.timeSlot = session.timeSlot;
          }
          if (session.room !== undefined) existingSession.room = session.room;
          if (session.subject !== undefined) existingSession.subject = session.subject;
          if (session.status) {
            if (!['DA_PHAN_CONG', 'DA_HOAN_THANH', 'YEU_CAU_DOI_BUOI'].includes(session.status)) {
              return res.status(400).json({ msg: 'Trạng thái không hợp lệ' });
            }
            existingSession.status = session.status;
          }
          updatedSessions.push(existingSession);
        } else {
          // Thêm session mới
          if (!session.date || !session.timeSlot) {
            return res.status(400).json({ msg: 'Session mới phải có date và timeSlot' });
          }
          if (!['morning', 'afternoon'].includes(session.timeSlot)) {
            return res.status(400).json({ msg: 'Ca thi không hợp lệ' });
          }
          updatedSessions.push({
            date: session.date,
            timeSlot: session.timeSlot,
            room: session.room || '',
            subject: session.subject || '',
            status: 'DA_PHAN_CONG'
          });
        }
      }

      // Cập nhật lại toàn bộ sessions
      supervision.sessions = updatedSessions;
      // Sắp xếp lại sessions
      supervision.sessions = sortSessions(supervision.sessions);
    }

    await supervision.save();
    await supervision.populate([
      { path: 'teacher', select: 'name email department' },
      { path: 'createdBy', select: 'name' },
    ]);

    // Thêm sessionNumber cho response
    supervision.sessions = supervision.sessions.map((session, index) => ({
      ...session.toObject(),
      sessionNumber: index + 1
    }));

    res.json(supervision);
  } catch (err) {
    console.error('Error in updateExamSupervision:', err);
    res.status(500).json({ msg: 'Lỗi server', error: err.message });
  }
};

// @desc    Xóa phân công coi thi
exports.deleteExamSupervision = async (req, res) => {
  try {
    const supervision = await ExamSupervision.findById(req.params.id);
    if (!supervision) {
      return res.status(404).json({ msg: 'Không tìm thấy phân công coi thi' });
    }

    // Kiểm tra có yêu cầu đổi buổi pending không
    const ExamChangeRequest = require('../models/ExamChangeRequest');
    const pendingRequests = await ExamChangeRequest.findOne({
      supervision: req.params.id,
      status: 'PENDING'
    });

    if (pendingRequests) {
      return res.status(400).json({ msg: 'Không thể xóa phân công có yêu cầu đổi buổi đang chờ xử lý' });
    }

    await supervision.deleteOne();
    res.json({ msg: 'Đã xóa phân công coi thi' });
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ msg: 'Lỗi server' });
  }
};

// @desc    Lấy chi tiết phân công coi thi
// @route   GET /api/exam-supervisions/:id
// @access  Private (Admin, Teacher)
exports.getExamSupervisionById = async (req, res) => {
  try {
    const supervision = await ExamSupervision.findById(req.params.id)
      .populate('teacher', 'name email department')
      .populate('createdBy', 'name');

    if (!supervision) {
      return res.status(404).json({ msg: 'Không tìm thấy phân công coi thi' });
    }

    // Kiểm tra quyền truy cập
    // Admin có thể xem tất cả, giáo viên chỉ xem được phân công của mình
    if (!req.user.role.includes(ROLES.ADMIN) &&
      supervision.teacher._id.toString() !== req.user.id) {
      return res.status(403).json({ msg: 'Không có quyền truy cập phân công này' });
    }

    // Sắp xếp sessions và thêm sessionNumber
    supervision.sessions = sortSessions(supervision.sessions);
    supervision.sessions = supervision.sessions.map((session, index) => ({
      ...session.toObject(),
      sessionNumber: index + 1
    }));

    res.json(supervision);
  } catch (err) {
    console.error('Error in getExamSupervisionById:', err);
    res.status(500).json({ msg: 'Lỗi server', error: err.message });
  }
}; 