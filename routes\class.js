// src/routes/class.js
const express = require('express');
const router = express.Router();
const Class = require('../models/Class');
const { protect, authorize } = require('../middlewares/auth');

router.post('/', protect, async (req, res) => {
  const { name, schoolYear } = req.body;
  if (!req.user.role.includes('admin')) {
    return res.status(403).json({ msg: 'Chỉ admin mới tạo lớp' });
  }
  try {
    let classObj = await Class.findOne({ name, schoolYear });
    if (classObj) {
      return res.status(400).json({ msg: 'Lớp đã tồn tại' });
    }
    classObj = new Class({ name, schoolYear });
    await classObj.save();
    res.json(classObj);
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ msg: 'Lỗi máy chủ' });
  }
});

module.exports = router;