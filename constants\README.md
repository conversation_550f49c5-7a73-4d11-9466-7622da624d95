# Constants Documentation

## Overview
This directory contains all centralized constants used throughout the School Management System. The refactoring eliminates hardcoded values and provides a consistent, maintainable approach to managing application constants.

## Files Structure

### Core Constants
- **`messages.js`** - All user-facing messages and error messages
- **`httpConstants.js`** - HTTP status codes and response helpers
- **`validationConstants.js`** - Validation patterns and helper functions
- **`roleConstants.js`** - User roles definitions

### Domain-Specific Constants
- **`announcementConstants.js`** - Announcement types, statuses, and labels
- **`userConstants.js`** - User-related constants (gender, specific roles, etc.)
- **`attendanceConstants.js`** - Attendance-related constants
- **`departments.js`** - Department and subject groupings

### Utility
- **`index.js`** - Centralized export of all constants
- **`api.js`** - API-related constants

## Usage Examples

### Before Refactoring (Hardcoded)
```javascript
// ❌ Bad - Hardcoded values
return res.status(400).json({ 
  success: false, 
  msg: 'Dữ liệu không hợp lệ' 
});

if (!user) {
  return res.status(404).json({ msg: 'Không tìm thấy người dùng' });
}
```

### After Refactoring (Constants)
```javascript
// ✅ Good - Using constants
const { HTTP_STATUS, createErrorResponse, MESSAGES } = require('../constants');

return res.status(HTTP_STATUS.BAD_REQUEST).json(
  createErrorResponse(MESSAGES.ERROR.VALIDATION_FAILED)
);

if (!user) {
  return res.status(HTTP_STATUS.NOT_FOUND).json(
    createErrorResponse(MESSAGES.USER.NOT_FOUND)
  );
}
```

## Key Benefits

### 1. Consistency
- Standardized HTTP status codes across all controllers
- Uniform response structure using helper functions
- Consistent error messages

### 2. Maintainability
- Single source of truth for all constants
- Easy to update messages or status codes
- Centralized validation logic

### 3. Type Safety & IntelliSense
- Better IDE support with constant definitions
- Reduced typos and errors
- Clear documentation of available options

### 4. Internationalization Ready
- All messages centralized for easy translation
- Structured approach for multi-language support

## Response Helpers

### `createSuccessResponse(data, message)`
```javascript
// Usage
res.json(createSuccessResponse(userData, 'User created successfully'));

// Output
{
  "success": true,
  "message": "User created successfully",
  "data": { /* userData */ }
}
```

### `createErrorResponse(message, error)`
```javascript
// Usage
res.status(HTTP_STATUS.BAD_REQUEST).json(
  createErrorResponse(MESSAGES.ERROR.VALIDATION_FAILED, validationErrors)
);

// Output
{
  "success": false,
  "msg": "Dữ liệu không hợp lệ",
  "error": { /* validationErrors */ }
}
```

### `createPaginatedResponse(data, count, page, limit)`
```javascript
// Usage
res.json(createPaginatedResponse(users, totalCount, 1, 10));

// Output
{
  "success": true,
  "count": 50,
  "page": 1,
  "limit": 10,
  "totalPages": 5,
  "data": [ /* users */ ]
}
```

## Validation Helpers

### `validateRequiredFields(data, requiredFields)`
```javascript
const { validateRequiredFields, VALIDATION_RULES } = require('../constants');

const validation = validateRequiredFields(req.body, VALIDATION_RULES.REQUIRED_FIELDS.USER_REGISTER);
if (!validation.isValid) {
  return res.status(HTTP_STATUS.BAD_REQUEST).json(
    createErrorResponse(validation.message)
  );
}
```

## Migration Guide

### Controllers Updated
- ✅ `zaloController.js` - Fully refactored
- ✅ `authController.js` - Fully refactored  
- ✅ `announcementController.js` - Fully refactored
- 🔄 Other controllers - Pending refactoring

### Import Pattern
```javascript
// Old way - Multiple imports
const MESSAGES = require('../constants/messages');
const ROLES = require('../constants/roleConstants');
const { HTTP_STATUS } = require('../constants/httpConstants');

// New way - Single import
const { MESSAGES, ROLES, HTTP_STATUS, createSuccessResponse } = require('../constants');
```

## Constants Categories

### HTTP Status Codes
- `HTTP_STATUS.OK` (200)
- `HTTP_STATUS.CREATED` (201)
- `HTTP_STATUS.BAD_REQUEST` (400)
- `HTTP_STATUS.UNAUTHORIZED` (401)
- `HTTP_STATUS.FORBIDDEN` (403)
- `HTTP_STATUS.NOT_FOUND` (404)
- `HTTP_STATUS.CONFLICT` (409)
- `HTTP_STATUS.INTERNAL_SERVER_ERROR` (500)

### Common Messages
- `MESSAGES.ERROR.*` - General error messages
- `MESSAGES.USER.*` - User-related messages
- `MESSAGES.ANNOUNCEMENT.*` - Announcement messages
- `MESSAGES.ZALO.*` - Zalo integration messages

### Validation Patterns
- `VALIDATION_PATTERNS.PHONE_NUMBER` - Phone validation regex
- `VALIDATION_PATTERNS.STUDENT_ID` - Student ID validation
- `VALIDATION_PATTERNS.OBJECT_ID` - MongoDB ObjectId validation

## Future Improvements

1. **Complete Migration** - Refactor remaining controllers
2. **Internationalization** - Add multi-language support
3. **Type Definitions** - Add TypeScript definitions
4. **Validation Middleware** - Create reusable validation middleware
5. **Response Middleware** - Standardize all API responses

## Contributing

When adding new constants:
1. Add to appropriate category file
2. Update `index.js` exports
3. Update this documentation
4. Follow existing naming conventions
5. Add usage examples 