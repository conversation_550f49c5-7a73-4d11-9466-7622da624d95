const ExamChangeRequest = require('../models/ExamChangeRequest');
const ExamSupervision = require('../models/ExamSupervision');
const { calculateSessionNumber, checkReportDeadline } = require('../utils/dateHelpers');

// @desc    Tạo yêu cầu đổi buổi coi thi
exports.createChangeRequest = async (req, res) => {
  try {
    const { supervisionId, sessionId, reason, proposedNewDate, proposedNewTimeSlot } = req.body;

    // Kiểm tra phân công tồn tại
    const supervision = await ExamSupervision.findById(supervisionId);
    if (!supervision) {
      return res.status(404).json({ msg: 'Không tìm thấy phân công coi thi' });
    }

    // Kiểm tra giáo viên có quyền yêu cầu đổi buổi này không
    if (supervision.teacher.toString() !== req.user.id) {
      return res.status(403).json({ msg: '<PERSON>hông có quyền thực hiện hành động này' });
    }

    // Tìm session bằng sessionId
    const session = supervision.sessions.id(sessionId);
    if (!session) {
      return res.status(404).json({ msg: 'Không tìm thấy buổi coi thi' });
    }

    // Kiểm tra session chưa hoàn thành
    if (session.status === 'DA_HOAN_THANH') {
      return res.status(400).json({ msg: 'Không thể đổi buổi đã hoàn thành' });
    }

    // Kiểm tra thời hạn báo cáo (2 ngày trước)
    if (!checkReportDeadline(session.date)) {
      return res.status(400).json({ 
        msg: 'Phải báo cáo trước ít nhất 2 ngày so với ngày được phân công' 
      });
    }

    // Kiểm tra đã có yêu cầu pending cho session này chưa
    const existingRequest = await ExamChangeRequest.findOne({
      supervision: supervisionId,
      sessionId,
      status: 'PENDING',
    });

    if (existingRequest) {
      return res.status(400).json({ msg: 'Đã có yêu cầu đổi buổi đang chờ xử lý' });
    }

    const changeRequest = new ExamChangeRequest({
      supervision: supervisionId,
      sessionId,
      teacher: req.user.id,
      reason,
      originalDate: session.date,
      originalTimeSlot: session.timeSlot,
      proposedNewDate,
      proposedNewTimeSlot,
    });

    await changeRequest.save();
    await changeRequest.populate([
      { path: 'supervision', select: 'examType schoolYear' },
      { path: 'teacher', select: 'name email' },
    ]);

    // Cập nhật trạng thái session
    session.status = 'YEU_CAU_DOI_BUOI';
    await supervision.save();

    // Thêm sessionNumber để frontend hiển thị
    const sessionNumber = calculateSessionNumber(supervision.sessions, sessionId);

    res.status(201).json({
      success: true,
      changeRequest: {
        ...changeRequest.toObject(),
        sessionNumber
      },
      updatedSession: {
        _id: sessionId,
        date: session.date,
        timeSlot: session.timeSlot,
        room: session.room,
        subject: session.subject,
        status: session.status
      }
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ msg: 'Lỗi server' });
  }
};

// @desc    Lấy danh sách yêu cầu đổi buổi (Admin)
exports.getChangeRequests = async (req, res) => {
  try {
    const { status, schoolYear, examType } = req.query;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;
    
    let query = {};
    if (status) query.status = status;

    const changeRequests = await ExamChangeRequest.find(query)
      .populate([
        { 
          path: 'supervision', 
          select: 'examType schoolYear',
          match: {
            ...(schoolYear && { schoolYear }),
            ...(examType && { examType })
          }
        },
        { path: 'teacher', select: 'name email' },
        { path: 'approvedBy', select: 'name' }
      ])
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);

    // Lọc bỏ những request có supervision null (do populate match)
    const filteredRequests = changeRequests.filter(req => req.supervision);

    // Thêm sessionNumber cho mỗi request
    for (let request of filteredRequests) {
      // Kiểm tra supervision tồn tại trước khi truy cập _id
      if (request.supervision && request.supervision._id) {
        const supervision = await ExamSupervision.findById(request.supervision._id);
        if (supervision) {
          const sessionNumber = calculateSessionNumber(supervision.sessions, request.sessionId);
          request.sessionNumber = sessionNumber;
        } else {
          request.sessionNumber = null;
        }
      } else {
        request.sessionNumber = null;
      }
    }

    const total = await ExamChangeRequest.countDocuments(query);

    res.json({
      changeRequests: filteredRequests,
      pagination: {
        current: page,
        pages: Math.ceil(total / limit),
        total
      }
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ msg: 'Lỗi server' });
  }
};

// @desc    Lấy yêu cầu đổi buổi của giáo viên hiện tại
exports.getMyChangeRequests = async (req, res) => {
  try {
    const { status, startDate, endDate } = req.query;
    
    const query = { teacher: req.user.id };
    if (status) query.status = status;

    // Lọc theo khoảng thời gian createdAt
    if (startDate || endDate) {
      query.createdAt = {};
      
      if (startDate) {
        const fromDate = new Date(startDate);
        fromDate.setHours(0, 0, 0, 0); // Đặt về đầu ngày
        query.createdAt.$gte = fromDate;
      }
      
      if (endDate) {
        const toDate = new Date(endDate);
        toDate.setHours(23, 59, 59, 999); // Đặt về cuối ngày
        query.createdAt.$lte = toDate;
      }
    }

    const changeRequests = await ExamChangeRequest.find(query)
      .populate([
        { path: 'supervision', select: 'examType schoolYear' },
        { path: 'approvedBy', select: 'name' }
      ])
      .sort({ createdAt: -1 });

    // Lọc bỏ những request có supervision null
    const validRequests = changeRequests.filter(req => req.supervision);

    // Thêm sessionNumber cho mỗi request
    for (let request of validRequests) {
      // Kiểm tra supervision tồn tại trước khi truy cập _id
      if (request.supervision && request.supervision._id) {
        const supervision = await ExamSupervision.findById(request.supervision._id);
        if (supervision) {
          const sessionNumber = calculateSessionNumber(supervision.sessions, request.sessionId);
          request.sessionNumber = sessionNumber;
        } else {
          request.sessionNumber = null;
        }
      } else {
        request.sessionNumber = null;
      }
    }

    res.json(validRequests);
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ msg: 'Lỗi server' });
  }
};

// @desc    Phê duyệt/Từ chối yêu cầu đổi buổi
exports.approveChangeRequest = async (req, res) => {
  try {
    const { status, approvalNotes, newDate, newTimeSlot } = req.body;

    const changeRequest = await ExamChangeRequest.findById(req.params.id);
    if (!changeRequest) {
      return res.status(404).json({ msg: 'Không tìm thấy yêu cầu đổi buổi' });
    }

    if (changeRequest.status !== 'PENDING') {
      return res.status(400).json({ msg: 'Yêu cầu đã được xử lý' });
    }

    changeRequest.status = status;
    changeRequest.approvedBy = req.user.id;
    changeRequest.approvalDate = new Date();
    changeRequest.approvalNotes = approvalNotes;

    const supervision = await ExamSupervision.findById(changeRequest.supervision);
    const session = supervision.sessions.id(changeRequest.sessionId);

    if (status === 'APPROVED') {
      // Cập nhật ngày và ca mới nếu có
      if (newDate) session.date = newDate;
      if (newTimeSlot) session.timeSlot = newTimeSlot;
      session.status = 'DA_PHAN_CONG';
    } else if (status === 'REJECTED') {
      // Khôi phục trạng thái session
      session.status = 'DA_PHAN_CONG';
    }

    await supervision.save();
    await changeRequest.save();
    
    await changeRequest.populate([
      { path: 'supervision', select: 'examType schoolYear' },
      { path: 'teacher', select: 'name email' },
      { path: 'approvedBy', select: 'name' },
    ]);

    // Thêm sessionNumber
    const sessionNumber = calculateSessionNumber(supervision.sessions, changeRequest.sessionId);

    res.json({
      ...changeRequest.toObject(),
      sessionNumber
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ msg: 'Lỗi server' });
  }
}; 