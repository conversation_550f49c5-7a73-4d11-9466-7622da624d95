// controllers/leaveRequestController.js
const LeaveRequest = require('../models/LeaveRequest');
const User = require('../models/User');
const Class = require('../models/Class');
const Attendance = require('../models/Attendance');
const asyncHandler = require('../middlewares/async');
const MESSAGES = require('../constants/messages');
const ROLES = require('../constants/roleConstants');

// Hàm tạo bản ghi điểm danh với trạng thái "excused"
async function createExcusedAttendance(studentId, classId, date, session, leaveRequestId, creatorId) {
  // Lấy ngày từ đối tượng Date (chỉ lấy ngày, tháng, năm)
  const dateString = date.toISOString().split('T')[0];

  // Tìm tất cả các bản ghi điểm danh trong ngày
  const allAttendances = await Attendance.find({
    student: studentId,
    class: classId,
    session
  });

  // Lọ<PERSON> ra bản ghi điểm danh trùng với ngày xin phép
  let existingAttendance = null;

  for (const attendance of allAttendances) {
    const attendanceDate = attendance.date.toISOString().split('T')[0];
    if (attendanceDate === dateString) {
      existingAttendance = attendance;
      break;
    }
  }

  if (existingAttendance) {
    // Nếu đã có bản ghi, cập nhật trạng thái
    existingAttendance.status = 'excused';
    existingAttendance.leaveRequest = leaveRequestId;
    existingAttendance.notes = 'Vắng mặt có phép - Đơn xin phép nghỉ học được phê duyệt';
    existingAttendance.updatedBy = creatorId;
    await existingAttendance.save();
  } else {
    // Nếu chưa có bản ghi, tạo mới
    await Attendance.create({
      student: studentId,
      class: classId,
      date,
      session,
      status: 'excused',
      location: null, // Không có vị trí vì học sinh vắng mặt có phép
      leaveRequest: leaveRequestId,
      notes: 'Vắng mặt có phép - Đơn xin phép nghỉ học được phê duyệt',
      createdBy: creatorId
    });
  }
}

// Hàm tạo bản ghi điểm danh với trạng thái "absent"
async function createAbsentAttendance(studentId, classId, date, session, creatorId, leaveRequestId) {
  console.log(`createAbsentAttendance - Bắt đầu xử lý: studentId=${studentId}, classId=${classId}, date=${date}, session=${session}`);

  try {
    // Lấy ngày từ đối tượng Date (chỉ lấy ngày, tháng, năm)
    const dateString = date.toISOString().split('T')[0];
    console.log(`createAbsentAttendance - Ngày xử lý: ${dateString}`);

    // Tìm tất cả các bản ghi điểm danh trong ngày
    const allAttendances = await Attendance.find({
      student: studentId,
      class: classId,
      session
    });

    console.log(`createAbsentAttendance - Tìm thấy ${allAttendances.length} bản ghi điểm danh cho buổi ${session}`);

    // Lọc ra bản ghi điểm danh trùng với ngày xin phép
    let existingAttendance = null;

    for (const attendance of allAttendances) {
      const attendanceDate = attendance.date.toISOString().split('T')[0];
      console.log(`createAbsentAttendance - So sánh ngày: ${attendanceDate} vs ${dateString}`);

      if (attendanceDate === dateString) {
        existingAttendance = attendance;
        console.log(`createAbsentAttendance - Tìm thấy bản ghi điểm danh trùng ngày: ${attendance._id}`);
        break;
      }
    }

    if (existingAttendance) {
      // Nếu đã có bản ghi, cập nhật trạng thái
      console.log(`createAbsentAttendance - Cập nhật bản ghi điểm danh ${existingAttendance._id} từ '${existingAttendance.status}' thành 'absent'`);

      existingAttendance.status = 'absent';
      existingAttendance.notes = 'Vắng mặt không phép - Đơn xin phép đã bị từ chối';
      existingAttendance.updatedBy = creatorId;
      await existingAttendance.save();

      console.log(`createAbsentAttendance - Đã cập nhật bản ghi điểm danh ${existingAttendance._id} thành 'absent'`);
      return existingAttendance;
    } else {
      // Nếu chưa có bản ghi, tạo mới
      console.log(`createAbsentAttendance - Tạo bản ghi điểm danh mới với trạng thái 'absent'`);

      const newAttendance = await Attendance.create({
        student: studentId,
        class: classId,
        date,
        session,
        status: 'absent',
        location: null, // Không có vị trí vì học sinh vắng mặt
        notes: 'Vắng mặt không phép - Đơn xin phép đã bị từ chối',
        createdBy: creatorId
      });

      console.log(`createAbsentAttendance - Đã tạo bản ghi điểm danh mới với ID ${newAttendance._id} và trạng thái 'absent'`);
      return newAttendance;
    }
  } catch (error) {
    console.error(`createAbsentAttendance - Lỗi: ${error.message}`);
    throw error;
  }
}

// @desc    Tạo yêu cầu xin phép nghỉ học mới
// @route   POST /api/leave-requests
// @access  Private (học sinh, giáo viên, admin)
exports.createLeaveRequest = asyncHandler(async (req, res) => {
  const {
    studentId,
    classId,
    startDate,
    endDate,
    sessions,
    reason,
    attachments,
    requestType
  } = req.body;

  let targetUserId;
  let targetRequestType;

  // Xác định loại yêu cầu và người được xin phép
  if (req.user.role.includes('student')) {
    // Học sinh tự xin phép
    targetUserId = req.user.id;
    targetRequestType = 'student';
    
    // Kiểm tra xem có studentId không (trường hợp admin tạo cho học sinh)
    if (studentId && studentId !== req.user.id) {
      return res.status(403).json({
        success: false,
        msg: MESSAGES.ERROR.FORBIDDEN
      });
    }
  } else if (req.user.role.includes('teacher')) {
    // Giáo viên có thể tự xin phép hoặc xin phép cho học sinh
    if (requestType === 'teacher' || (!studentId && !requestType)) {
      // Giáo viên tự xin phép (mặc định nếu không có studentId)
      targetUserId = req.user.id;
      targetRequestType = 'teacher';
    } else {
      // Giáo viên xin phép cho học sinh
      if (!studentId) {
        return res.status(400).json({
          success: false,
          msg: 'Thiếu thông tin học sinh khi tạo đơn xin phép cho học sinh'
        });
      }
      targetUserId = studentId;
      targetRequestType = 'student';
    }
  } else if (req.user.role.includes('admin')) {
    // Admin có thể tạo cho bất kỳ ai
    if (requestType === 'teacher') {
      targetUserId = studentId || req.user.id; // Sử dụng studentId làm teacherId hoặc tự tạo cho admin
      targetRequestType = 'teacher';
    } else {
      if (!studentId) {
        return res.status(400).json({
          success: false,
          msg: 'Thiếu thông tin người dùng khi tạo đơn xin phép'
        });
      }
      targetUserId = studentId;
      targetRequestType = 'student';
    }
  } else {
    return res.status(403).json({
      success: false,
      msg: MESSAGES.ERROR.FORBIDDEN
    });
  }

  // Kiểm tra dữ liệu đầu vào cơ bản
  if (!startDate || !endDate || !sessions || !reason || reason.trim() === '') {
    return res.status(400).json({
      success: false,
      msg: 'Thiếu thông tin bắt buộc: ngày bắt đầu, ngày kết thúc, buổi nghỉ, lý do'
    });
  }

  // Đối với học sinh, classId là bắt buộc
  if (targetRequestType === 'student' && !classId) {
    return res.status(400).json({
      success: false,
      msg: 'Lớp học là bắt buộc đối với đơn xin phép của học sinh'
    });
  }

  // Đối với giáo viên, classId không cần thiết
  if (targetRequestType === 'teacher' && classId) {
    console.warn('ClassId được cung cấp cho đơn xin phép của giáo viên nhưng sẽ bị bỏ qua');
  }

  // Kiểm tra sessions có hợp lệ không
  const validSessions = ['morning', 'afternoon', 'all-day'];

  // Chuyển đổi sessions thành mảng nếu nó là chuỗi
  let sessionsArray = sessions;
  if (typeof sessions === 'string') {
    sessionsArray = [sessions];
  }

  if (!Array.isArray(sessionsArray) || sessionsArray.length === 0 || !sessionsArray.every(session => validSessions.includes(session))) {
    return res.status(400).json({
      success: false,
      msg: 'Buổi nghỉ không hợp lệ. Phải là: morning, afternoon, hoặc all-day'
    });
  }

  // Kiểm tra startDate <= endDate
  const startDateCheck = new Date(startDate);
  const endDateCheck = new Date(endDate);

  if (startDateCheck > endDateCheck) {
    return res.status(400).json({
      success: false,
      msg: 'Ngày bắt đầu phải nhỏ hơn hoặc bằng ngày kết thúc'
    });
  }

  // Kiểm tra người dùng có tồn tại không
  const targetUser = await User.findById(targetUserId);
  if (!targetUser) {
    return res.status(404).json({
      success: false,
      msg: targetRequestType === 'student' ? MESSAGES.STUDENT.NOT_FOUND : 'Không tìm thấy giáo viên'
    });
  }

  // Kiểm tra role phù hợp
  if (targetRequestType === 'student' && !targetUser.role.includes('student')) {
    return res.status(400).json({
      success: false,
      msg: 'Người dùng không phải là học sinh'
    });
  }

  if (targetRequestType === 'teacher' && !targetUser.role.includes('teacher') && !targetUser.role.includes('admin')) {
    return res.status(400).json({
      success: false,
      msg: 'Người dùng không phải là giáo viên'
    });
  }

  // === XỬ LÝ RIÊNG CHO TỪNG LOẠI YÊU CẦU ===
  
  let classObj = null;
  
  if (targetRequestType === 'student') {
    // Logic xử lý cho học sinh
    classObj = await Class.findById(classId);
    if (!classObj) {
      return res.status(404).json({
        success: false,
        msg: MESSAGES.CLASS.NOT_FOUND
      });
    }

    // Nếu là học sinh tự xin phép, kiểm tra học sinh có thuộc lớp không
    if (req.user.role.includes('student') && targetUser.class && targetUser.class.toString() !== classId) {
      return res.status(403).json({
        success: false,
        msg: 'Bạn chỉ có thể tạo đơn xin phép cho lớp của mình'
      });
    }

    // Kiểm tra xung đột điểm danh (chỉ áp dụng cho học sinh)
    const startDateObj = new Date(startDate);
    const endDateObj = new Date(endDate);

    const requestDates = [];
    let currentDate = new Date(startDateObj);

    while (currentDate <= endDateObj) {
      requestDates.push(currentDate.toISOString().split('T')[0]);
      currentDate.setDate(currentDate.getDate() + 1);
    }

    // Kiểm tra xem đã có đơn xin phép nào cho cùng thời gian không
    const existingLeaveRequests = await LeaveRequest.find({
      requester: targetUserId,
      class: classId,
      status: { $in: ['pending', 'approved'] },
      $or: [
        // Kiểm tra nếu có đơn xin phép nào có khoảng thời gian giao với đơn mới
        {
          startDate: { $lte: endDateObj },
          endDate: { $gte: startDateObj }
        }
      ]
    });

    if (existingLeaveRequests.length > 0) {
      // Kiểm tra chi tiết từng buổi
      const conflictingLeaveRequests = [];

      for (const existingRequest of existingLeaveRequests) {
        const existingDates = [];
        let existingDate = new Date(existingRequest.startDate);
        
        while (existingDate <= existingRequest.endDate) {
          const dateStr = existingDate.toISOString().split('T')[0];
          if (requestDates.includes(dateStr)) {
            // Kiểm tra xung đột buổi học
            const hasConflict = existingRequest.sessions.some(existingSession => {
              if (existingSession === 'all-day') {
                return true; // Xung đột với tất cả các buổi
              }
              return sessionsArray.includes('all-day') || sessionsArray.includes(existingSession);
            });

            if (hasConflict) {
              conflictingLeaveRequests.push({
                date: dateStr,
                sessions: existingRequest.sessions.map(s => s === 'morning' ? 'Sáng' : s === 'afternoon' ? 'Chiều' : 'Cả ngày'),
                status: existingRequest.status
              });
            }
          }
          existingDate.setDate(existingDate.getDate() + 1);
        }
      }

      if (conflictingLeaveRequests.length > 0) {
        return res.status(400).json({
          success: false,
          msg: 'Đã có đơn xin phép cho các buổi này',
          conflictingLeaveRequests
        });
      }
    }

    const allAttendances = await Attendance.find({
      student: targetUserId,
      class: classId,
      status: { $in: ['present', 'late'] }
    });

    const conflictingSessions = [];

    for (const attendance of allAttendances) {
      const attendanceDate = attendance.date.toISOString().split('T')[0];
      const attendanceSession = attendance.session;

      if (requestDates.includes(attendanceDate)) {
        if (sessionsArray.includes('all-day') || sessionsArray.includes(attendanceSession)) {
          conflictingSessions.push({
            date: attendanceDate,
            session: attendanceSession === 'morning' ? 'Sáng' : 'Chiều',
            status: attendance.status
          });
        }
      }
    }

    if (conflictingSessions.length > 0) {
      return res.status(400).json({
        success: false,
        msg: 'Không thể xin phép cho các buổi đã điểm danh',
        conflictingSessions
      });
    }
  } else if (targetRequestType === 'teacher') {
    // Logic xử lý cho giáo viên (không cần kiểm tra lớp học hay điểm danh)
    console.log(`Tạo đơn xin phép cho giáo viên: ${targetUser.name} từ ${startDate} đến ${endDate}`);
  }

  // Tạo yêu cầu xin phép mới
  const leaveRequestData = {
    requestType: targetRequestType,
    requester: targetUserId,
    startDate,
    endDate,
    sessions: sessionsArray,
    reason,
    status: 'pending',
    createdBy: req.user.id
  };

  // Chỉ thêm class cho học sinh
  if (targetRequestType === 'student') {
    leaveRequestData.class = classId;
    leaveRequestData.student = targetUserId; // Để tương thích với code cũ
  }

  const leaveRequest = new LeaveRequest(leaveRequestData);

  // Lưu thông tin hình ảnh đính kèm nếu có
  if (attachments && Array.isArray(attachments) && attachments.length > 0) {
    leaveRequest.attachments = attachments.map(attachment => ({
      data: attachment.data,
      width: attachment.width,
      height: attachment.height
    }));
  }

  await leaveRequest.save();

  // Populate thông tin để trả về đầy đủ
  await leaveRequest.populate('requester', 'name role specificRole');
  if (targetRequestType === 'student') {
    await leaveRequest.populate('class', 'name');
  }
  await leaveRequest.populate('createdBy', 'name');

  res.status(201).json({
    success: true,
    msg: `Tạo đơn xin phép ${targetRequestType === 'student' ? 'học sinh' : 'giáo viên'} thành công`,
    data: leaveRequest
  });
});

// @desc    Lấy danh sách yêu cầu xin phép nghỉ học của một lớp
// @route   GET /api/leave-requests/class/:classId
// @access  Private (giáo viên chủ nhiệm của lớp đó, admin)
exports.getClassLeaveRequests = asyncHandler(async (req, res) => {
  const { classId } = req.params;
  const { status, startDate, endDate, requestType, page = 1, limit = 10 } = req.query;

  // Kiểm tra quyền truy cập
  if (!req.user.role.includes(ROLES.TEACHER) && !req.user.role.includes(ROLES.ADMIN)) {
    return res.status(403).json({
      success: false,
      msg: MESSAGES.ERROR.FORBIDDEN
    });
  }

  // Kiểm tra lớp có tồn tại không
  const classObj = await Class.findById(classId);
  if (!classObj) {
    return res.status(404).json({
      success: false,
      msg: MESSAGES.CLASS.NOT_FOUND
    });
  }

  // Nếu là giáo viên, kiểm tra có phải là chủ nhiệm của lớp này không
  if (req.user.role.includes(ROLES.TEACHER) && !req.user.role.includes(ROLES.ADMIN)) {
    if (!classObj.homeroomTeacher || classObj.homeroomTeacher.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        msg: 'Bạn chỉ có thể xem đơn xin phép của lớp mình làm chủ nhiệm'
      });
    }
  }

  const query = { class: classId };

  // Lọc theo loại yêu cầu nếu có
  if (requestType && ['student', 'teacher'].includes(requestType)) {
    query.requestType = requestType;
  } else {
    // Mặc định chỉ lấy đơn của học sinh vì teacher không có class
    query.requestType = 'student';
  }

  // Lọc theo trạng thái nếu có
  if (status && ['pending', 'approved', 'rejected'].includes(status)) {
    query.status = status;
  }

  // Lọc theo khoảng thời gian nếu có - Logic đúng: tìm records có khoảng thời gian giao với filter
  if (startDate || endDate) {
    const dateFilter = {};

    if (startDate && endDate) {
      // Có cả startDate và endDate: tìm records có khoảng thời gian giao với [startDate, endDate]
      const start = new Date(startDate);
      start.setHours(0, 0, 0, 0);
      const end = new Date(endDate);
      end.setHours(23, 59, 59, 999);

      query.$and = [
        { startDate: { $lte: end } },   // Record bắt đầu trước hoặc trong khoảng filter
        { endDate: { $gte: start } }    // Record kết thúc sau hoặc trong khoảng filter
      ];
    } else if (startDate) {
      // Chỉ có startDate: tìm records kết thúc sau startDate
      const start = new Date(startDate);
      start.setHours(0, 0, 0, 0);
      query.endDate = { $gte: start };
    } else if (endDate) {
      // Chỉ có endDate: tìm records bắt đầu trước endDate
      const end = new Date(endDate);
      end.setHours(23, 59, 59, 999);
      query.startDate = { $lte: end };
    }
  }

  // Tính toán pagination
  const pageNumber = parseInt(page);
  const limitNumber = parseInt(limit);
  const skip = (pageNumber - 1) * limitNumber;

  // Lấy tổng số records để tính totalPages
  const totalLeaveRequests = await LeaveRequest.countDocuments(query);
  const totalPages = Math.ceil(totalLeaveRequests / limitNumber);

  const leaveRequests = await LeaveRequest.find(query)
    .populate('requester', 'name studentId role specificRole')
    .populate('student', 'name studentId') // Để tương thích với code cũ
    .populate('class', 'name')
    .populate('createdBy', 'name')
    .populate('approvedBy', 'name')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limitNumber);

  res.json({
    success: true,
    count: leaveRequests.length,
    totalCount: totalLeaveRequests,
    currentPage: pageNumber,
    totalPages,
    hasNextPage: pageNumber < totalPages,
    hasPrevPage: pageNumber > 1,
    data: leaveRequests
  });
});

// @desc    Lấy danh sách yêu cầu xin phép nghỉ học của một người dùng (học sinh hoặc giáo viên)
// @route   GET /api/leave-requests/user/:userId
// @access  Private (người dùng chỉ xem được của mình, giáo viên và admin xem được tất cả)
exports.getUserLeaveRequests = asyncHandler(async (req, res) => {
  let { userId } = req.params;
  const { status, startDate, endDate, requestType, page = 1, limit = 10 } = req.query;

  // Kiểm tra quyền truy cập - nếu là học sinh thì chỉ cho phép xem đơn của chính mình
  let accessNote = null;
  if (req.user.role.includes('student') && req.user.id !== userId) {
    // Thay vì trả về lỗi 403, tự động chuyển về xem đơn của chính mình
    accessNote = 'Bạn chỉ có thể xem đơn xin phép của chính mình';
    userId = req.user.id;
  }

  // Giáo viên có thể xem đơn của học sinh trong lớp mình dạy
  if (req.user.role.includes('teacher') && !req.user.role.includes('admin') && req.user.id !== userId) {
    // TODO: Thêm logic kiểm tra giáo viên có quyền xem đơn của học sinh này không
    // Hiện tại cho phép giáo viên xem tất cả
  }

  const query = { requester: userId };

  // Lọc theo loại yêu cầu nếu có
  if (requestType && ['student', 'teacher'].includes(requestType)) {
    query.requestType = requestType;
  }

  // Lọc theo trạng thái nếu có
  if (status && ['pending', 'approved', 'rejected'].includes(status)) {
    query.status = status;
  }

  // Lọc theo khoảng thời gian nếu có - Logic đúng: tìm records có khoảng thời gian giao với filter
  if (startDate || endDate) {
    if (startDate && endDate) {
      // Có cả startDate và endDate: tìm records có khoảng thời gian giao với [startDate, endDate]
      const start = new Date(startDate);
      start.setHours(0, 0, 0, 0);
      const end = new Date(endDate);
      end.setHours(23, 59, 59, 999);

      query.$and = [
        { startDate: { $lte: end } },   // Record bắt đầu trước hoặc trong khoảng filter
        { endDate: { $gte: start } }    // Record kết thúc sau hoặc trong khoảng filter
      ];
    } else if (startDate) {
      // Chỉ có startDate: tìm records kết thúc sau startDate
      const start = new Date(startDate);
      start.setHours(0, 0, 0, 0);
      query.endDate = { $gte: start };
    } else if (endDate) {
      // Chỉ có endDate: tìm records bắt đầu trước endDate
      const end = new Date(endDate);
      end.setHours(23, 59, 59, 999);
      query.startDate = { $lte: end };
    }
  }

  // Tính toán pagination
  const pageNumber = parseInt(page);
  const limitNumber = parseInt(limit);
  const skip = (pageNumber - 1) * limitNumber;

  // Lấy tổng số records để tính totalPages
  const totalLeaveRequests = await LeaveRequest.countDocuments(query);
  const totalPages = Math.ceil(totalLeaveRequests / limitNumber);

  const leaveRequests = await LeaveRequest.find(query)
    .populate('class', 'name')
    .populate('requester', 'name studentId role specificRole')
    .populate('approvedBy', 'name')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limitNumber);

  const response = {
    success: true,
    count: leaveRequests.length,
    totalCount: totalLeaveRequests,
    currentPage: pageNumber,
    totalPages,
    hasNextPage: pageNumber < totalPages,
    hasPrevPage: pageNumber > 1,
    data: leaveRequests
  };

  // Thêm thông báo nếu có
  if (accessNote) {
    response.notice = accessNote;
  }

  res.json(response);
});

// Giữ lại alias cho tương thích với code cũ
exports.getStudentLeaveRequests = exports.getUserLeaveRequests;

// @desc    Phê duyệt hoặc từ chối yêu cầu xin phép nghỉ học
// @route   PUT /api/leave-requests/:id
// @access  Private (giáo viên duyệt đơn học sinh lớp mình làm chủ nhiệm, admin duyệt đơn giáo viên)
exports.updateLeaveRequestStatus = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { status, approverNotes } = req.body;

  // Kiểm tra trạng thái hợp lệ
  if (!status || !['approved', 'rejected'].includes(status)) {
    return res.status(400).json({
      success: false,
      msg: MESSAGES.ERROR.VALIDATION_FAILED
    });
  }

  let leaveRequest = await LeaveRequest.findById(id).populate('class');

  if (!leaveRequest) {
    return res.status(404).json({
      success: false,
      msg: MESSAGES.LEAVE_REQUEST.NOT_FOUND
    });
  }

  // Kiểm tra quyền phê duyệt dựa trên loại yêu cầu
  if (leaveRequest.requestType === 'teacher') {
    // Chỉ admin (hiệu trưởng) mới có thể duyệt đơn của giáo viên
    if (!req.user.role.includes(ROLES.ADMIN)) {
      return res.status(403).json({
        success: false,
        msg: 'Chỉ hiệu trưởng mới có thể phê duyệt đơn xin phép của giáo viên'
      });
    }
  } else if (leaveRequest.requestType === 'student') {
    // Kiểm tra quyền duyệt đơn học sinh
    if (req.user.role.includes(ROLES.ADMIN)) {
      // Admin có thể duyệt tất cả đơn của học sinh
    } else if (req.user.role.includes(ROLES.TEACHER)) {
      // Giáo viên chỉ có thể duyệt đơn của học sinh trong lớp mình làm chủ nhiệm
      if (!leaveRequest.class || !leaveRequest.class.homeroomTeacher || 
          leaveRequest.class.homeroomTeacher.toString() !== req.user.id) {
        return res.status(403).json({
          success: false,
          msg: 'Bạn chỉ có thể phê duyệt đơn xin phép của học sinh trong lớp mình làm chủ nhiệm'
        });
      }
    } else {
      return res.status(403).json({
        success: false,
        msg: 'Bạn không có quyền phê duyệt đơn xin phép này'
      });
    }
  } else {
    return res.status(400).json({
      success: false,
      msg: 'Loại yêu cầu không hợp lệ'
    });
  }

  // Xử lý các bản ghi điểm danh dựa trên trạng thái mới (chỉ áp dụng cho học sinh)
  if (leaveRequest.requestType === 'student') {
    if (status === 'approved') {
      // Nếu phê duyệt đơn xin phép, tạo các bản ghi điểm danh với trạng thái "excused"

      // Tạo các bản ghi điểm danh cho khoảng thời gian xin phép
      const startDate = new Date(leaveRequest.startDate);
      const endDate = new Date(leaveRequest.endDate);
      const sessions = leaveRequest.sessions;

      // Tạo một mảng các ngày từ startDate đến endDate
      const dates = [];
      let currentDate = new Date(startDate);

      while (currentDate <= endDate) {
        dates.push(new Date(currentDate));
        currentDate.setDate(currentDate.getDate() + 1);
      }

      // Tạo bản ghi điểm danh cho mỗi ngày và mỗi buổi
      for (const date of dates) {
        for (const session of sessions) {
          if (session === 'all-day') {
            // Nếu nghỉ cả ngày, tạo bản ghi cho cả buổi sáng và chiều
            await createExcusedAttendance(leaveRequest.requester, leaveRequest.class._id, date, 'morning', leaveRequest._id, req.user.id);
            await createExcusedAttendance(leaveRequest.requester, leaveRequest.class._id, date, 'afternoon', leaveRequest._id, req.user.id);
          } else {
            // Nếu chỉ nghỉ buổi sáng hoặc chiều
            await createExcusedAttendance(leaveRequest.requester, leaveRequest.class._id, date, session, leaveRequest._id, req.user.id);
          }
        }
      }
    } else if (status === 'rejected') {
      // Nếu từ chối đơn xin phép, tạo các bản ghi điểm danh với trạng thái "absent"

      console.log(`Đang xử lý từ chối đơn xin phép với ID: ${leaveRequest._id}`);

      try {
        // Kiểm tra xem đơn xin phép có phải cho ngày trong quá khứ không
        const now = new Date();
        now.setHours(0, 0, 0, 0);

        const startDate = new Date(leaveRequest.startDate);
        const endDate = new Date(leaveRequest.endDate);
        const sessions = leaveRequest.sessions;

        // Tạo một mảng các ngày từ startDate đến endDate
        const dates = [];
        let currentDate = new Date(startDate);

        while (currentDate <= endDate) {
          // Thêm tất cả các ngày, bao gồm cả ngày trong tương lai
          dates.push(new Date(currentDate));
          currentDate.setDate(currentDate.getDate() + 1);
        }

        console.log(`Tổng số ngày cần xử lý: ${dates.length}`);

        // Nếu không có ngày nào, log thông tin để debug
        if (dates.length === 0) {
          console.log(`Không có ngày nào để xử lý. StartDate: ${startDate}, EndDate: ${endDate}`);
        }

        // Log thông tin về các buổi cần xử lý
        console.log(`Các buổi cần xử lý: ${sessions.join(', ')}`);

        // Tạo bản ghi điểm danh cho mỗi ngày và mỗi buổi
        for (const date of dates) {
          console.log(`Đang xử lý ngày: ${date.toISOString().split('T')[0]}`);

          for (const session of sessions) {
            console.log(`  - Buổi: ${session}`);

            if (session === 'all-day') {
              // Nếu nghỉ cả ngày, tạo bản ghi cho cả buổi sáng và chiều
              console.log(`    Tạo bản ghi điểm danh buổi sáng`);
              await createAbsentAttendance(leaveRequest.requester, leaveRequest.class._id, date, 'morning', req.user.id, leaveRequest._id);

              console.log(`    Tạo bản ghi điểm danh buổi chiều`);
              await createAbsentAttendance(leaveRequest.requester, leaveRequest.class._id, date, 'afternoon', req.user.id, leaveRequest._id);
            } else {
              // Nếu chỉ nghỉ buổi sáng hoặc chiều
              console.log(`    Tạo bản ghi điểm danh buổi ${session}`);
              await createAbsentAttendance(leaveRequest.requester, leaveRequest.class._id, date, session, req.user.id, leaveRequest._id);
            }
          }
        }

        console.log(`Đã tạo ${dates.length} bản ghi điểm danh với trạng thái "absent" cho đơn xin phép bị từ chối`);
      } catch (error) {
        console.error('Lỗi khi tạo bản ghi điểm danh:', error);
      }
    }
  }
  
  // Giáo viên xin phép không cần xử lý điểm danh vì không có model điểm danh cho giáo viên
  // TODO: Có thể thêm xử lý notification hoặc cập nhật lịch dạy nếu cần

  // Cập nhật trạng thái đơn xin phép
  leaveRequest = await LeaveRequest.findByIdAndUpdate(
    id,
    {
      status,
      approverNotes,
      approvedBy: req.user.id,
      approvedAt: Date.now(),
      updatedAt: Date.now()
    },
    { new: true, runValidators: true }
  );

  res.json({
    success: true,
    msg: `${status === 'approved' ? 'Phê duyệt' : 'Từ chối'} đơn xin phép thành công`,
    data: leaveRequest
  });
});

// @desc    Xóa yêu cầu xin phép nghỉ học
// @route   DELETE /api/leave-requests/:id
// @access  Private (người dùng chỉ xóa được của mình và chỉ khi đang ở trạng thái pending, giáo viên và admin xóa được tất cả)
exports.deleteLeaveRequest = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const leaveRequest = await LeaveRequest.findById(id);

  if (!leaveRequest) {
    return res.status(404).json({
      success: false,
      msg: MESSAGES.LEAVE_REQUEST.NOT_FOUND
    });
  }

  // Kiểm tra quyền xóa
  if (req.user.role.includes('student')) {
    // Học sinh chỉ xóa được yêu cầu của mình và chỉ khi đang ở trạng thái pending
    if (leaveRequest.requester.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        msg: MESSAGES.ERROR.FORBIDDEN
      });
    }

    if (leaveRequest.status !== 'pending') {
      return res.status(400).json({
        success: false,
        msg: 'Chỉ có thể xóa đơn xin phép đang chờ phê duyệt'
      });
    }
  } else if (req.user.role.includes('teacher') && !req.user.role.includes('admin')) {
    // Giáo viên chỉ xóa được đơn của mình hoặc đơn của học sinh (chỉ khi pending)
    if (leaveRequest.requester.toString() === req.user.id) {
      // Giáo viên xóa đơn của chính mình
      if (leaveRequest.status !== 'pending') {
        return res.status(400).json({
          success: false,
          msg: 'Chỉ có thể xóa đơn xin phép đang chờ phê duyệt'
        });
      }
    } else if (leaveRequest.requestType === 'student') {
      // Giáo viên xóa đơn của học sinh
      if (leaveRequest.status !== 'pending') {
        return res.status(400).json({
          success: false,
          msg: 'Chỉ có thể xóa đơn xin phép đang chờ phê duyệt'
        });
      }
    } else {
      return res.status(403).json({
        success: false,
        msg: MESSAGES.ERROR.FORBIDDEN
      });
    }
  } else if (!req.user.role.includes('admin')) {
    return res.status(403).json({
      success: false,
      msg: MESSAGES.ERROR.FORBIDDEN
    });
  }

  // Admin có thể xóa tất cả đơn xin phép

  // Sử dụng findByIdAndDelete thay vì remove() vì remove() không còn được hỗ trợ trong phiên bản mới của Mongoose
  await LeaveRequest.findByIdAndDelete(id);

  res.json({
    success: true,
    data: {}
  });
});

// @desc    Lấy danh sách yêu cầu xin phép của tất cả giáo viên
// @route   GET /api/leave-requests/teachers
// @access  Private (chỉ admin/hiệu trưởng)
exports.getTeacherLeaveRequests = asyncHandler(async (req, res) => {
  const { status, startDate, endDate, teacherId, page = 1, limit = 10 } = req.query;

  // Kiểm tra quyền truy cập - chỉ admin (hiệu trưởng) mới được xem
  if (!req.user.role.includes(ROLES.ADMIN)) {
    return res.status(403).json({
      success: false,
      msg: 'Chỉ hiệu trưởng mới có thể xem đơn xin phép của giáo viên'
    });
  }

  const query = { requestType: 'teacher' };

  // Lọc theo giáo viên cụ thể nếu có
  if (teacherId) {
    query.requester = teacherId;
  }

  // Lọc theo trạng thái nếu có
  if (status && ['pending', 'approved', 'rejected'].includes(status)) {
    query.status = status;
  }

  // Lọc theo khoảng thời gian nếu có - Logic đúng: tìm records có khoảng thời gian giao với filter
  if (startDate || endDate) {
    if (startDate && endDate) {
      // Có cả startDate và endDate: tìm records có khoảng thời gian giao với [startDate, endDate]
      const start = new Date(startDate);
      start.setHours(0, 0, 0, 0);
      const end = new Date(endDate);
      end.setHours(23, 59, 59, 999);

      query.$and = [
        { startDate: { $lte: end } },   // Record bắt đầu trước hoặc trong khoảng filter
        { endDate: { $gte: start } }    // Record kết thúc sau hoặc trong khoảng filter
      ];
    } else if (startDate) {
      // Chỉ có startDate: tìm records kết thúc sau startDate
      const start = new Date(startDate);
      start.setHours(0, 0, 0, 0);
      query.endDate = { $gte: start };
    } else if (endDate) {
      // Chỉ có endDate: tìm records bắt đầu trước endDate
      const end = new Date(endDate);
      end.setHours(23, 59, 59, 999);
      query.startDate = { $lte: end };
    }
  }

  // Tính toán pagination
  const pageNumber = parseInt(page);
  const limitNumber = parseInt(limit);
  const skip = (pageNumber - 1) * limitNumber;

  // Lấy tổng số records để tính totalPages
  const totalLeaveRequests = await LeaveRequest.countDocuments(query);
  const totalPages = Math.ceil(totalLeaveRequests / limitNumber);

  const leaveRequests = await LeaveRequest.find(query)
    .populate('requester', 'name role specificRole department zaloId')
    .populate('createdBy', 'name')
    .populate('approvedBy', 'name')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limitNumber);

  res.json({
    success: true,
    count: leaveRequests.length,
    totalCount: totalLeaveRequests,
    currentPage: pageNumber,
    totalPages,
    hasNextPage: pageNumber < totalPages,
    hasPrevPage: pageNumber > 1,
    data: leaveRequests
  });
});

// @desc    Lấy danh sách yêu cầu xin phép của học sinh trong các lớp giáo viên làm chủ nhiệm
// @route   GET /api/leave-requests/my-students
// @access  Private (chỉ giáo viên)
exports.getMyStudentLeaveRequests = asyncHandler(async (req, res) => {
  const { status, startDate, endDate, classId, page = 1, limit = 10 } = req.query;

  // Kiểm tra quyền truy cập - chỉ giáo viên
  if (!req.user.role.includes(ROLES.TEACHER)) {
    return res.status(403).json({
      success: false,
      msg: 'Chỉ giáo viên mới có thể sử dụng API này'
    });
  }

  // Tìm các lớp mà giáo viên này làm chủ nhiệm
  const homeroomClasses = await Class.find({ homeroomTeacher: req.user.id });
  
  if (homeroomClasses.length === 0) {
    return res.json({
      success: true,
      count: 0,
      totalCount: 0,
      currentPage: parseInt(page),
      totalPages: 0,
      hasNextPage: false,
      hasPrevPage: false,
      data: [],
      msg: 'Bạn chưa làm chủ nhiệm lớp nào'
    });
  }

  const homeroomClassIds = homeroomClasses.map(cls => cls._id);

  const query = { 
    requestType: 'student',
    class: { $in: homeroomClassIds }
  };

  // Lọc theo lớp cụ thể nếu có (phải là lớp mình làm chủ nhiệm)
  if (classId) {
    if (!homeroomClassIds.some(id => id.toString() === classId)) {
      return res.status(403).json({
        success: false,
        msg: 'Bạn không phải là chủ nhiệm của lớp này'
      });
    }
    query.class = classId;
  }

  // Lọc theo trạng thái nếu có
  if (status && ['pending', 'approved', 'rejected'].includes(status)) {
    query.status = status;
  }

  // Lọc theo khoảng thời gian nếu có - Logic đúng: tìm records có khoảng thời gian giao với filter
  if (startDate || endDate) {
    if (startDate && endDate) {
      // Có cả startDate và endDate: tìm records có khoảng thời gian giao với [startDate, endDate]
      const start = new Date(startDate);
      start.setHours(0, 0, 0, 0);
      const end = new Date(endDate);
      end.setHours(23, 59, 59, 999);

      query.$and = [
        { startDate: { $lte: end } },   // Record bắt đầu trước hoặc trong khoảng filter
        { endDate: { $gte: start } }    // Record kết thúc sau hoặc trong khoảng filter
      ];
    } else if (startDate) {
      // Chỉ có startDate: tìm records kết thúc sau startDate
      const start = new Date(startDate);
      start.setHours(0, 0, 0, 0);
      query.endDate = { $gte: start };
    } else if (endDate) {
      // Chỉ có endDate: tìm records bắt đầu trước endDate
      const end = new Date(endDate);
      end.setHours(23, 59, 59, 999);
      query.startDate = { $lte: end };
    }
  }

  // Tính toán pagination
  const pageNumber = parseInt(page);
  const limitNumber = parseInt(limit);
  const skip = (pageNumber - 1) * limitNumber;

  // Lấy tổng số records để tính totalPages
  const totalLeaveRequests = await LeaveRequest.countDocuments(query);
  const totalPages = Math.ceil(totalLeaveRequests / limitNumber);

  const leaveRequests = await LeaveRequest.find(query)
    .populate('requester', 'name studentId role specificRole')
    .populate('class', 'name group')
    .populate('createdBy', 'name')
    .populate('approvedBy', 'name')
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limitNumber);

  res.json({
    success: true,
    count: leaveRequests.length,
    totalCount: totalLeaveRequests,
    currentPage: pageNumber,
    totalPages,
    hasNextPage: pageNumber < totalPages,
    hasPrevPage: pageNumber > 1,
    data: leaveRequests,
    homeroomClasses: homeroomClasses.map(cls => ({
      id: cls._id,
      name: cls.name,
      group: cls.group
    }))
  });
});

// Không cần middleware xử lý upload file vì chúng ta lưu trữ hình ảnh dưới dạng Data URL