const mongoose = require('mongoose');

const ExamSupervisionSchema = new mongoose.Schema({
  examType: {
    type: String,
    required: true,
    enum: ['CUOI_HK1', 'CUOI_HK2', 'THI_TIEP_CAN_LAN_1', 'THI_TIEP_CAN_LAN_2', 'THI_TIEP_CAN_LAN_3'],
    trim: true,
  },
  schoolYear: {
    type: String,
    required: true,
    trim: true, // "2024-2025"
  },
  teacher: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  sessions: [{
    date: {
      type: Date,
      required: true,
    },
    timeSlot: {
      type: String,
      enum: ['morning', 'afternoon'],
      required: true,
    },
    room: {
      type: String,
      trim: true,
    },
    subject: {
      type: String,
      trim: true,
    },
    status: {
      type: String,
      enum: ['DA_PHAN_CONG', 'DA_HOAN_THANH', 'YEU_CAU_DOI_BUOI'],
      default: 'DA_PHAN_CONG',
    },
  }],
  notes: {
    type: String,
    trim: true,
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
}, { 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Method để tính sessionNumber
ExamSupervisionSchema.methods.getSessionNumber = function(sessionId) {
  const sessionIndex = this.sessions.findIndex(s => s._id.toString() === sessionId);
  return sessionIndex !== -1 ? sessionIndex + 1 : null;
};

module.exports = mongoose.model('ExamSupervision', ExamSupervisionSchema); 