const express = require('express');
const router = express.Router();

// Import controller functions
const {
  getExamSupervisions,
  getMyExamAssignments,
  createExamSupervision,
  updateExamSupervision,
  deleteExamSupervision,
  getExamSupervisionById
} = require('../controllers/examSupervisionController');

// Import middleware
const { protect, authorize } = require('../middlewares/auth');
const { validateExamSupervision } = require('../middlewares/examValidation');

// @desc    Lấy danh sách phân công coi thi (Admin)
// @route   GET /api/exam-supervisions
// @access  Private (Admin only)
router.get('/', protect, authorize('admin'), getExamSupervisions);

// @desc    Lấy phân công coi thi của giáo viên đang đăng nhập
// @route   GET /api/exam-supervisions/my-assignments
// @access  Private (Teacher only)
router.get('/my-assignments', protect, authorize('teacher'), getMyExamAssignments);

// @desc    Lấy chi tiết phân công coi thi
// @route   GET /api/exam-supervisions/:id
// @access  Private (Admin, Teacher)
router.get('/:id', protect, getExamSupervisionById);

// @desc    Tạo phân công coi thi mới
// @route   POST /api/exam-supervisions
// @access  Private (Admin only)
router.post('/', protect, authorize('admin'), validateExamSupervision, createExamSupervision);

// @desc    Cập nhật phân công coi thi
// @route   PUT /api/exam-supervisions/:id
// @access  Private (Admin only)
router.put('/:id', protect, authorize('admin'), updateExamSupervision);

// @desc    Xóa phân công coi thi
// @route   DELETE /api/exam-supervisions/:id
// @access  Private (Admin only)
router.delete('/:id', protect, authorize('admin'), deleteExamSupervision);

module.exports = router; 