// controllers/examController.js
const Exam = require('../models/Exam');
const Question = require('../models/Question');
const ExamAttempt = require('../models/ExamAttempt');
const ExamResult = require('../models/ExamResult');
const User = require('../models/User');
const Subject = require('../models/Subject');
const Class = require('../models/Class');
const { processExamSubmission } = require('../utils/examUtils');
const asyncHandler = require('../middlewares/async');
const MESSAGES = require('../constants/messages');
const ROLES = require('../constants/roleConstants');

// @desc    Tạo đề thi mới
// @route   POST /api/exams
// @access  Private (giáo viên)
exports.createExam = asyncHandler(async (req, res) => {
    const {
        title,
        subject,
        description,
        instructions,
        totalQuestions,
        questionsPerAttempt,
        timeLimit,
        startDate,
        endDate
    } = req.body;

    // Kiểm tra dữ liệu đầu vào
    if (!title || !subject || !startDate || !endDate) {
        return res.status(400).json({ msg: MESSAGES.ERROR.MISSING_INFO });
    }

    // Xác thực môn học
    const subjectExists = await Subject.findById(subject);
    if (!subjectExists) {
        return res.status(404).json({ msg: MESSAGES.SUBJECT.NOT_FOUND });
    }

    // Tạo đề thi mới
    const newExam = new Exam({
        title,
        subject,
        description,
        instructions,
        totalQuestions: totalQuestions || 100,
        questionsPerAttempt: questionsPerAttempt || 20,
        timeLimit: timeLimit || 45,
        startDate,
        endDate,
        createdBy: req.user._id
    });

    await newExam.save();

    res.status(201).json({
        success: true,
        msg: MESSAGES.EXAM.CREATED,
        data: newExam
    });
});

// @desc    Lấy tất cả đề thi theo môn học
// @route   GET /api/exams
// @access  Private
exports.getExams = asyncHandler(async (req, res) => {
    const { subjectId, classId, createdByMe, startDateFrom, startDateTo } = req.query;

    // Xây dựng truy vấn
    const query = {};
    if (subjectId) {
        query.subject = subjectId;
    }

    // Nếu có filter createdByMe=true, chỉ lấy đề thi do người dùng hiện tại tạo
    if (createdByMe === 'true') {
        query.createdBy = req.user._id;
    }

    // Nếu là học sinh, chỉ hiển thị các đề thi đang hoạt động
    // Nếu là học sinh, chỉ hiển thị các đề thi đang hoạt động và được phân công cho lớp của học sinh
    if (req.user.role.includes(ROLES.STUDENT)) {
        query.isActive = true;
        const currentDate = new Date();
        query.startDate = { $lte: currentDate };
        query.endDate = { $gte: currentDate };

        // Lấy lớp của học sinh từ StudentEnrollment
        const StudentEnrollment = require('../models/StudentEnrollment');
        const enrollment = await StudentEnrollment.findOne({
            student: req.user._id,
            status: 'active'
        });

        if (enrollment) {
            query.classes = enrollment.class;
        } else {
            // Nếu không có enrollment, không có đề thi nào
            query.classes = null;
        }
    }
    // Nếu là giáo viên hoặc admin và có filter theo lớp
    else if ((req.user.role.includes(ROLES.TEACHER) || req.user.role.includes(ROLES.ADMIN)) && classId) {
        query.classes = classId;
    }

    // Lọc theo khoảng thời gian startDate (chỉ áp dụng cho giáo viên/admin, không ảnh hưởng đến logic học sinh)
    if (!req.user.role.includes(ROLES.STUDENT) && (startDateFrom || startDateTo)) {
        // Nếu đã có query.startDate từ logic học sinh, cần merge
        if (query.startDate) {
            // Với học sinh, đã có logic $lte currentDate, cần giữ lại
            if (startDateFrom) {
                const fromDate = new Date(startDateFrom);
                fromDate.setHours(0, 0, 0, 0);
                query.startDate.$gte = fromDate;
            }
            if (startDateTo) {
                const toDate = new Date(startDateTo);
                toDate.setHours(23, 59, 59, 999);
                // Giữ giá trị nhỏ hơn giữa toDate và currentDate
                if (query.startDate.$lte) {
                    query.startDate.$lte = new Date(Math.min(toDate, query.startDate.$lte));
                } else {
                    query.startDate.$lte = toDate;
                }
            }
        } else {
            // Tạo filter startDate mới cho giáo viên/admin
            query.startDate = {};
            if (startDateFrom) {
                const fromDate = new Date(startDateFrom);
                fromDate.setHours(0, 0, 0, 0);
                query.startDate.$gte = fromDate;
            }
            if (startDateTo) {
                const toDate = new Date(startDateTo);
                toDate.setHours(23, 59, 59, 999);
                query.startDate.$lte = toDate;
            }
        }
    }

    const exams = await Exam.find(query)
        .populate('subject', 'name code')
        .populate('createdBy', 'name')
        .populate('classes', 'name grade')
        .sort({ createdAt: -1 });

    // Nếu là học sinh, bổ sung thông tin về số lần đã làm
    let results = [...exams];

    if (req.user.role.includes(ROLES.STUDENT)) {
        const studentResults = await ExamResult.find({
            student: req.user._id,
            exam: { $in: exams.map(exam => exam._id) }
        });

        results = exams.map(exam => {
            const examObj = exam.toObject();
            const studentResult = studentResults.find(
                r => r.exam.toString() === exam._id.toString()
            );

            if (studentResult) {
                examObj.attempts = {
                    totalAttempts: studentResult.totalAttempts,
                    bestScore: studentResult.bestScore,
                    lastAttemptDate: studentResult.lastAttemptDate
                };
            } else {
                examObj.attempts = {
                    totalAttempts: 0,
                    bestScore: 0
                };
            }

            return examObj;
        });
    }

    res.json({
        success: true,
        count: results.length,
        data: results
    });
});

// @desc    Lấy thông tin chi tiết đề thi
// @route   GET /api/exams/:id
// @access  Private
exports.getExamById = asyncHandler(async (req, res) => {
    const { id } = req.params;

    const exam = await Exam.findById(id)
        .populate('subject', 'name code')
        .populate('createdBy', 'name');

    if (!exam) {
        return res.status(404).json({ msg: MESSAGES.EXAM.NOT_FOUND });
    }

    // Nếu là học sinh, kiểm tra xem đề thi có đang hoạt động không
    if (req.user.role.includes(ROLES.STUDENT)) {
        const currentDate = new Date();
        if (!exam.isActive || exam.startDate > currentDate || exam.endDate < currentDate) {
            return res.status(403).json({ msg: MESSAGES.EXAM.NOT_ACTIVE_OR_EXPIRED });
        }
    }

    // Nếu là học sinh, bổ sung thông tin về số lần đã làm
    let examData = exam;

    if (req.user.role.includes(ROLES.STUDENT)) {
        const studentResult = await ExamResult.findOne({
            student: req.user._id,
            exam: exam._id
        });

        examData = exam.toObject();

        if (studentResult) {
            examData.attempts = {
                totalAttempts: studentResult.totalAttempts,
                bestScore: studentResult.bestScore,
                lastAttemptDate: studentResult.lastAttemptDate
            };
        } else {
            examData.attempts = {
                totalAttempts: 0,
                bestScore: 0
            };
        }
    }

    res.json({
        success: true,
        data: examData
    });
});

// @desc    Lấy danh sách đề thi do người dùng tạo
// @route   GET /api/exams/my-exams
// @access  Private (giáo viên, admin)
exports.getMyExams = asyncHandler(async (req, res) => {
    const { subjectId, page = 1, limit = 10, status, startDateFrom, startDateTo } = req.query;

    // Chỉ giáo viên và admin mới có thể sử dụng endpoint này
    if (!req.user.role.includes(ROLES.TEACHER) && !req.user.role.includes(ROLES.ADMIN)) {
        return res.status(403).json({ 
            success: false,
            msg: MESSAGES.ERROR.FORBIDDEN 
        });
    }

    // Xây dựng truy vấn
    const query = { createdBy: req.user._id };
    
    // Lọc theo môn học nếu có
    if (subjectId) {
        query.subject = subjectId;
    }
    
    // Lọc theo trạng thái nếu có
    if (status === 'active') {
        query.isActive = true;
    } else if (status === 'inactive') {
        query.isActive = false;
    }

    // Lọc theo khoảng thời gian startDate
    if (startDateFrom || startDateTo) {
        query.startDate = {};
        
        // Lọc từ ngày bắt đầu
        if (startDateFrom) {
            const fromDate = new Date(startDateFrom);
            fromDate.setHours(0, 0, 0, 0); // Đặt về đầu ngày
            query.startDate.$gte = fromDate;
        }
        
        // Lọc đến ngày kết thúc
        if (startDateTo) {
            const toDate = new Date(startDateTo);
            toDate.setHours(23, 59, 59, 999); // Đặt về cuối ngày
            query.startDate.$lte = toDate;
        }
    }

    // Tính toán pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Lấy danh sách đề thi
    const exams = await Exam.find(query)
        .populate('subject', 'name code')
        .populate('classes', 'name grade')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit));

    // Đếm tổng số đề thi
    const totalExams = await Exam.countDocuments(query);

    // Tính toán thống kê cho mỗi đề thi
    const examsWithStats = await Promise.all(
        exams.map(async (exam) => {
            const examObj = exam.toObject();
            
            // Đếm số câu hỏi
            const totalQuestions = await Question.countDocuments({ exam: exam._id });
            
            // Đếm số lượt thi
            const totalAttempts = await ExamAttempt.countDocuments({ 
                exam: exam._id,
                isCompleted: true 
            });
            
            // Đếm số học sinh đã thi
            const uniqueStudents = await ExamAttempt.distinct('student', { 
                exam: exam._id,
                isCompleted: true 
            });

            examObj.stats = {
                totalQuestions,
                totalAttempts,
                totalStudents: uniqueStudents.length
            };

            return examObj;
        })
    );

    res.json({
        success: true,
        data: examsWithStats,
        pagination: {
            currentPage: parseInt(page),
            totalPages: Math.ceil(totalExams / parseInt(limit)),
            totalItems: totalExams,
            itemsPerPage: parseInt(limit)
        }
    });
});

// @desc    Cập nhật đề thi
// @route   PUT /api/exams/:id
// @access  Private (giáo viên)
exports.updateExam = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const {
        title,
        subject,
        description,
        instructions,
        totalQuestions,
        questionsPerAttempt,
        timeLimit,
        startDate,
        endDate,
        isActive
    } = req.body;

    // Kiểm tra đề thi có tồn tại không
    let exam = await Exam.findById(id);
    if (!exam) {
        return res.status(404).json({ msg: MESSAGES.EXAM.NOT_FOUND });
    }

    // Chỉ người tạo hoặc admin mới được cập nhật
    if (exam.createdBy.toString() !== req.user._id.toString() && !req.user.role.includes(ROLES.ADMIN)) {
        return res.status(403).json({ msg: MESSAGES.EXAM.NO_PERMISSION_UPDATE });
    }

    // Cập nhật thông tin
    if (title) exam.title = title;
    if (subject) {
        // Xác thực môn học
        const subjectExists = await Subject.findById(subject);
        if (!subjectExists) {
            return res.status(404).json({ msg: MESSAGES.SUBJECT.NOT_FOUND });
        }
        exam.subject = subject;
    }
    if (description !== undefined) exam.description = description;
    if (instructions !== undefined) exam.instructions = instructions;
    if (totalQuestions) exam.totalQuestions = totalQuestions;
    if (questionsPerAttempt) exam.questionsPerAttempt = questionsPerAttempt;
    if (timeLimit) exam.timeLimit = timeLimit;
    if (startDate) exam.startDate = startDate;
    if (endDate) exam.endDate = endDate;
    if (isActive !== undefined) exam.isActive = isActive;

    exam.updatedAt = Date.now();
    await exam.save();

    res.json({
        success: true,
        msg: MESSAGES.EXAM.UPDATED,
        data: exam
    });
});

// @desc    Xóa đề thi
// @route   DELETE /api/exams/:id
// @access  Private (giáo viên)
exports.deleteExam = asyncHandler(async (req, res) => {
    const { id } = req.params;

    // Kiểm tra đề thi có tồn tại không
    const exam = await Exam.findById(id);
    if (!exam) {
        return res.status(404).json({ msg: MESSAGES.EXAM.NOT_FOUND });
    }

    // Chỉ người tạo hoặc admin mới được xóa
    if (exam.createdBy.toString() !== req.user._id.toString() && !req.user.role.includes(ROLES.ADMIN)) {
        return res.status(403).json({ msg: MESSAGES.EXAM.NO_PERMISSION_DELETE });
    }

    // Xóa tất cả câu hỏi liên quan
    await Question.deleteMany({ exam: id });

    // Xóa tất cả lần thi và kết quả liên quan
    await ExamAttempt.deleteMany({ exam: id });
    await ExamResult.deleteMany({ exam: id });

    // Xóa đề thi
    await exam.deleteOne();

    res.json({
        success: true,
        msg: MESSAGES.EXAM.DELETED
    });
});

// @desc    Thêm câu hỏi vào đề thi
// @route   POST /api/exams/:id/questions
// @access  Private (giáo viên)
exports.addQuestion = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { content, options, correctOption, explanation, difficulty } = req.body;

    // Kiểm tra dữ liệu đầu vào
    if (!content || !options || options.length < 2 || correctOption === undefined) {
        return res.status(400).json({ msg: MESSAGES.ERROR.MISSING_INFO });
    }

    // Kiểm tra đề thi có tồn tại không
    const exam = await Exam.findById(id);
    if (!exam) {
        return res.status(404).json({ msg: MESSAGES.EXAM.NOT_FOUND });
    }

    // Chỉ người tạo đề thi hoặc admin mới được thêm câu hỏi
    if (exam.createdBy.toString() !== req.user._id.toString() && !req.user.role.includes(ROLES.ADMIN)) {
        return res.status(403).json({ msg: MESSAGES.EXAM.NO_PERMISSION_ADD_QUESTION });
    }

    // Tạo câu hỏi mới
    const newQuestion = new Question({
        exam: id,
        content,
        options,
        correctOption,
        explanation,
        difficulty: difficulty || 'medium'
    });

    await newQuestion.save();

    res.status(201).json({
        success: true,
        msg: MESSAGES.QUESTION.CREATED,
        data: newQuestion
    });
});

// @desc    Lấy tất cả câu hỏi của đề thi
// @route   GET /api/exams/:id/questions
// @access  Private (giáo viên)
exports.getQuestions = asyncHandler(async (req, res) => {
    const { id } = req.params;

    // Kiểm tra đề thi có tồn tại không
    const exam = await Exam.findById(id);
    if (!exam) {
        return res.status(404).json({ msg: MESSAGES.EXAM.NOT_FOUND });
    }

    // Giáo viên mới được xem tất cả câu hỏi
    if (!req.user.role.includes(ROLES.TEACHER) && !req.user.role.includes(ROLES.ADMIN)) {
        return res.status(403).json({ msg: MESSAGES.EXAM.NO_PERMISSION_VIEW_QUESTIONS });
    }

    const questions = await Question.find({ exam: id }).sort({ createdAt: 1 });

    res.json({
        success: true,
        count: questions.length,
        data: questions
    });
});

// @desc    Cập nhật câu hỏi
// @route   PUT /api/questions/:id
// @access  Private (giáo viên)
exports.updateQuestion = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { content, options, correctOption, explanation, difficulty } = req.body;

    // Kiểm tra câu hỏi có tồn tại không
    let question = await Question.findById(id).populate('exam');
    if (!question) {
        return res.status(404).json({ msg: MESSAGES.QUESTION.NOT_FOUND });
    }

    // Chỉ người tạo đề thi hoặc admin mới được cập nhật câu hỏi
    if (question.exam.createdBy.toString() !== req.user._id.toString() && !req.user.role.includes(ROLES.ADMIN)) {
        return res.status(403).json({ msg: MESSAGES.QUESTION.NO_PERMISSION_UPDATE });
    }

    // Cập nhật thông tin
    if (content) question.content = content;
    if (options) question.options = options;
    if (correctOption !== undefined) question.correctOption = correctOption;
    if (explanation !== undefined) question.explanation = explanation;
    if (difficulty) question.difficulty = difficulty;

    question.updatedAt = Date.now();
    await question.save();

    res.json({
        success: true,
        msg: MESSAGES.QUESTION.UPDATED,
        data: question
    });
});

// @desc    Xóa câu hỏi
// @route   DELETE /api/questions/:id
// @access  Private (giáo viên)
exports.deleteQuestion = asyncHandler(async (req, res) => {
    const { id } = req.params;

    // Kiểm tra câu hỏi có tồn tại không
    const question = await Question.findById(id).populate('exam');
    if (!question) {
        return res.status(404).json({ msg: MESSAGES.QUESTION.NOT_FOUND });
    }

    // Chỉ người tạo đề thi hoặc admin mới được xóa câu hỏi
    if (question.exam.createdBy.toString() !== req.user._id.toString() && !req.user.role.includes(ROLES.ADMIN)) {
        return res.status(403).json({ msg: MESSAGES.QUESTION.NO_PERMISSION_DELETE });
    }

    await question.deleteOne();

    res.json({
        success: true,
        msg: MESSAGES.QUESTION.DELETED
    });
});

// @desc    Bắt đầu làm bài thi
// @route   POST /api/exams/:id/attempt
// @access  Private (học sinh)
exports.startExam = asyncHandler(async (req, res) => {
    const { id } = req.params;

    // Kiểm tra đề thi có tồn tại không
    const exam = await Exam.findById(id);
    if (!exam) {
        return res.status(404).json({ msg: MESSAGES.EXAM.NOT_FOUND });
    }

    // Kiểm tra đề thi có đang hoạt động không
    const currentDate = new Date();
    if (!exam.isActive || exam.startDate > currentDate || exam.endDate < currentDate) {
        return res.status(403).json({ msg: MESSAGES.EXAM.NOT_ACTIVE_OR_EXPIRED });
    }

    // Lấy tất cả câu hỏi của đề thi
    const allQuestions = await Question.find({ exam: id });
    if (allQuestions.length === 0) {
        return res.status(404).json({ msg: MESSAGES.EXAM.NO_QUESTIONS });
    }

    // Lấy ngẫu nhiên questionsPerAttempt câu hỏi
    const shuffledQuestions = allQuestions.sort(() => 0.5 - Math.random());
    const selectedQuestions = shuffledQuestions.slice(0, Math.min(exam.questionsPerAttempt, allQuestions.length));

    // Tạo một lần làm bài mới
    const newAttempt = new ExamAttempt({
        student: req.user._id,
        exam: id,
        questions: selectedQuestions.map(q => ({
            question: q._id,
            selectedOption: null,
            isCorrect: false
        })),
        startTime: new Date()
    });

    await newAttempt.save();

    // Trả về thông tin làm bài, loại bỏ đáp án đúng
    const attemptData = {
        _id: newAttempt._id,
        startTime: newAttempt.startTime,
        timeLimit: exam.timeLimit,
        questions: await Promise.all(
            selectedQuestions.map(async (q) => {
                const questionDetail = await Question.findById(q._id);
                return {
                    _id: questionDetail._id,
                    content: questionDetail.content,
                    options: questionDetail.options
                };
            })
        )
    };

    res.status(201).json({
        success: true,
        msg: MESSAGES.EXAM_ATTEMPT.CREATED,
        data: attemptData
    });
});

// @desc    Trả lời câu hỏi
// @route   PUT /api/exam-attempts/:id/answer
// @access  Private (học sinh)
exports.answerQuestion = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { questionId, selectedOption } = req.body;

    // Kiểm tra dữ liệu đầu vào
    if (!questionId || selectedOption === undefined) {
        return res.status(400).json({ msg: MESSAGES.ERROR.MISSING_INFO });
    }

    // Kiểm tra lần làm bài có tồn tại không
    const attempt = await ExamAttempt.findById(id);
    if (!attempt) {
        return res.status(404).json({ msg: MESSAGES.EXAM_ATTEMPT.NOT_FOUND });
    }

    // Kiểm tra xem người dùng có quyền trả lời câu hỏi không
    if (attempt.student.toString() !== req.user._id.toString()) {
        return res.status(403).json({ msg: MESSAGES.EXAM.NO_PERMISSION_ANSWER });
    }

    // Kiểm tra xem lần làm bài đã hoàn thành chưa
    if (attempt.isCompleted) {
        return res.status(400).json({ msg: MESSAGES.EXAM_ATTEMPT.ALREADY_COMPLETED });
    }

    // Kiểm tra xem câu hỏi có nằm trong lần làm bài không
    const questionIndex = attempt.questions.findIndex(q => q.question.toString() === questionId);
    if (questionIndex === -1) {
        return res.status(404).json({ msg: MESSAGES.QUESTION.NOT_IN_ATTEMPT });
    }

    // Lấy đáp án đúng
    const question = await Question.findById(questionId);
    if (!question) {
        return res.status(404).json({ msg: MESSAGES.QUESTION.NOT_FOUND });
    }

    // Cập nhật câu trả lời
    attempt.questions[questionIndex].selectedOption = selectedOption;
    attempt.questions[questionIndex].isCorrect = selectedOption === question.correctOption;

    await attempt.save();

    res.json({
        success: true,
        msg: MESSAGES.EXAM_ATTEMPT.ANSWER_SAVED,
        data: {
            questionId,
            selectedOption,
            isCorrect: attempt.questions[questionIndex].isCorrect
        }
    });
});

// @desc    Hoàn thành bài thi
// @route   PUT /api/exam-attempts/:id/submit
// @access  Private (học sinh)
exports.submitExam = asyncHandler(async (req, res) => {
    const { id } = req.params;

    // Kiểm tra lần làm bài có tồn tại không
    let attempt = await ExamAttempt.findById(id);
    if (!attempt) {
        return res.status(404).json({ msg: MESSAGES.EXAM_ATTEMPT.NOT_FOUND });
    }

    // Kiểm tra xem người dùng có quyền nộp bài không
    if (attempt.student.toString() !== req.user._id.toString()) {
        return res.status(403).json({ msg: MESSAGES.EXAM.NO_PERMISSION_SUBMIT });
    }

    // Kiểm tra xem lần làm bài đã hoàn thành chưa
    if (attempt.isCompleted) {
        return res.status(400).json({ msg: MESSAGES.EXAM_ATTEMPT.ALREADY_COMPLETED });
    }

    // Xử lý nộp bài bằng hàm tái sử dụng
    const result = await processExamSubmission(attempt);

    res.json({
        success: true,
        msg: MESSAGES.EXAM_ATTEMPT.SUBMITTED,
        data: result
    });
});

// @desc    Lấy chi tiết kết quả làm bài
// @route   GET /api/exam-attempts/:id
// @access  Private
exports.getAttemptDetail = asyncHandler(async (req, res) => {
    const { id } = req.params;

    // Kiểm tra lần làm bài có tồn tại không
    const attempt = await ExamAttempt.findById(id);
    if (!attempt) {
        return res.status(404).json({ msg: MESSAGES.EXAM_ATTEMPT.NOT_FOUND });
    }

    // Kiểm tra quyền truy cập
    if (
        attempt.student.toString() !== req.user._id.toString() &&
        !req.user.role.includes(ROLES.TEACHER) &&
        !req.user.role.includes(ROLES.ADMIN)
    ) {
        return res.status(403).json({ msg: MESSAGES.EXAM.NO_PERMISSION_VIEW_RESULTS });
    }

    // Lấy thông tin đề thi
    const exam = await Exam.findById(attempt.exam);
    if (!exam) {
        return res.status(404).json({ msg: MESSAGES.EXAM.NOT_FOUND });
    }

    // Lấy thông tin chi tiết các câu hỏi
    const questionIds = attempt.questions.map(q => q.question);
    const questionsDetail = await Question.find({ _id: { $in: questionIds } });

    // Kết hợp thông tin
    const questionsWithAnswers = attempt.questions.map(q => {
        const questionDetail = questionsDetail.find(
            detail => detail._id.toString() === q.question.toString()
        );

        return {
            _id: q.question,
            content: questionDetail.content,
            options: questionDetail.options,
            selectedOption: q.selectedOption,
            correctOption: questionDetail.correctOption,
            isCorrect: q.isCorrect,
            explanation: questionDetail.explanation
        };
    });

    res.json({
        success: true,
        data: {
            exam: {
                _id: exam._id,
                title: exam.title,
                subject: exam.subject,
                timeLimit: exam.timeLimit,
                questionsPerAttempt: exam.questionsPerAttempt
            },
            attempt: {
                _id: attempt._id,
                startTime: attempt.startTime,
                endTime: attempt.endTime,
                isCompleted: attempt.isCompleted,
                score: attempt.score,
                totalCorrect: attempt.totalCorrect,
                totalIncorrect: attempt.totalIncorrect,
                totalUnanswered: attempt.totalUnanswered
            },
            questions: questionsWithAnswers
        }
    });
});

// @desc    Lấy danh sách kết quả làm bài của học sinh
// @route   GET /api/exams/:id/results
// @access  Private (giáo viên)
exports.getExamResults = asyncHandler(async (req, res) => {
    const { id } = req.params;

    // Kiểm tra đề thi có tồn tại không
    const exam = await Exam.findById(id);
    if (!exam) {
        return res.status(404).json({ msg: MESSAGES.EXAM.NOT_FOUND });
    }

    // Nếu là học sinh, chỉ xem kết quả của mình
    if (req.user.role.includes(ROLES.STUDENT)) {
        const studentResults = await ExamResult.findOne({
            student: req.user._id,
            exam: id
        });

        if (!studentResults) {
            return res.json({
                success: true,
                data: {
                    totalAttempts: 0,
                    bestScore: 0,
                    averageScore: 0
                }
            });
        }

        // Lấy lần làm bài gần nhất
        const latestAttempt = await ExamAttempt.findOne({
            student: req.user._id,
            exam: id,
            isCompleted: true
        }).sort({ endTime: -1 });

        return res.json({
            success: true,
            data: {
                totalAttempts: studentResults.totalAttempts,
                bestScore: studentResults.bestScore,
                averageScore: studentResults.averageScore,
                latestAttempt: latestAttempt ? {
                    _id: latestAttempt._id,
                    score: latestAttempt.score,
                    endTime: latestAttempt.endTime
                } : null
            }
        });
    }

    // Nếu là giáo viên, lấy tất cả kết quả
    const results = await ExamResult.find({ exam: id })
        .populate('student', 'name studentId')
        .sort({ bestScore: -1 });

    // Tổng hợp thông tin
    const studentIds = results.map(r => r.student._id);

    // Lấy lần làm bài gần nhất của mỗi học sinh
    const latestAttempts = await Promise.all(
        studentIds.map(async (studentId) => {
            const attempt = await ExamAttempt.findOne({
                student: studentId,
                exam: id,
                isCompleted: true
            }).sort({ endTime: -1 });

            return attempt ? {
                studentId,
                attemptId: attempt._id,
                score: attempt.score,
                endTime: attempt.endTime
            } : null;
        })
    );

    // Kết hợp thông tin
    const detailedResults = results.map(r => {
        const latest = latestAttempts.find(
            a => a && a.studentId.toString() === r.student._id.toString()
        );

        return {
            student: {
                _id: r.student._id,
                name: r.student.name,
                studentId: r.student.studentId
            },
            totalAttempts: r.totalAttempts,
            bestScore: r.bestScore,
            averageScore: r.averageScore,
            lastAttemptDate: r.lastAttemptDate,
            latestAttempt: latest ? {
                _id: latest.attemptId,
                score: latest.score,
                endTime: latest.endTime
            } : null
        };
    });

    res.json({
        success: true,
        count: detailedResults.length,
        data: detailedResults
    });
});

// @desc    Lấy danh sách lần làm bài của học sinh
// @route   GET /api/students/:studentId/exam-attempts
// @access  Private
exports.getStudentAttempts = asyncHandler(async (req, res) => {
    const { studentId } = req.params;
    const { examId } = req.query;

    // Xác thực học sinh
    const student = await User.findById(studentId);
    if (!student) {
        return res.status(404).json({ msg: MESSAGES.STUDENT.NOT_FOUND });
    }

    // Kiểm tra quyền truy cập
    if (
        studentId !== req.user._id.toString() &&
        !req.user.role.includes(ROLES.TEACHER) &&
        !req.user.role.includes(ROLES.ADMIN)
    ) {
        return res.status(403).json({ msg: MESSAGES.EXAM.NO_PERMISSION_VIEW_INFO });
    }

    // Xây dựng truy vấn
    const query = { student: studentId, isCompleted: true };
    if (examId) {
        query.exam = examId;
    }
    if (req.query.completed !== undefined) {
        query.isCompleted = req.query.completed === 'true';
    }

    // Lấy danh sách các lần làm bài
    const attempts = await ExamAttempt.find(query)
        .populate({
            path: 'exam',
            select: 'title subject questionsPerAttempt',
            populate: { path: 'subject', select: 'name code' }
        })
        .sort({ endTime: -1 });

    res.json({
        success: true,
        count: attempts.length,
        data: attempts.map(a => ({
            _id: a._id,
            exam: {
                _id: a.exam._id,
                title: a.exam.title,
                subject: a.exam.subject
            },
            startTime: a.startTime,
            endTime: a.endTime,
            score: a.score,
            totalCorrect: a.totalCorrect,
            totalQuestions: a.questions.length
        }))
    });
});

// @desc    Phân công đề thi cho các lớp
// @route   PUT /api/exams/:id/assign-classes
// @access  Private (giáo viên)
exports.assignClassesToExam = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { classIds } = req.body;

    // Kiểm tra dữ liệu đầu vào
    if (!classIds || !Array.isArray(classIds)) {
        return res.status(400).json({ msg: MESSAGES.CLASS.ASSIGN_LIST_REQUIRED });
    }

    // Kiểm tra đề thi có tồn tại không
    const exam = await Exam.findById(id);
    if (!exam) {
        return res.status(404).json({ msg: MESSAGES.EXAM.NOT_FOUND });
    }

    // Chỉ người tạo hoặc admin mới được phân công
    if (exam.createdBy.toString() !== req.user._id.toString() && req.user.role !== ROLES.ADMIN) {
        return res.status(403).json({ msg: MESSAGES.EXAM.NO_PERMISSION_ASSIGN });
    }

    // Kiểm tra các lớp có tồn tại không
    const classes = await Class.find({ _id: { $in: classIds } });
    if (classes.length !== classIds.length) {
        return res.status(400).json({ msg: MESSAGES.CLASS.SOME_NOT_EXIST });
    }

    // Cập nhật danh sách lớp
    exam.classes = classIds;
    await exam.save();

    res.json({
        success: true,
        msg: MESSAGES.EXAM.ASSIGNED_CLASSES,
        data: {
            _id: exam._id,
            title: exam.title,
            classes: classIds
        }
    });
});

// @desc    Hủy phân công đề thi cho lớp
// @route   PUT /api/exams/:id/unassign-class/:classId
// @access  Private (giáo viên)
exports.unassignClassFromExam = asyncHandler(async (req, res) => {
    const { id, classId } = req.params;

    // Kiểm tra đề thi có tồn tại không
    const exam = await Exam.findById(id);
    if (!exam) {
        return res.status(404).json({ msg: MESSAGES.EXAM.NOT_FOUND });
    }

    // Chỉ người tạo hoặc admin mới được hủy phân công
    if (exam.createdBy.toString() !== req.user._id.toString() && req.user.role !== ROLES.ADMIN) {
        return res.status(403).json({ msg: MESSAGES.EXAM.NO_PERMISSION_UNASSIGN });
    }

    // Xóa lớp khỏi danh sách
    exam.classes = exam.classes.filter(c => c.toString() !== classId);
    await exam.save();

    res.json({
        success: true,
        msg: MESSAGES.EXAM.UNASSIGNED_CLASS,
        data: {
            _id: exam._id,
            title: exam.title,
            classes: exam.classes
        }
    });
});

// @desc    Lấy thống kê bài tập
// @route   GET /api/exams/statistics
// @access  Private
exports.getExamStatistics = asyncHandler(async (req, res) => {
    const { subjectId, classId } = req.query;

    // Xây dựng query
    const examQuery = {};

    // Lọc theo môn học nếu có
    if (subjectId) {
        examQuery.subject = subjectId;
    }

    // Nếu là học sinh, chỉ lấy đề thi của lớp đó
    if (req.user.role.includes(ROLES.STUDENT)) {
        // Lấy lớp của học sinh từ StudentEnrollment
        const StudentEnrollment = require('../models/StudentEnrollment');
        const enrollment = await StudentEnrollment.findOne({
            student: req.user._id,
            status: 'active'
        });

        if (enrollment) {
            examQuery.classes = enrollment.class;
        } else {
            // Nếu không có enrollment, không có đề thi nào
            examQuery.classes = null;
        }

        examQuery.isActive = true;

        // Thêm lọc theo thời gian nếu cần
        const currentDate = new Date();
        examQuery.startDate = { $lte: currentDate };
        examQuery.endDate = { $gte: currentDate };

        // Lấy tất cả đề thi phù hợp với học sinh
        const exams = await Exam.find(examQuery);
        const examIds = exams.map(exam => exam._id);

        // Lấy kết quả của học sinh
        const results = await ExamResult.find({
            student: req.user._id,
            exam: { $in: examIds }
        });

        // Lấy các lần làm bài đang dở
        const activeAttempts = await ExamAttempt.find({
            student: req.user._id,
            exam: { $in: examIds },
            isCompleted: false
        });

        // Tạo map để dễ truy cập kết quả
        const resultMap = {};
        results.forEach(result => {
            resultMap[result.exam.toString()] = result;
        });

        // Tạo map các lần làm bài đang dở
        const activeAttemptsMap = {};
        activeAttempts.forEach(attempt => {
            activeAttemptsMap[attempt.exam.toString()] = attempt;
        });

        // Tính toán thống kê
        const examStatistics = exams.map(exam => {
            const result = resultMap[exam._id.toString()];
            const activeAttempt = activeAttemptsMap[exam._id.toString()];

            return {
                examId: exam._id,
                title: exam.title,
                subject: exam.subject,
                totalAttempts: result ? result.totalAttempts : 0,
                bestScore: result ? result.bestScore : 0,
                hasActiveAttempt: !!activeAttempt,
                activeAttemptId: activeAttempt ? activeAttempt._id : null,
                completed: result ? result.totalAttempts > 0 : false
            };
        });

        // Tổng hợp thống kê
        const totalExams = examStatistics.length;
        const completedExams = examStatistics.filter(stat => stat.completed).length;
        const inProgressExams = examStatistics.filter(stat => stat.hasActiveAttempt && !stat.completed).length;
        const notStartedExams = totalExams - completedExams - inProgressExams;

        return res.json({
            success: true,
            data: {
                summary: {
                    totalExams,
                    completedExams,
                    inProgressExams,
                    notStartedExams,
                    completionRate: totalExams > 0 ? (completedExams / totalExams * 100).toFixed(2) : 0
                },
                examStatistics
            }
        });
    }
    // Nếu là giáo viên hoặc admin
    else if (req.user.role.includes(ROLES.TEACHER) || req.user.role.includes(ROLES.ADMIN)) {
        // Lọc theo lớp nếu có
        if (classId) {
            examQuery.classes = classId;
        }

        // Lấy tất cả đề thi phù hợp với điều kiện
        const exams = await Exam.find(examQuery)
            .populate('subject', 'name code')
            .sort({ createdAt: -1 });

        // Nếu có lọc theo lớp, lấy danh sách học sinh của lớp đó từ StudentEnrollment
        let students = [];
        if (classId) {
            const StudentEnrollment = require('../models/StudentEnrollment');
            const { getCurrentSchoolYear } = require('../utils/schoolYear');
            
            const currentSchoolYear = getCurrentSchoolYear();
            
            // Lấy danh sách học sinh từ StudentEnrollment
            const enrollments = await StudentEnrollment.find({
                class: classId,
                schoolYear: currentSchoolYear,
                status: 'active'
            }).populate('student', '_id name studentId');
            
            students = enrollments.map(enrollment => enrollment.student).filter(student => student);
        }

        // Tạo thống kê cho từng đề thi
        const examStatistics = await Promise.all(exams.map(async (exam) => {
            // Đếm số học sinh đã làm đề thi này
            const studentAttempts = await ExamResult.countDocuments({ exam: exam._id });

            // Nếu có lọc theo lớp, tính chi tiết cho từng học sinh
            let studentDetails = [];
            if (classId && students.length > 0) {
                // Lấy kết quả của tất cả học sinh trong lớp cho đề thi này
                const results = await ExamResult.find({
                    exam: exam._id,
                    student: { $in: students.map(s => s._id) }
                });

                // Tạo map để dễ truy cập kết quả
                const resultMap = {};
                results.forEach(result => {
                    resultMap[result.student.toString()] = result;
                });

                // Tạo thống kê cho từng học sinh
                studentDetails = students.map(student => {
                    const result = resultMap[student._id.toString()];
                    return {
                        studentId: student._id,
                        name: student.name,
                        code: student.studentId,
                        hasAttempted: !!result,
                        totalAttempts: result ? result.totalAttempts : 0,
                        bestScore: result ? result.bestScore : 0
                    };
                });
            }

            return {
                examId: exam._id,
                title: exam.title,
                subject: exam.subject,
                totalQuestions: exam.totalQuestions,
                questionsPerAttempt: exam.questionsPerAttempt,
                totalStudentAttempts: studentAttempts,
                completionRate: classId && students.length > 0 ?
                    ((studentDetails.filter(s => s.hasAttempted).length / students.length) * 100).toFixed(2) : 0,
                studentDetails: studentDetails
            };
        }));

        return res.json({
            success: true,
            count: exams.length,
            data: {
                examStatistics,
                totalExams: exams.length,
                totalStudents: classId ? students.length : null
            }
        });
    }
    
    // Nếu không phải student, teacher, hoặc admin
    return res.status(403).json({
        success: false,
        msg: MESSAGES.ERROR.FORBIDDEN
    });
});