/**
 * T<PERSON><PERSON> hợp các hằng số liên quan đến vi phạm nội quy trong ứng dụng
 * Giúp thống nhất cách sử dụng và dễ dàng thay đổi sau này
 */

// Các loại vi phạm nội quy
const VIOLATION_TYPES = {
  ABSENT: 'absent',                    // Vắng học
  LATE: 'late',                       // Đi muộn
  UNIFORM: 'uniform',                 // Vi phạm đồng phục
  BEHAVIOR: 'behavior',               // Vi phạm hành vi
  HOMEWORK: 'homework',               // Không làm bài tập
  PHONE: 'phone',                     // Sử dụng điện thoại
  FIGHTING: 'fighting',               // Đánh nhau
  SMOKING: 'smoking',                 // Hút thuốc
  CHEATING: 'cheating',               // Gian lận
  DISRESPECT: 'disrespect',           // Thiếu tôn trọng
  VANDALISM: 'vandalism',             // Ph<PERSON> hoại tài sản
  BULLYING: 'bullying',               // Bắt nạt
  SKIP_CLASS: 'skip_class',           // Trốn tiết
  NOISE: 'noise',                     // Gây ồn ào
  EATING: 'eating',                   // Ăn uống trong lớp
  OTHER: 'other'                      // Khác
};

// Tên hiển thị cho các loại vi phạm (tiếng Việt)
const VIOLATION_TYPE_LABELS = {
  [VIOLATION_TYPES.ABSENT]: 'Vắng học',
  [VIOLATION_TYPES.LATE]: 'Đi muộn',
  [VIOLATION_TYPES.UNIFORM]: 'Vi phạm đồng phục',
  [VIOLATION_TYPES.BEHAVIOR]: 'Vi phạm hành vi',
  [VIOLATION_TYPES.HOMEWORK]: 'Không làm bài tập',
  [VIOLATION_TYPES.PHONE]: 'Sử dụng điện thoại trong giờ học',
  [VIOLATION_TYPES.FIGHTING]: 'Đánh nhau',
  [VIOLATION_TYPES.SMOKING]: 'Hút thuốc',
  [VIOLATION_TYPES.CHEATING]: 'Gian lận trong thi cử',
  [VIOLATION_TYPES.DISRESPECT]: 'Thiếu tôn trọng thầy cô',
  [VIOLATION_TYPES.VANDALISM]: 'Phá hoại tài sản trường lớp',
  [VIOLATION_TYPES.BULLYING]: 'Bắt nạt bạn học',
  [VIOLATION_TYPES.SKIP_CLASS]: 'Trốn tiết học',
  [VIOLATION_TYPES.NOISE]: 'Gây ồn ào trong lớp',
  [VIOLATION_TYPES.EATING]: 'Ăn uống trong lớp học',
  [VIOLATION_TYPES.OTHER]: 'Vi phạm khác'
};

// Mức độ nghiêm trọng của vi phạm
const VIOLATION_SEVERITY = {
  LIGHT: 'light',         // Nhẹ (1-2 điểm)
  MEDIUM: 'medium',       // Trung bình (3-5 điểm)
  SERIOUS: 'serious',     // Nghiêm trọng (6-10 điểm)
  SEVERE: 'severe'        // Rất nghiêm trọng (11-20 điểm)
};

// Tên hiển thị cho mức độ nghiêm trọng
const VIOLATION_SEVERITY_LABELS = {
  [VIOLATION_SEVERITY.LIGHT]: 'Nhẹ',
  [VIOLATION_SEVERITY.MEDIUM]: 'Trung bình',
  [VIOLATION_SEVERITY.SERIOUS]: 'Nghiêm trọng',
  [VIOLATION_SEVERITY.SEVERE]: 'Rất nghiêm trọng'
};

// Số điểm bị trừ theo loại vi phạm
const VIOLATION_POINTS = {
  [VIOLATION_TYPES.ABSENT]: 2,
  [VIOLATION_TYPES.LATE]: 1,
  [VIOLATION_TYPES.UNIFORM]: 1,
  [VIOLATION_TYPES.BEHAVIOR]: 3,
  [VIOLATION_TYPES.HOMEWORK]: 1,
  [VIOLATION_TYPES.PHONE]: 2,
  [VIOLATION_TYPES.FIGHTING]: 10,
  [VIOLATION_TYPES.SMOKING]: 15,
  [VIOLATION_TYPES.CHEATING]: 8,
  [VIOLATION_TYPES.DISRESPECT]: 5,
  [VIOLATION_TYPES.VANDALISM]: 7,
  [VIOLATION_TYPES.BULLYING]: 8,
  [VIOLATION_TYPES.SKIP_CLASS]: 3,
  [VIOLATION_TYPES.NOISE]: 1,
  [VIOLATION_TYPES.EATING]: 1,
  [VIOLATION_TYPES.OTHER]: 2
};

// Trạng thái xử lý vi phạm
const VIOLATION_STATUS = {
  PENDING: 'pending',         // Chờ xử lý
  PROCESSED: 'processed',     // Đã xử lý
  APPEALED: 'appealed',       // Đang khiếu nại
  CANCELLED: 'cancelled'      // Đã hủy
};

// Tên hiển thị cho trạng thái xử lý
const VIOLATION_STATUS_LABELS = {
  [VIOLATION_STATUS.PENDING]: 'Chờ xử lý',
  [VIOLATION_STATUS.PROCESSED]: 'Đã xử lý',
  [VIOLATION_STATUS.APPEALED]: 'Đang khiếu nại',
  [VIOLATION_STATUS.CANCELLED]: 'Đã hủy'
};

// Điểm thi đua mặc định
const DEFAULT_CONDUCT_POINTS = 50;

// Điểm thi đua tối thiểu
const MIN_CONDUCT_POINTS = 0;

// Điểm thi đua tối đa
const MAX_CONDUCT_POINTS = 100;

// Phân loại hạnh kiểm theo điểm
const CONDUCT_CLASSIFICATION = {
  EXCELLENT: { min: 45, max: 50, label: 'Xuất sắc' },
  GOOD: { min: 35, max: 44, label: 'Tốt' },
  FAIR: { min: 25, max: 34, label: 'Khá' },
  AVERAGE: { min: 15, max: 24, label: 'Trung bình' },
  WEAK: { min: 0, max: 14, label: 'Yếu' }
};

// Thông báo mặc định cho vi phạm
const DEFAULT_VIOLATION_MESSAGES = {
  REPORT_TEMPLATE: 'BÁO HỌC SINH VI PHẠM NỘI QUY\nThông báo GVCN: {homeroomTeacher}\nHọ và tên HS: {studentName}\nLớp: {className}\nNội dung vi phạm: {violationType}\nNgày vi phạm: {violationDate}\nSố lần VPNQ: {totalViolations}\nSố điểm thi đua còn lại: {remainingPoints}\nĐề nghị Thầy Cô tiếp nhận và nhắc nhở học sinh.',
  NOTIFICATION_TITLE: 'Thông báo vi phạm nội quy',
  PARENT_NOTIFICATION: 'Con em bạn {studentName} lớp {className} đã vi phạm nội quy: {violationType} vào ngày {violationDate}. Hiện tại còn {remainingPoints} điểm thi đua.'
};

// Quyền hạn xử lý vi phạm
const VIOLATION_PERMISSIONS = {
  CREATE: ['admin', 'teacher'],
  VIEW: ['admin', 'teacher', 'student'],
  UPDATE: ['admin', 'teacher'],
  DELETE: ['admin'],
  APPEAL: ['student']
};

module.exports = {
  VIOLATION_TYPES,
  VIOLATION_TYPE_LABELS,
  VIOLATION_SEVERITY,
  VIOLATION_SEVERITY_LABELS,
  VIOLATION_POINTS,
  VIOLATION_STATUS,
  VIOLATION_STATUS_LABELS,
  DEFAULT_CONDUCT_POINTS,
  MIN_CONDUCT_POINTS,
  MAX_CONDUCT_POINTS,
  CONDUCT_CLASSIFICATION,
  DEFAULT_VIOLATION_MESSAGES,
  VIOLATION_PERMISSIONS
};
