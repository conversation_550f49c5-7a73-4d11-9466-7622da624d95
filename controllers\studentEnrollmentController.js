// controllers/studentEnrollmentController.js - Controller cho quản lý ghi danh học sinh
const asyncHandler = require('../middlewares/async');
const StudentEnrollmentService = require('../services/studentEnrollmentService');
const MESSAGES = require('../constants/messages');
const { ROLES } = require('../constants/roleConstants');
const User = require('../models/User');
const Class = require('../models/Class');
const StudentEnrollment = require('../models/StudentEnrollment');

// @desc    Ghi danh học sinh vào lớp
// @route   POST /api/enrollment
// @access  Private (admin, giáo viên chủ nhiệm)
exports.enrollStudent = asyncHandler(async (req, res) => {
  const { studentId, classId, schoolYear, group = 'A' } = req.body;

  // Chỉ admin và giáo viên mới có quyền ghi danh học sinh
  if (!req.user.role.includes(ROLES.ADMIN) && !req.user.role.includes(ROLES.TEACHER)) {
    return res.status(403).json({
      success: false,
      msg: MESSAGES.ERROR.FORBIDDEN
    });
  }

  // Kiểm tra dữ liệu đầu vào
  if (!studentId || !classId || !schoolYear) {
    return res.status(400).json({
      success: false,
      msg: MESSAGES.ERROR.VALIDATION_FAILED
    });
  }

  try {
    // Kiểm tra học sinh có tồn tại không
    const student = await User.findById(studentId);
    if (!student || !student.role.includes('student')) {
      return res.status(404).json({
        success: false,
        msg: MESSAGES.STUDENT.NOT_FOUND
      });
    }

    // Kiểm tra lớp có tồn tại không
    const classObj = await Class.findById(classId);
    if (!classObj) {
      return res.status(404).json({
        success: false,
        msg: MESSAGES.CLASS.NOT_FOUND
      });
    }

    // Kiểm tra năm học có hợp lệ không
    if (classObj.schoolYear !== schoolYear) {
      return res.status(400).json({
        success: false,
        msg: 'Năm học không khớp với lớp học'
      });
    }

    // Kiểm tra học sinh đã có ghi danh cho năm học này chưa
    const existingEnrollment = await StudentEnrollment.findOne({
      student: studentId,
      schoolYear
    });

    if (existingEnrollment) {
      return res.status(400).json({
        success: false,
        msg: 'Học sinh đã có ghi danh cho năm học này'
      });
    }

    // Tạo ghi danh mới
    const enrollment = await StudentEnrollmentService.enrollStudent(
      studentId,
      classId,
      schoolYear,
      group
    );

    res.status(201).json({
      success: true,
      msg: 'Ghi danh học sinh thành công',
      data: enrollment
    });

  } catch (error) {
    console.error('Error in enrollStudent:', error);
    res.status(500).json({
      success: false,
      msg: MESSAGES.ERROR.GENERAL
    });
  }
});

// @desc    Chuyển học sinh sang lớp khác
// @route   PUT /api/enrollment/transfer
// @access  Private (admin, giáo viên chủ nhiệm)
exports.transferStudent = asyncHandler(async (req, res) => {
  const { studentId, newClassId, schoolYear, reason } = req.body;

  // Kiểm tra quyền truy cập
  if (req.user.role !== ROLES.ADMIN && req.user.role !== ROLES.TEACHER) {
    return res.status(403).json({
      success: false,
      msg: MESSAGES.ERROR.FORBIDDEN
    });
  }

  try {
    const enrollment = await StudentEnrollmentService.transferStudent(
      studentId, 
      newClassId, 
      schoolYear, 
      reason
    );

    res.json({
      success: true,
      data: enrollment,
      msg: 'Chuyển lớp thành công'
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      msg: error.message
    });
  }
});

// @desc    Lấy danh sách học sinh của lớp
// @route   GET /api/enrollment/class/:classId
// @access  Private (tất cả người dùng đã đăng nhập)
exports.getClassStudents = asyncHandler(async (req, res) => {
  const { classId } = req.params;
  const { schoolYear } = req.query;

  // Chỉ admin và giáo viên mới có quyền xem danh sách học sinh
  if (!req.user.role.includes(ROLES.ADMIN) && !req.user.role.includes(ROLES.TEACHER)) {
    return res.status(403).json({
      success: false,
      msg: MESSAGES.ERROR.FORBIDDEN
    });
  }

  if (!schoolYear) {
    return res.status(400).json({
      success: false,
      msg: 'Thiếu thông tin năm học'
    });
  }

  try {
    const enrollments = await StudentEnrollmentService.getClassStudents(
      classId, 
      schoolYear
    );

    res.json({
      success: true,
      count: enrollments.length,
      data: enrollments
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      msg: error.message
    });
  }
});

// @desc    Lấy lớp hiện tại của học sinh
// @route   GET /api/enrollment/student/:studentId/current
// @access  Private (tất cả người dùng đã đăng nhập)
exports.getStudentCurrentClass = asyncHandler(async (req, res) => {
  const { studentId } = req.params;
  const { schoolYear } = req.query;

  if (!schoolYear) {
    return res.status(400).json({
      success: false,
      msg: 'Thiếu thông tin năm học'
    });
  }

  try {
    const enrollment = await StudentEnrollmentService.getStudentCurrentClass(
      studentId, 
      schoolYear
    );

    if (!enrollment) {
      return res.status(404).json({
        success: false,
        msg: 'Không tìm thấy lớp hiện tại của học sinh'
      });
    }

    res.json({
      success: true,
      data: enrollment
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      msg: error.message
    });
  }
});

// @desc    Lấy lịch sử học tập của học sinh
// @route   GET /api/enrollment/student/:studentId/history
// @access  Private (admin, giáo viên, chính học sinh đó)
exports.getStudentHistory = asyncHandler(async (req, res) => {
  const { studentId } = req.params;

  // Kiểm tra quyền truy cập
  if (req.user.role === ROLES.STUDENT && req.user.id !== studentId) {
    return res.status(403).json({
      success: false,
      msg: MESSAGES.ERROR.FORBIDDEN
    });
  }

  try {
    const history = await StudentEnrollmentService.getStudentHistory(studentId);

    res.json({
      success: true,
      count: history.length,
      data: history
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      msg: error.message
    });
  }
});

// @desc    Cập nhật trạng thái ghi danh
// @route   PUT /api/enrollment/status
// @access  Private (admin)
exports.updateEnrollmentStatus = asyncHandler(async (req, res) => {
  const { studentId, schoolYear, newStatus, reason } = req.body;

  // Chỉ admin mới được cập nhật trạng thái
  if (req.user.role !== ROLES.ADMIN) {
    return res.status(403).json({
      success: false,
      msg: MESSAGES.ERROR.FORBIDDEN
    });
  }

  try {
    const enrollment = await StudentEnrollmentService.updateEnrollmentStatus(
      studentId, 
      schoolYear, 
      newStatus, 
      reason
    );

    res.json({
      success: true,
      data: enrollment,
      msg: 'Cập nhật trạng thái thành công'
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      msg: error.message
    });
  }
});

// @desc    Lấy thống kê ghi danh theo lớp
// @route   GET /api/enrollment/stats/class/:classId
// @access  Private (admin, giáo viên)
exports.getClassEnrollmentStats = asyncHandler(async (req, res) => {
  const { classId } = req.params;
  const { schoolYear } = req.query;

  // Kiểm tra quyền truy cập
  if (req.user.role !== ROLES.ADMIN && req.user.role !== ROLES.TEACHER) {
    return res.status(403).json({
      success: false,
      msg: MESSAGES.ERROR.FORBIDDEN
    });
  }

  if (!schoolYear) {
    return res.status(400).json({
      success: false,
      msg: 'Thiếu thông tin năm học'
    });
  }

  try {
    const stats = await StudentEnrollmentService.getClassEnrollmentStats(
      classId, 
      schoolYear
    );

    res.json({
      success: true,
      data: stats
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      msg: error.message
    });
  }
});

// @desc    Lấy số lượng học sinh trong lớp
// @route   GET /api/enrollment/count/class/:classId
// @access  Private (tất cả người dùng đã đăng nhập)
exports.getStudentCount = asyncHandler(async (req, res) => {
  const { classId } = req.params;
  const { schoolYear, status = 'active' } = req.query;

  if (!schoolYear) {
    return res.status(400).json({
      success: false,
      msg: 'Thiếu thông tin năm học'
    });
  }

  try {
    const count = await StudentEnrollmentService.getStudentCount(
      classId, 
      schoolYear, 
      status
    );

    res.json({
      success: true,
      data: { count }
    });
  } catch (error) {
    res.status(400).json({
      success: false,
      msg: error.message
    });
  }
});

// @desc    Cập nhật thông tin ghi danh của học sinh
// @route   PUT /api/student-enrollment/:enrollmentId
// @access  Private (Admin, giáo viên)
exports.updateStudentEnrollment = asyncHandler(async (req, res) => {
  const { enrollmentId } = req.params;
  const { classId, group, status } = req.body;

  // Chỉ admin và giáo viên mới có quyền cập nhật ghi danh
  if (!req.user.role.includes(ROLES.ADMIN) && !req.user.role.includes(ROLES.TEACHER)) {
    return res.status(403).json({
      success: false,
      msg: MESSAGES.ERROR.FORBIDDEN
    });
  }

  try {
    // Kiểm tra ghi danh có tồn tại không
    const enrollment = await StudentEnrollment.findById(enrollmentId)
      .populate('student', 'name studentId')
      .populate('class', 'name schoolYear');

    if (!enrollment) {
      return res.status(404).json({
        success: false,
        msg: 'Không tìm thấy thông tin ghi danh'
      });
    }

    // Cập nhật thông tin ghi danh
    if (classId && classId !== enrollment.class._id.toString()) {
      // Kiểm tra lớp mới có tồn tại không
      const newClass = await Class.findById(classId);
      if (!newClass) {
        return res.status(404).json({
          success: false,
          msg: MESSAGES.CLASS.NOT_FOUND
        });
      }

      // Kiểm tra năm học có khớp không
      if (newClass.schoolYear !== enrollment.schoolYear) {
        return res.status(400).json({
          success: false,
          msg: 'Năm học của lớp mới không khớp với ghi danh hiện tại'
        });
      }

      enrollment.class = classId;
    }

    if (group) enrollment.group = group;
    if (status) enrollment.status = status;

    enrollment.updatedAt = Date.now();
    await enrollment.save();

    // Populate lại thông tin sau khi cập nhật
    await enrollment.populate('student', 'name studentId');
    await enrollment.populate('class', 'name schoolYear');

    res.json({
      success: true,
      msg: 'Cập nhật thông tin ghi danh thành công',
      data: enrollment
    });

  } catch (error) {
    console.error('Error in updateStudentEnrollment:', error);
    res.status(500).json({
      success: false,
      msg: MESSAGES.ERROR.GENERAL
    });
  }
});

// @desc    Lấy danh sách ghi danh của một học sinh
// @route   GET /api/student-enrollment/student/:studentId
// @access  Private (Admin, giáo viên, học sinh chỉ xem được của mình)
exports.getStudentEnrollments = asyncHandler(async (req, res) => {
  const { studentId } = req.params;

  // Kiểm tra quyền truy cập
  if (req.user.role.includes(ROLES.STUDENT) && req.user.id !== studentId) {
    return res.status(403).json({
      success: false,
      msg: MESSAGES.ERROR.FORBIDDEN
    });
  }

  try {
    const enrollments = await StudentEnrollmentService.getStudentEnrollments(studentId);

    res.json({
      success: true,
      count: enrollments.length,
      data: enrollments
    });

  } catch (error) {
    console.error('Error in getStudentEnrollments:', error);
    res.status(500).json({
      success: false,
      msg: MESSAGES.ERROR.GENERAL
    });
  }
});

// @desc    Xóa ghi danh của học sinh
// @route   DELETE /api/student-enrollment/:enrollmentId
// @access  Private (Admin only)
exports.deleteStudentEnrollment = asyncHandler(async (req, res) => {
  const { enrollmentId } = req.params;

  // Chỉ admin mới có quyền xóa ghi danh
  if (!req.user.role.includes(ROLES.ADMIN)) {
    return res.status(403).json({
      success: false,
      msg: MESSAGES.ERROR.FORBIDDEN
    });
  }

  try {
    const enrollment = await StudentEnrollment.findById(enrollmentId);

    if (!enrollment) {
      return res.status(404).json({
        success: false,
        msg: 'Không tìm thấy thông tin ghi danh'
      });
    }

    await StudentEnrollment.findByIdAndDelete(enrollmentId);

    res.json({
      success: true,
      msg: 'Xóa ghi danh thành công',
      data: {}
    });

  } catch (error) {
    console.error('Error in deleteStudentEnrollment:', error);
    res.status(500).json({
      success: false,
      msg: MESSAGES.ERROR.GENERAL
    });
  }
});
