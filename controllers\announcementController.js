const asyncHandler = require('express-async-handler');
const Announcement = require('../models/Announcement');
const AnnouncementRead = require('../models/AnnouncementRead');
const AnnouncementBookmark = require('../models/AnnouncementBookmark');
const User = require('../models/User');
const Class = require('../models/Class');
const StudentEnrollment = require('../models/StudentEnrollment');
const MESSAGES = require('../constants/messages');
const ROLES = require('../constants/roleConstants');
const { HTTP_STATUS, createSuccessResponse, createErrorResponse } = require('../constants/httpConstants');
const {
  ANNOUNCEMENT_TYPES,
  ANNOUNCEMENT_STATUS,
  DEPARTMENTS,
  ANNOUNCEMENT_TYPE_LABELS,
  ANNOUNCEMENT_STATUS_LABELS
} = require('../constants/announcementConstants');
const { TEACHER_SPECIFIC_ROLES } = require('../constants/userConstants');
const { VALID_DEPARTMENT_GROUPS } = require('../constants/departmentConstants');
const { sendImmediateZaloMessage } = require('../jobs/zaloMessageScheduler');
const AnnouncementReply = require('../models/AnnouncementReply');

// @desc    Tạo thông báo mới
// @access  Private (yêu cầu x-auth-token)
exports.createAnnouncement = asyncHandler(async (req, res) => {
  const {
    title,
    type,
    content,
    recipients,
    zaloConfig,
    status = ANNOUNCEMENT_STATUS.DRAFT
  } = req.body;

  // Kiểm tra các trường bắt buộc
  if (!title || !type || !content) {
    return res.status(HTTP_STATUS.BAD_REQUEST).json(
      createErrorResponse(MESSAGES.ERROR.VALIDATION_FAILED)
    );
  }

  // Kiểm tra độ dài title
  if (title.length > 200) {
    return res.status(HTTP_STATUS.BAD_REQUEST).json(
      createErrorResponse(MESSAGES.ANNOUNCEMENT.TITLE_TOO_LONG)
    );
  }

  // Kiểm tra req.user có tồn tại
  if (!req.user || !req.user._id) {
    return res.status(HTTP_STATUS.UNAUTHORIZED).json(
      createErrorResponse(MESSAGES.ERROR.UNAUTHORIZED)
    );
  }

  // Kiểm tra quyền tạo thông báo dựa trên type
  const hasPermission = await checkAnnouncementPermission(req.user, type);
  if (!hasPermission) {
    return res.status(HTTP_STATUS.FORBIDDEN).json(
      createErrorResponse(MESSAGES.ERROR.FORBIDDEN)
    );
  }

  // Validate recipients dựa trên type
  const validationResult = validateRecipients(type, recipients);
  if (!validationResult.isValid) {
    return res.status(HTTP_STATUS.BAD_REQUEST).json(
      createErrorResponse(validationResult.message)
    );
  }

  // Tự động set status thành 'sent' nếu không sử dụng Zalo
  let finalStatus = status;
  if (!zaloConfig || !zaloConfig.enabled) {
    finalStatus = ANNOUNCEMENT_STATUS.SENT;
  }

  const announcement = new Announcement({
    sender: req.user._id,
    title: title.trim(),
    type,
    recipients,
    content,
    zaloConfig: zaloConfig || {},
    status: finalStatus,
  });

  await announcement.save();

  // Populate sender information
  await announcement.populate('sender', 'name role specificRole');

  res.status(201).json(announcement);
});

// @desc    Lấy danh sách thông báo (phân trang)
// @access  Private (yêu cầu x-auth-token)
exports.getAnnouncements = asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    type,
    classId,
    department,
    status,
    startDate,
    endDate,
    mode = 'received' // 'received' hoặc 'sent'
  } = req.query;

  // Xây dựng query dựa trên role và quyền của user
  const query = await buildAnnouncementQuery(req.user, {
    type,
    classId,
    department,
    status,
    startDate,
    endDate,
    mode
  });

  const announcements = await Announcement.find(query)
    .populate('sender', 'name role specificRole')
    .populate('recipients.class', 'name')
    .populate('recipients.teachers', 'name role')
    .sort({ createdAt: -1 })
    .skip((page - 1) * limit)
    .limit(parseInt(limit));

  // Thêm thông tin trạng thái đọc, bookmark và labels cho từng thông báo
  const announcementsWithReadStatus = await Promise.all(
    announcements.map(async (announcement) => {
      // Logic isRead dựa trên mode và sender
      let isRead = null;
      if (mode === 'received') {
        // Mode received: luôn kiểm tra isRead
        isRead = await announcement.isReadByUser(req.user._id);
      } else if (mode === 'all') {
        // Mode all: chỉ kiểm tra isRead cho thông báo không phải mình gửi
        if (announcement.sender && announcement.sender.toString() !== req.user._id.toString()) {
          isRead = await announcement.isReadByUser(req.user._id);
        }
        // Thông báo mình gửi thì isRead = null (không cần thiết)
      }
      // Mode sent: isRead = null (không cần thiết vì là người gửi)

      const isBookmarked = await AnnouncementBookmark.isBookmarked(req.user._id, announcement._id);

      return {
        ...announcement.toObject(),
        sender: announcement.sender || { name: 'Người dùng đã bị xóa', role: [], specificRole: null },
        isRead,
        isBookmarked,
        typeLabel: ANNOUNCEMENT_TYPE_LABELS[announcement.type],
        statusLabel: ANNOUNCEMENT_STATUS_LABELS[announcement.status]
      };
    })
  );

  const totalAnnouncements = await Announcement.countDocuments(query);
  const totalPages = Math.ceil(totalAnnouncements / limit);

  res.json({
    announcements: announcementsWithReadStatus,
    currentPage: parseInt(page),
    totalPages,
    totalAnnouncements,
    mode
  });
});

// @desc    Lấy danh sách thông báo mình đã gửi
// @access  Private (yêu cầu x-auth-token)
exports.getSentAnnouncements = asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    type,
    status,
    startDate,
    endDate
  } = req.query;

  // Query để lấy thông báo mình đã gửi
  let query = { sender: req.user._id };

  // Thêm filter theo type nếu có
  if (type) {
    query.type = type;
  }

  // Thêm filter theo status nếu có
  if (status) {
    query.status = status;
  }

  // Thêm filter theo ngày tạo
  if (startDate || endDate) {
    query.createdAt = {};
    if (startDate) {
      const start = new Date(startDate);
      start.setHours(0, 0, 0, 0);
      query.createdAt.$gte = start;
    }
    if (endDate) {
      const end = new Date(endDate);
      end.setHours(23, 59, 59, 999);
      query.createdAt.$lte = end;
    }
  }

  const announcements = await Announcement.find(query)
    .populate('sender', 'name role specificRole')
    .populate('recipients.class', 'name')
    .populate('recipients.teachers', 'name role')
    .sort({ createdAt: -1 })
    .skip((page - 1) * limit)
    .limit(parseInt(limit));

  // Thêm labels và thống kê đọc cho từng thông báo
  const announcementsWithLabels = await Promise.all(
    announcements.map(async (announcement) => {
      // Lấy thống kê đọc cho thông báo này
      const readStats = await AnnouncementRead.getReadStats(announcement._id);

      return {
        ...announcement.toObject(),
        sender: announcement.sender || { name: 'Người dùng đã bị xóa', role: [], specificRole: null },
        typeLabel: ANNOUNCEMENT_TYPE_LABELS[announcement.type],
        statusLabel: ANNOUNCEMENT_STATUS_LABELS[announcement.status],
        readStats: {
          totalRead: readStats.totalRead || 0,
          totalUnread: readStats.totalUnread || 0,
          totalRecipients: (readStats.totalRead || 0) + (readStats.totalUnread || 0),
          readPercentage: readStats.totalRead && (readStats.totalRead + readStats.totalUnread) > 0
            ? Math.round((readStats.totalRead / (readStats.totalRead + readStats.totalUnread)) * 100)
            : 0
        }
      };
    })
  );

  const totalAnnouncements = await Announcement.countDocuments(query);
  const totalPages = Math.ceil(totalAnnouncements / limit);

  res.json({
    announcements: announcementsWithLabels,
    currentPage: parseInt(page),
    totalPages,
    totalAnnouncements,
    mode: 'sent'
  });
});

// @desc    Lấy thông báo mới nhất
// @access  Private (yêu cầu x-auth-token)
exports.getRecentAnnouncements = asyncHandler(async (req, res) => {
  const { limit = 5, classId } = req.query;

  // Xây dựng query dựa trên role và quyền của user
  const query = await buildAnnouncementQuery(req.user, { classId });

  const announcements = await Announcement.find(query)
    .populate('sender', 'name role specificRole')
    .populate('recipients.class', 'name')
    .sort({ createdAt: -1 })
    .limit(parseInt(limit));

  // Thêm thông tin trạng thái đọc và labels cho từng thông báo
  const announcementsWithReadStatus = await Promise.all(
    announcements.map(async (announcement) => {
      const isRead = await announcement.isReadByUser(req.user._id);
      return {
        ...announcement.toObject(),
        sender: announcement.sender || { name: 'Người dùng đã bị xóa', role: [], specificRole: null },
        isRead,
        typeLabel: ANNOUNCEMENT_TYPE_LABELS[announcement.type],
        statusLabel: ANNOUNCEMENT_STATUS_LABELS[announcement.status]
      };
    })
  );

  res.json(announcementsWithReadStatus);
});

// @desc    Lấy chi tiết thông báo
// @access  Private (yêu cầu x-auth-token)
exports.getAnnouncementById = asyncHandler(async (req, res) => {
  const announcement = await Announcement.findById(req.params.id)
    .populate('sender', 'name role specificRole')
    .populate('recipients.class', 'name')
    .populate('recipients.teachers', 'name role');

  if (!announcement) {
    return res.status(404).json({ msg: MESSAGES.ERROR.NOT_FOUND });
  }

  // Kiểm tra quyền xem thông báo
  const hasPermission = await checkViewPermission(req.user, announcement);
  if (!hasPermission) {
    return res.status(403).json({ msg: MESSAGES.ERROR.FORBIDDEN });
  }

  // Tự động đánh dấu đã đọc khi xem chi tiết
  await announcement.markAsReadByUser(req.user._id);

  // Thêm thông tin trạng thái đọc, bookmark và labels
  const isRead = await announcement.isReadByUser(req.user._id);
  const isBookmarked = await AnnouncementBookmark.isBookmarked(req.user._id, announcement._id);

  const announcementWithReadStatus = {
    ...announcement.toObject(),
    sender: announcement.sender || { name: 'Người dùng đã bị xóa', role: [], specificRole: null },
    isRead,
    isBookmarked,
    typeLabel: ANNOUNCEMENT_TYPE_LABELS[announcement.type],
    statusLabel: ANNOUNCEMENT_STATUS_LABELS[announcement.status]
  };

  res.json(announcementWithReadStatus);
});

// @desc    Cập nhật thông báo
// @access  Private (người tạo thông báo hoặc admin)
exports.updateAnnouncement = asyncHandler(async (req, res) => {
  let announcement = await Announcement.findById(req.params.id);

  if (!announcement) {
    return res.status(404).json({
      success: false,
      msg: MESSAGES.ANNOUNCEMENT.NOT_FOUND
    });
  }

  // Kiểm tra quyền sửa đổi
  if (announcement.sender && announcement.sender.toString() !== req.user._id.toString() && !req.user.role.includes(ROLES.ADMIN)) {
    return res.status(403).json({
      success: false,
      msg: MESSAGES.ERROR.FORBIDDEN
    });
  }

  const { title, content, recipients, zaloConfig, status } = req.body;

  // Validate title nếu có thay đổi
  if (title !== undefined) {
    if (!title || title.trim().length === 0) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(
        createErrorResponse(MESSAGES.ANNOUNCEMENT.TITLE_REQUIRED)
      );
    }
    if (title.length > 200) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(
        createErrorResponse(MESSAGES.ANNOUNCEMENT.TITLE_TOO_LONG)
      );
    }
    announcement.title = title.trim();
  }

  // Validate recipients nếu có thay đổi
  if (recipients) {
    const validationResult = validateRecipients(announcement.type, recipients);
    if (!validationResult.isValid) {
      return res.status(HTTP_STATUS.BAD_REQUEST).json(
        createErrorResponse(validationResult.message)
      );
    }
    announcement.recipients = recipients;
  }

  if (content) announcement.content = content;
  if (zaloConfig) {
    announcement.zaloConfig = { ...announcement.zaloConfig, ...zaloConfig };

    // Tự động set status thành 'sent' nếu tắt Zalo
    if (zaloConfig.enabled === false) {
      announcement.status = ANNOUNCEMENT_STATUS.SENT;
    }
  }
  if (status) {
    // Chỉ cho phép set status khác 'sent' nếu Zalo được bật
    if (status !== ANNOUNCEMENT_STATUS.SENT || !announcement.zaloConfig.enabled) {
      announcement.status = status;
    }
  }
  announcement.updatedAt = Date.now();

  await announcement.save();
  await announcement.populate('sender', 'name role specificRole');

  res.json(announcement);
});

// @desc    Xóa thông báo
// @access  Private (người tạo thông báo hoặc admin)
exports.deleteAnnouncement = asyncHandler(async (req, res) => {
  const announcement = await Announcement.findById(req.params.id);

  if (!announcement) {
    return res.status(404).json({
      success: false,
      msg: MESSAGES.ANNOUNCEMENT.NOT_FOUND
    });
  }

  // Kiểm tra quyền xóa
  if (announcement.sender && announcement.sender.toString() !== req.user._id.toString() && !req.user.role.includes(ROLES.ADMIN)) {
    return res.status(403).json({
      success: false,
      msg: MESSAGES.ERROR.FORBIDDEN
    });
  }

  await Announcement.findByIdAndDelete(req.params.id);

  res.json(createSuccessResponse(null, MESSAGES.ANNOUNCEMENT.DELETED));
});

// @desc    Lấy danh sách loại thông báo và bộ môn
// @access  Private (yêu cầu x-auth-token)
exports.getAnnouncementConfig = asyncHandler(async (req, res) => {
  const config = {
    types: ANNOUNCEMENT_TYPES,
    typeLabels: ANNOUNCEMENT_TYPE_LABELS,
    statuses: ANNOUNCEMENT_STATUS,
    statusLabels: ANNOUNCEMENT_STATUS_LABELS,
    departments: VALID_DEPARTMENT_GROUPS
  };

  res.json(config);
});

// @desc    Lấy thống kê thông báo
// @access  Private (yêu cầu x-auth-token)
exports.getAnnouncementStats = asyncHandler(async (req, res) => {
  const { startDate, endDate, mode = 'all' } = req.query;

  let dateFilter = {};
  if (startDate && endDate) {
    dateFilter = {
      createdAt: {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      }
    };
  }

  // Xây dựng query dựa trên mode
  let baseFilter = {};

  if (mode === 'sent') {
    // Thống kê thông báo mình đã gửi (tất cả trạng thái)
    baseFilter = {
      sender: req.user._id,
      ...dateFilter
    };
  } else if (mode === 'received') {
    // Thống kê thông báo mình nhận được (chỉ trạng thái sent)
    const receivedQuery = await buildAnnouncementQuery(req.user, { mode: 'received' });
    baseFilter = {
      ...receivedQuery,
      status: 'sent', // Chỉ lấy thông báo đã gửi
      ...dateFilter
    };
  } else if (mode === 'all') {
    // Thống kê tất cả: cả mình gửi và mình nhận
    const receivedQuery = await buildAnnouncementQuery(req.user, { mode: 'received' });

    baseFilter = {
      $or: [
        // Thông báo mình gửi (tất cả trạng thái)
        { sender: req.user._id },
        // Thông báo mình nhận được (chỉ trạng thái sent)
        { ...receivedQuery, status: 'sent' }
      ],
      ...dateFilter
    };
  }

  // Thống kê theo type với labels
  const typeStats = await Announcement.aggregate([
    { $match: baseFilter },
    { $group: { _id: '$type', count: { $sum: 1 } } }
  ]);

  const typeStatsWithLabels = typeStats.map(stat => ({
    type: stat._id,
    typeLabel: ANNOUNCEMENT_TYPE_LABELS[stat._id],
    count: stat.count
  }));

  // Thống kê theo status với labels (chỉ có ý nghĩa với mode 'sent')
  let statusStatsWithLabels = [];
  if (mode === 'sent') {
    const statusStats = await Announcement.aggregate([
      { $match: baseFilter },
      { $group: { _id: '$status', count: { $sum: 1 } } }
    ]);

    statusStatsWithLabels = statusStats.map(stat => ({
      status: stat._id,
      statusLabel: ANNOUNCEMENT_STATUS_LABELS[stat._id],
      count: stat.count
    }));
  }
  // Với mode 'received' và 'all': bỏ thống kê status vì không có ý nghĩa
  // (received chắc chắn là 'sent', all cũng chủ yếu là 'sent')

  // Thống kê theo người gửi (chỉ khi mode = 'received' hoặc 'all')
  let senderStats = [];
  if (mode !== 'sent') {
    const senderStatsRaw = await Announcement.aggregate([
      { $match: baseFilter },
      {
        $lookup: {
          from: 'users',
          localField: 'sender',
          foreignField: '_id',
          as: 'senderInfo'
        }
      },
      { $unwind: '$senderInfo' },
      {
        $group: {
          _id: '$sender',
          count: { $sum: 1 },
          senderName: { $first: '$senderInfo.name' },
          senderRole: { $first: '$senderInfo.role' }
        }
      },
      { $sort: { count: -1 } },
      { $limit: 10 }
    ]);

    senderStats = senderStatsRaw.map(stat => ({
      senderId: stat._id,
      senderName: stat.senderName,
      senderRole: stat.senderRole,
      count: stat.count
    }));
  }

  // Tổng số thông báo
  const totalAnnouncements = await Announcement.countDocuments(baseFilter);

  // Thêm thống kê chi tiết cho mode 'all'
  let breakdown = null;
  if (mode === 'all') {
    // Đếm riêng số thông báo đã gửi
    const sentFilter = {
      sender: req.user._id,
      ...dateFilter
    };
    const sentCount = await Announcement.countDocuments(sentFilter);

    // Đếm riêng số thông báo nhận được
    const receivedQuery = await buildAnnouncementQuery(req.user, { mode: 'received' });
    const receivedFilter = {
      ...receivedQuery,
      status: 'sent',
      ...dateFilter
    };
    const receivedCount = await Announcement.countDocuments(receivedFilter);

    breakdown = {
      sent: sentCount,
      received: receivedCount,
      note: sentCount + receivedCount !== totalAnnouncements ?
        'Có thể có thông báo trùng lặp (vừa gửi vừa nhận)' : null
    };
  }

  const response = {
    total: totalAnnouncements,
    mode,
    byType: typeStatsWithLabels,
    byStatus: statusStatsWithLabels,
    bySender: senderStats,
    period: startDate && endDate ? { startDate, endDate } : null
  };

  // Thêm breakdown nếu có
  if (breakdown) {
    response.breakdown = breakdown;
  }

  res.json(response);
});

// @desc    Lấy nhóm Zalo của user cho announcement
// @access  Private (yêu cầu x-auth-token)
exports.getUserZaloGroupsForAnnouncement = asyncHandler(async (req, res) => {
  const user = await User.findById(req.user._id).select('groupZaloIds');

  const zaloGroups = user.groupZaloIds || [];

  res.json({
    success: true,
    zaloGroups: zaloGroups.map(group => ({
      groupId: group.groupId,
      groupName: group.groupName,
      addedAt: group.addedAt
    }))
  });
});

// @desc    Gửi thông báo qua Zalo ngay lập tức
// @access  Private (yêu cầu x-auth-token)
exports.sendZaloMessage = asyncHandler(async (req, res) => {
  const announcement = await Announcement.findById(req.params.id);

  if (!announcement) {
    return res.status(HTTP_STATUS.NOT_FOUND).json(
      createErrorResponse(MESSAGES.ERROR.NOT_FOUND)
    );
  }

  // Kiểm tra quyền gửi thông báo (chỉ người tạo hoặc admin)
  if (announcement.sender && announcement.sender.toString() !== req.user._id.toString() && !req.user.role.includes(ROLES.ADMIN)) {
    return res.status(HTTP_STATUS.FORBIDDEN).json(
      createErrorResponse(MESSAGES.ERROR.FORBIDDEN)
    );
  }

  // Kiểm tra cấu hình Zalo
  if (!announcement.zaloConfig.enabled || !announcement.zaloConfig.groupId) {
    return res.status(HTTP_STATUS.BAD_REQUEST).json(
      createErrorResponse(MESSAGES.ANNOUNCEMENT.ZALO_NOT_CONFIGURED)
    );
  }

  // Kiểm tra đã gửi chưa
  if (announcement.zaloConfig.sent) {
    return res.status(HTTP_STATUS.BAD_REQUEST).json(
      createErrorResponse(MESSAGES.ANNOUNCEMENT.ZALO_ALREADY_SENT)
    );
  }

  try {
    const result = await sendImmediateZaloMessage(announcement._id);
    res.json(createSuccessResponse(result, MESSAGES.ZALO.SEND_SUCCESS));
  } catch (error) {
    console.error('Error sending Zalo message:', error);
    res.status(500).json({
      msg: 'Lỗi khi gửi thông báo qua Zalo',
      error: error.message
    });
  }
});

// @desc    Đánh dấu thông báo đã đọc
// @access  Private (người nhận thông báo hoặc admin)
exports.markAnnouncementAsRead = asyncHandler(async (req, res) => {
  const announcement = await Announcement.findById(req.params.id);

  if (!announcement) {
    return res.status(404).json({
      success: false,
      msg: MESSAGES.ANNOUNCEMENT.NOT_FOUND
    });
  }

  // Kiểm tra đã đọc chưa
  const isAlreadyRead = await announcement.isReadByUser(req.user._id);
  if (isAlreadyRead) {
    return res.json(createSuccessResponse(null, MESSAGES.ANNOUNCEMENT.ALREADY_READ));
  }

  // Đánh dấu đã đọc
  await announcement.markAsReadByUser(req.user._id);

  res.json(createSuccessResponse(null, MESSAGES.ANNOUNCEMENT.MARKED_AS_READ));
});

// @desc    Lấy thống kê đọc thông báo
// @access  Private (yêu cầu x-auth-token)
exports.getAnnouncementReadStatus = asyncHandler(async (req, res) => {
  const announcement = await Announcement.findById(req.params.id);

  if (!announcement) {
    return res.status(HTTP_STATUS.NOT_FOUND).json(
      createErrorResponse(MESSAGES.ANNOUNCEMENT.NOT_FOUND)
    );
  }

  // Chỉ người tạo hoặc admin mới có thể xem thống kê đọc
  if (announcement.sender && announcement.sender.toString() !== req.user._id.toString() && !req.user.role.includes(ROLES.ADMIN)) {
    return res.status(HTTP_STATUS.FORBIDDEN).json(
      createErrorResponse(MESSAGES.ERROR.FORBIDDEN)
    );
  }

  // Lấy thống kê đọc
  const readStats = await AnnouncementRead.getReadStats(announcement._id);

  // Lấy danh sách người đã đọc (với thông tin chi tiết)
  const readRecords = await AnnouncementRead.find({
    announcement: announcement._id,
    isRead: true
  })
    .populate('user', 'name role specificRole')
    .sort({ readAt: -1 });

  // Lọc bỏ các record có user null (user đã bị xóa)
  const validReadRecords = readRecords.filter(record => record.user !== null);

  // Lấy danh sách người chưa đọc (nếu có thể xác định được)
  let unreadUsers = [];
  let totalRecipients = readStats.totalRead + readStats.totalUnread;

  if (announcement.type === ANNOUNCEMENT_TYPES.TEACHER_TO_STUDENT && announcement.recipients.class) {
    // Lấy thông tin lớp để biết năm học
    const classInfo = await Class.findById(announcement.recipients.class);
    if (classInfo) {
      // Lấy danh sách học sinh trong lớp thông qua StudentEnrollment
      const enrollments = await StudentEnrollment.find({
        class: announcement.recipients.class,
        schoolYear: classInfo.schoolYear,
        status: 'active'
      }).populate('student', 'name studentId');
      
      // Lọc và map students, bỏ qua những student không tồn tại
      const classStudents = enrollments
        .filter(enrollment => enrollment.student)
        .map(enrollment => enrollment.student);

      const readUserIds = validReadRecords.map(record => record.user._id.toString());
      unreadUsers = classStudents.filter(student =>
        !readUserIds.includes(student._id.toString())
      );

      // Cập nhật totalRecipients và totalUnread dựa trên thực tế
      totalRecipients = validReadRecords.length + unreadUsers.length;
      const actualTotalUnread = unreadUsers.length;

      return res.json(createSuccessResponse({
        announcement: {
          _id: announcement._id,
          title: announcement.title,
          type: announcement.type,
          typeLabel: ANNOUNCEMENT_TYPE_LABELS[announcement.type],
          createdAt: announcement.createdAt,
          recipients: announcement.recipients
        },
        stats: {
          totalRead: validReadRecords.length,
          totalUnread: actualTotalUnread,
          totalRecipients: totalRecipients,
          readPercentage: totalRecipients > 0
            ? Math.round((validReadRecords.length / totalRecipients) * 100)
            : 0
        },
        readBy: validReadRecords.map(record => ({
          user: {
            ...record.user.toObject(),
            displayRole: record.user.role.includes(ROLES.STUDENT) ? 'Học sinh' : 'Giáo viên'
          },
          readAt: record.readAt,
          timeAgo: getTimeAgo(record.readAt)
        })),
        unreadUsers: unreadUsers.map(user => ({
          _id: user._id,
          name: user.name,
          studentId: user.studentId,
          displayRole: 'Học sinh'
        }))
      }, MESSAGES.ANNOUNCEMENT.READ_STATUS_RETRIEVED));
    }
  }

  // Cho các loại thông báo khác, sử dụng logic cũ
  return res.json(createSuccessResponse({
    announcement: {
      _id: announcement._id,
      title: announcement.title,
      type: announcement.type,
      typeLabel: ANNOUNCEMENT_TYPE_LABELS[announcement.type],
      createdAt: announcement.createdAt,
      recipients: announcement.recipients
    },
    stats: {
      totalRead: validReadRecords.length,
      totalUnread: readStats.totalUnread,
      totalRecipients: totalRecipients,
      readPercentage: totalRecipients > 0
        ? Math.round((validReadRecords.length / totalRecipients) * 100)
        : 0
    },
    readBy: validReadRecords.map(record => ({
      user: {
        ...record.user.toObject(),
        displayRole: record.user.role.includes(ROLES.STUDENT) ? 'Học sinh' : 'Giáo viên'
      },
      readAt: record.readAt,
      timeAgo: getTimeAgo(record.readAt)
    })),
    unreadUsers: []
  }, MESSAGES.ANNOUNCEMENT.READ_STATUS_RETRIEVED));
});

// @desc    Lấy danh sách thông báo chưa đọc của user
// @access  Private (yêu cầu x-auth-token)
exports.getUnreadAnnouncements = asyncHandler(async (req, res) => {
  const { limit = 10 } = req.query;

  // Xây dựng query để lấy thông báo user có quyền xem
  const query = await buildAnnouncementQuery(req.user, {});

  // Lấy tất cả thông báo user có quyền xem
  const allAnnouncements = await Announcement.find(query)
    .select('_id title type createdAt')
    .sort({ createdAt: -1 });

  const announcementIds = allAnnouncements.map(a => a._id);

  // Lấy danh sách thông báo chưa đọc
  const unreadIds = await AnnouncementRead.getUnreadAnnouncements(req.user._id, announcementIds);

  // Lấy thông tin chi tiết của thông báo chưa đọc với labels
  const unreadAnnouncements = allAnnouncements
    .filter(announcement => unreadIds.includes(announcement._id.toString()))
    .slice(0, parseInt(limit))
    .map(announcement => ({
      ...announcement.toObject(),
      typeLabel: ANNOUNCEMENT_TYPE_LABELS[announcement.type]
    }));

  res.json(createSuccessResponse({
    unreadCount: unreadIds.length,
    announcements: unreadAnnouncements
  }));
});

// @desc    Gửi nhắc nhở cho người chưa đọc thông báo
// @access  Private (yêu cầu x-auth-token)
exports.sendReadReminder = asyncHandler(async (req, res) => {
  const announcement = await Announcement.findById(req.params.id);

  if (!announcement) {
    return res.status(HTTP_STATUS.NOT_FOUND).json(
      createErrorResponse(MESSAGES.ANNOUNCEMENT.NOT_FOUND)
    );
  }

  // Chỉ người tạo hoặc admin mới có thể gửi nhắc nhở
  if (announcement.sender && announcement.sender.toString() !== req.user._id.toString() && !req.user.role.includes(ROLES.ADMIN)) {
    return res.status(HTTP_STATUS.FORBIDDEN).json(
      createErrorResponse(MESSAGES.ERROR.FORBIDDEN)
    );
  }

  // Chỉ gửi nhắc nhở cho thông báo đã gửi
  if (announcement.status !== ANNOUNCEMENT_STATUS.SENT) {
    return res.status(HTTP_STATUS.BAD_REQUEST).json(
      createErrorResponse('Chỉ có thể gửi nhắc nhở cho thông báo đã gửi')
    );
  }

  // Logic gửi nhắc nhở (có thể tích hợp với Zalo hoặc notification system)
  // Ở đây chỉ trả về thông báo thành công
  res.json(createSuccessResponse(null, 'Đã gửi nhắc nhở thành công'));
});

// @desc    Lấy dashboard analytics cho người gửi
// @access  Private (yêu cầu x-auth-token)
exports.getSenderDashboard = asyncHandler(async (req, res) => {
  const { period = '30' } = req.query; // 7, 30, 90 days

  const startDate = new Date();
  startDate.setDate(startDate.getDate() - parseInt(period));

  // Thống kê tổng quan
  const totalSent = await Announcement.countDocuments({
    sender: req.user._id,
    createdAt: { $gte: startDate }
  });

  const byStatus = await Announcement.aggregate([
    {
      $match: {
        sender: req.user._id,
        createdAt: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 }
      }
    }
  ]);

  const byType = await Announcement.aggregate([
    {
      $match: {
        sender: req.user._id,
        createdAt: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: '$type',
        count: { $sum: 1 }
      }
    }
  ]);

  // Thống kê đọc tổng hợp
  const readStatsAgg = await Announcement.aggregate([
    {
      $match: {
        sender: req.user._id,
        status: ANNOUNCEMENT_STATUS.SENT,
        createdAt: { $gte: startDate }
      }
    },
    {
      $lookup: {
        from: 'announcementreads',
        localField: '_id',
        foreignField: 'announcement',
        as: 'reads'
      }
    },
    {
      $project: {
        title: 1,
        createdAt: 1,
        totalReads: { $size: '$reads' },
        readUsers: {
          $filter: {
            input: '$reads',
            cond: { $eq: ['$$this.isRead', true] }
          }
        }
      }
    },
    {
      $project: {
        title: 1,
        createdAt: 1,
        totalReads: 1,
        actualReads: { $size: '$readUsers' }
      }
    }
  ]);

  const totalReads = readStatsAgg.reduce((sum, item) => sum + item.actualReads, 0);
  const avgReadRate = readStatsAgg.length > 0
    ? Math.round((totalReads / readStatsAgg.reduce((sum, item) => sum + item.totalReads, 0)) * 100)
    : 0;

  res.json(createSuccessResponse({
    period: parseInt(period),
    overview: {
      totalSent,
      totalReads,
      avgReadRate
    },
    byStatus: byStatus.map(stat => ({
      status: stat._id,
      statusLabel: ANNOUNCEMENT_STATUS_LABELS[stat._id],
      count: stat.count
    })),
    byType: byType.map(stat => ({
      type: stat._id,
      typeLabel: ANNOUNCEMENT_TYPE_LABELS[stat._id],
      count: stat.count
    })),
    recentActivity: readStatsAgg.slice(0, 10)
  }));
});

// @desc    Tìm kiếm thông báo nâng cao
// @access  Private (yêu cầu x-auth-token)
exports.searchAnnouncements = asyncHandler(async (req, res) => {
  const {
    q, // search query
    type,
    status,
    mode = 'received',
    startDate,
    endDate,
    page = 1,
    limit = 10
  } = req.query;

  // Xây dựng base query
  let baseQuery = await buildAnnouncementQuery(req.user, { mode });

  // Thêm text search
  if (q) {
    baseQuery = {
      ...baseQuery,
      $text: { $search: q }
    };
  }

  // Thêm filters
  if (type) {
    baseQuery.type = type;
  }
  if (status) {
    baseQuery.status = status;
  }
  if (startDate || endDate) {
    baseQuery.createdAt = {};
    if (startDate) baseQuery.createdAt.$gte = new Date(startDate);
    if (endDate) baseQuery.createdAt.$lte = new Date(endDate);
  }

  const announcements = await Announcement.find(baseQuery)
    .populate('sender', 'name role specificRole')
    .populate('recipients.class', 'name')
    .sort(q ? { score: { $meta: 'textScore' } } : { createdAt: -1 })
    .skip((page - 1) * limit)
    .limit(parseInt(limit));

  const total = await Announcement.countDocuments(baseQuery);

  // Thêm thông tin bổ sung
  const announcementsWithInfo = await Promise.all(
    announcements.map(async (announcement) => {
      // Logic isRead dựa trên mode và sender
      let isRead = null;
      if (mode === 'received') {
        // Mode received: luôn kiểm tra isRead
        isRead = await announcement.isReadByUser(req.user._id);
      } else if (mode === 'all') {
        // Mode all: chỉ kiểm tra isRead cho thông báo không phải mình gửi
        if (announcement.sender && announcement.sender.toString() !== req.user._id.toString()) {
          isRead = await announcement.isReadByUser(req.user._id);
        }
        // Thông báo mình gửi thì isRead = null (không cần thiết)
      }
      // Mode sent: isRead = null (không cần thiết vì là người gửi)

      const readStats = mode === 'sent' ? await AnnouncementRead.getReadStats(announcement._id) : null;

      return {
        ...announcement.toObject(),
        isRead,
        typeLabel: ANNOUNCEMENT_TYPE_LABELS[announcement.type],
        statusLabel: ANNOUNCEMENT_STATUS_LABELS[announcement.status],
        readStats: readStats ? {
          totalRead: readStats.totalRead || 0,
          totalUnread: readStats.totalUnread || 0,
          readPercentage: readStats.totalRead && (readStats.totalRead + readStats.totalUnread) > 0
            ? Math.round((readStats.totalRead / (readStats.totalRead + readStats.totalUnread)) * 100)
            : 0
        } : null
      };
    })
  );

  res.json({
    announcements: announcementsWithInfo,
    pagination: {
      currentPage: parseInt(page),
      totalPages: Math.ceil(total / limit),
      totalItems: total,
      itemsPerPage: parseInt(limit)
    },
    searchQuery: q,
    filters: { type, status, mode, startDate, endDate }
  });
});

// @desc    Bookmark/Unbookmark thông báo
// @access  Private (yêu cầu x-auth-token)
exports.toggleBookmark = asyncHandler(async (req, res) => {
  const { note = '' } = req.body;
  const announcement = await Announcement.findById(req.params.id);

  if (!announcement) {
    return res.status(HTTP_STATUS.NOT_FOUND).json(
      createErrorResponse(MESSAGES.ANNOUNCEMENT.NOT_FOUND)
    );
  }

  // Kiểm tra quyền xem thông báo
  const hasPermission = await checkViewPermission(req.user, announcement);
  if (!hasPermission) {
    return res.status(HTTP_STATUS.FORBIDDEN).json(
      createErrorResponse(MESSAGES.ANNOUNCEMENT.NO_PERMISSION_VIEW)
    );
  }

  // Kiểm tra đã bookmark chưa
  const isBookmarked = await AnnouncementBookmark.isBookmarked(req.user._id, announcement._id);

  if (isBookmarked) {
    // Unbookmark
    await AnnouncementBookmark.unbookmarkAnnouncement(req.user._id, announcement._id);
    res.json(createSuccessResponse({ bookmarked: false }, 'Đã bỏ bookmark thông báo'));
  } else {
    // Bookmark
    await AnnouncementBookmark.bookmarkAnnouncement(req.user._id, announcement._id, note);
    res.json(createSuccessResponse({ bookmarked: true }, 'Đã bookmark thông báo'));
  }
});

// @desc    Lấy danh sách thông báo đã bookmark
// @access  Private (yêu cầu x-auth-token)
exports.getBookmarkedAnnouncements = asyncHandler(async (req, res) => {
  const { page = 1, limit = 10 } = req.query;

  const bookmarks = await AnnouncementBookmark.getUserBookmarks(req.user._id, { page, limit });
  const total = await AnnouncementBookmark.countDocuments({ user: req.user._id });

  // Thêm thông tin bổ sung cho từng thông báo
  const bookmarksWithInfo = await Promise.all(
    bookmarks.map(async (bookmark) => {
      const announcement = bookmark.announcement;
      const isRead = await announcement.isReadByUser(req.user._id);

      return {
        _id: bookmark._id,
        note: bookmark.note,
        bookmarkedAt: bookmark.bookmarkedAt,
        announcement: {
          ...announcement.toObject(),
          isRead,
          typeLabel: ANNOUNCEMENT_TYPE_LABELS[announcement.type],
          statusLabel: ANNOUNCEMENT_STATUS_LABELS[announcement.status]
        }
      };
    })
  );

  res.json({
    bookmarks: bookmarksWithInfo,
    pagination: {
      currentPage: parseInt(page),
      totalPages: Math.ceil(total / limit),
      totalItems: total,
      itemsPerPage: parseInt(limit)
    }
  });
});

// @desc    Cập nhật ghi chú bookmark
// @access  Private (yêu cầu x-auth-token)
exports.updateBookmarkNote = asyncHandler(async (req, res) => {
  const { note } = req.body;

  const bookmark = await AnnouncementBookmark.findOneAndUpdate(
    {
      user: req.user._id,
      announcement: req.params.id
    },
    { note },
    { new: true }
  );

  if (!bookmark) {
    return res.status(HTTP_STATUS.NOT_FOUND).json(
      createErrorResponse('Không tìm thấy bookmark')
    );
  }

  res.json(createSuccessResponse(bookmark, 'Cập nhật ghi chú thành công'));
});

// @desc    Thêm phản hồi cho thông báo hoặc phản hồi khác
// @route   POST /api/announcements/:id/reply
// @access  Private (người có quyền xem thông báo)
exports.replyToAnnouncement = asyncHandler(async (req, res) => {
  const { content, parentReplyId, replyToUserId } = req.body;

  if (!content || content.trim().length === 0) {
    return res.status(HTTP_STATUS.BAD_REQUEST).json(
      createErrorResponse('Nội dung phản hồi không được để trống')
    );
  }

  const announcement = await Announcement.findById(req.params.id);

  if (!announcement) {
    return res.status(HTTP_STATUS.NOT_FOUND).json(
      createErrorResponse(MESSAGES.ANNOUNCEMENT.NOT_FOUND)
    );
  }

  // Kiểm tra quyền phản hồi bằng cách kiểm tra quyền xem
  const hasPermission = await checkViewPermission(req.user, announcement);
  if (!hasPermission) {
    return res.status(HTTP_STATUS.FORBIDDEN).json(
      createErrorResponse(MESSAGES.ERROR.FORBIDDEN)
    );
  }

  // Kiểm tra parentReply nếu có
  let parentReply = null;
  if (parentReplyId) {
    parentReply = await AnnouncementReply.findOne({
      _id: parentReplyId,
      announcement: announcement._id,
      isDeleted: false
    });

    if (!parentReply) {
      return res.status(HTTP_STATUS.NOT_FOUND).json(
        createErrorResponse('Không tìm thấy phản hồi cha')
      );
    }
  }

  // Kiểm tra replyTo nếu có
  let replyTo = null;
  if (replyToUserId) {
    replyTo = await User.findById(replyToUserId);
    if (!replyTo) {
      return res.status(HTTP_STATUS.NOT_FOUND).json(
        createErrorResponse('Không tìm thấy người dùng được reply')
      );
    }
  }

  // Tạo phản hồi mới
  const reply = new AnnouncementReply({
    announcement: announcement._id,
    user: req.user._id,
    content: content.trim(),
    parentReply: parentReply ? parentReply._id : null,
    replyTo: replyTo ? replyTo._id : null
  });

  await reply.save();
  await reply.populate([
    { path: 'user', select: 'name role specificRole' },
    { path: 'replyTo', select: 'name role specificRole' }
  ]);

  res.status(HTTP_STATUS.CREATED).json(
    createSuccessResponse({
      ...reply.toObject(),
      timeAgo: reply.timeAgo
    }, 'Đã thêm phản hồi thành công')
  );
});

// @desc    Lấy danh sách phản hồi của thông báo
// @route   GET /api/announcements/:id/replies
// @access  Private (người có quyền xem thông báo)
exports.getAnnouncementReplies = asyncHandler(async (req, res) => {
  const { page = 1, limit = 10, parentReplyId = null } = req.query;

  const announcement = await Announcement.findById(req.params.id);

  if (!announcement) {
    return res.status(HTTP_STATUS.NOT_FOUND).json(
      createErrorResponse(MESSAGES.ANNOUNCEMENT.NOT_FOUND)
    );
  }

  // Kiểm tra quyền xem
  const hasPermission = await checkViewPermission(req.user, announcement);
  if (!hasPermission) {
    return res.status(HTTP_STATUS.FORBIDDEN).json(
      createErrorResponse(MESSAGES.ERROR.FORBIDDEN)
    );
  }

  // Xây dựng query
  const query = {
    announcement: announcement._id,
    isDeleted: false
  };

  // Nếu có parentReplyId, lấy replies của reply đó
  if (parentReplyId) {
    query.parentReply = parentReplyId;
  } else {
    // Nếu không có parentReplyId, lấy các reply gốc (không có parent)
    query.parentReply = null;
  }

  // Lấy danh sách phản hồi
  const replies = await AnnouncementReply.find(query)
    .populate('user', 'name role specificRole')
    .populate('replyTo', 'name role specificRole')
    .sort({ createdAt: -1 })
    .skip((page - 1) * limit)
    .limit(parseInt(limit));

  // Cập nhật replyCount cho tất cả phản hồi
  const replyIds = replies.map(reply => reply._id);
  await AnnouncementReply.updateReplyCounts(replyIds);

  // Lấy lại danh sách phản hồi sau khi cập nhật replyCount
  const updatedReplies = await AnnouncementReply.find({ _id: { $in: replyIds } })
    .populate('user', 'name role specificRole')
    .populate('replyTo', 'name role specificRole');

  const total = await AnnouncementReply.countDocuments(query);

  // Format lại dữ liệu trả về
  const formattedReplies = updatedReplies.map(reply => ({
    ...reply.toObject(),
    timeAgo: reply.timeAgo,
    canEdit: reply.user._id.toString() === req.user._id.toString() || req.user.role.includes(ROLES.ADMIN),
    canDelete: reply.user._id.toString() === req.user._id.toString() || req.user.role.includes(ROLES.ADMIN)
  }));

  res.json(createSuccessResponse({
    replies: formattedReplies,
    pagination: {
      currentPage: parseInt(page),
      totalPages: Math.ceil(total / limit),
      totalItems: total,
      itemsPerPage: parseInt(limit)
    }
  }));
});

// @desc    Xóa phản hồi
// @route   DELETE /api/announcements/:announcementId/replies/:replyId
// @access  Private (người tạo phản hồi hoặc admin)
exports.deleteReply = asyncHandler(async (req, res) => {
  const { announcementId, replyId } = req.params;

  const announcement = await Announcement.findById(announcementId);
  if (!announcement) {
    return res.status(HTTP_STATUS.NOT_FOUND).json(
      createErrorResponse(MESSAGES.ANNOUNCEMENT.NOT_FOUND)
    );
  }

  const reply = await AnnouncementReply.findById(replyId);
  if (!reply) {
    return res.status(HTTP_STATUS.NOT_FOUND).json(
      createErrorResponse('Không tìm thấy phản hồi')
    );
  }

  // Kiểm tra quyền xóa
  if (reply.user.toString() !== req.user._id.toString() && !req.user.role.includes(ROLES.ADMIN)) {
    return res.status(HTTP_STATUS.FORBIDDEN).json(
      createErrorResponse(MESSAGES.ERROR.FORBIDDEN)
    );
  }

  // Soft delete phản hồi
  await reply.softDelete();

  res.json(createSuccessResponse(null, 'Đã xóa phản hồi thành công'));
});

// @desc    Cập nhật phản hồi
// @route   PUT /api/announcements/:announcementId/replies/:replyId
// @access  Private (người tạo phản hồi hoặc admin)
exports.updateReply = asyncHandler(async (req, res) => {
  const { announcementId, replyId } = req.params;
  const { content } = req.body;

  if (!content || content.trim().length === 0) {
    return res.status(HTTP_STATUS.BAD_REQUEST).json(
      createErrorResponse('Nội dung phản hồi không được để trống')
    );
  }

  const announcement = await Announcement.findById(announcementId);
  if (!announcement) {
    return res.status(HTTP_STATUS.NOT_FOUND).json(
      createErrorResponse(MESSAGES.ANNOUNCEMENT.NOT_FOUND)
    );
  }

  const reply = await AnnouncementReply.findById(replyId);
  if (!reply) {
    return res.status(HTTP_STATUS.NOT_FOUND).json(
      createErrorResponse('Không tìm thấy phản hồi')
    );
  }

  // Kiểm tra quyền sửa
  if (reply.user.toString() !== req.user._id.toString() && !req.user.role.includes(ROLES.ADMIN)) {
    return res.status(HTTP_STATUS.FORBIDDEN).json(
      createErrorResponse(MESSAGES.ERROR.FORBIDDEN)
    );
  }

  // Cập nhật phản hồi
  await reply.edit(content.trim());
  await reply.populate('user', 'name role specificRole');

  res.json(createSuccessResponse({
    ...reply.toObject(),
    timeAgo: reply.timeAgo
  }, 'Đã cập nhật phản hồi thành công'));
});

// ================ HELPER FUNCTIONS ================

// Helper function để tính thời gian tương đối
const getTimeAgo = (date) => {
  const now = new Date();
  const diffInSeconds = Math.floor((now - new Date(date)) / 1000);

  if (diffInSeconds < 60) return 'Vừa xong';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} phút trước`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} giờ trước`;
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} ngày trước`;
  if (diffInSeconds < 31536000) return `${Math.floor(diffInSeconds / 2592000)} tháng trước`;
  return `${Math.floor(diffInSeconds / 31536000)} năm trước`;
};

// Kiểm tra quyền tạo thông báo
const checkAnnouncementPermission = async (user, type) => {
  switch (type) {
    case ANNOUNCEMENT_TYPES.TEACHER_TO_STUDENT:
      return user.role.includes(ROLES.TEACHER) || user.role.includes(ROLES.ADMIN);

    case ANNOUNCEMENT_TYPES.PRINCIPAL_TO_TEACHER:
      return user.role.includes(ROLES.ADMIN); // Assuming principal is admin

    case ANNOUNCEMENT_TYPES.HEAD_TO_TEACHER:
      return (user.role.includes(ROLES.TEACHER) && user.specificRole === TEACHER_SPECIFIC_ROLES.DEPARTMENT_HEAD) ||
        user.role.includes(ROLES.ADMIN);

    case ANNOUNCEMENT_TYPES.ADMIN_TO_ALL:
      return user.role.includes(ROLES.ADMIN);

    case ANNOUNCEMENT_TYPES.MUTUAL_COMMUNICATION:
      return true;

    default:
      return false;
  }
};

// Validate recipients dựa trên type
const validateRecipients = (type, recipients) => {
  if (!recipients) {
    return { isValid: false, message: 'Recipients không được để trống' };
  }

  switch (type) {
    case ANNOUNCEMENT_TYPES.TEACHER_TO_STUDENT:
      if (!recipients.class) {
        return { isValid: false, message: 'Cần chọn lớp học cho thông báo gửi học sinh' };
      }
      break;

    case ANNOUNCEMENT_TYPES.PRINCIPAL_TO_TEACHER:
    case ANNOUNCEMENT_TYPES.HEAD_TO_TEACHER:
      if (!recipients.teachers && !recipients.department && !recipients.schoolWide) {
        return { isValid: false, message: 'Cần chọn giáo viên, bộ môn hoặc toàn trường' };
      }
      break;

    case ANNOUNCEMENT_TYPES.ADMIN_TO_ALL:
      if (!recipients.schoolWide) {
        return { isValid: false, message: 'Thông báo admin phải gửi toàn trường' };
      }
      break;

    case ANNOUNCEMENT_TYPES.MUTUAL_COMMUNICATION:
      if (!recipients.specificUsers || !Array.isArray(recipients.specificUsers) || recipients.specificUsers.length === 0) {
        return { isValid: false, message: 'Cần chọn ít nhất một người nhận cho thông báo mutual communication' };
      }
      break;

    default:
      return { isValid: false, message: 'Loại thông báo không hợp lệ' };
  }

  return { isValid: true };
};

// Xây dựng query để lấy thông báo dựa trên role và quyền của user
const buildAnnouncementQuery = async (user, filters = {}) => {
  let query = {};

  // Xử lý theo mode
  if (filters.mode === 'sent') {
    // Mode 'sent': chỉ lấy thông báo mình đã gửi
    query = { sender: user._id };
  } else if (filters.mode === 'all') {
    // Mode 'all': lấy cả thông báo mình gửi và mình nhận được
    const receivedQuery = await buildReceivedQuery(user);
    query = {
      $or: [
        { sender: user._id }, // Thông báo mình gửi
        receivedQuery // Thông báo mình nhận được
      ]
    };
  } else {
    // Mode 'received' (default): chỉ lấy thông báo nhận được, không bao gồm thông báo mình gửi
    query = {
      $and: [
        await buildReceivedQuery(user),
        { sender: { $ne: user._id } } // Loại bỏ thông báo mình gửi
      ]
    };
  }

  // Áp dụng các filter bổ sung
  return applyAdditionalFilters(query, user, filters);
};

// Helper function để xây dựng query cho thông báo nhận được
const buildReceivedQuery = async (user) => {
  let receivedQuery = {};

  // Thông báo mutual communication - tất cả user đều có thể nhận
  const mutualCommunicationQuery = {
    type: ANNOUNCEMENT_TYPES.MUTUAL_COMMUNICATION,
    'recipients.specificUsers': user._id,
    status: ANNOUNCEMENT_STATUS.SENT
  };

  // Lọc theo role của user
  switch (true) {
    case user.role.includes(ROLES.ADMIN):
      // Admin xem tất cả thông báo nhận được (tất cả trạng thái) + mutual communication
      receivedQuery = {
        $or: [
          {}, // Tất cả thông báo
          mutualCommunicationQuery
        ]
      };
      break;

    case user.role.includes(ROLES.STUDENT):
      // Học sinh chỉ xem thông báo gửi cho lớp của mình + mutual communication
      // Lấy lớp hiện tại từ StudentEnrollment
      const studentEnrollment = await StudentEnrollment.findOne({
        student: user._id,
        status: 'active'
      });

      if (studentEnrollment) {
        receivedQuery = {
          $or: [
            {
              type: ANNOUNCEMENT_TYPES.TEACHER_TO_STUDENT,
              'recipients.class': studentEnrollment.class,
              status: ANNOUNCEMENT_STATUS.SENT // Chỉ xem thông báo đã gửi
            },
            mutualCommunicationQuery
          ]
        };
      } else {
        // Nếu không có enrollment active, chỉ xem mutual communication
        receivedQuery = mutualCommunicationQuery;
      }
      break;

    case user.role.includes(ROLES.TEACHER):
      // Giáo viên xem thông báo gửi cho mình + mutual communication
      receivedQuery = {
        $or: [
          // 1. Thông báo từ hiệu trưởng/tổ trưởng cho giáo viên (chỉ đã gửi)
          {
            type: { $in: [ANNOUNCEMENT_TYPES.PRINCIPAL_TO_TEACHER, ANNOUNCEMENT_TYPES.HEAD_TO_TEACHER] },
            status: ANNOUNCEMENT_STATUS.SENT,
            $or: [
              { 'recipients.schoolWide': true }, // Gửi toàn trường
              { 'recipients.teachers': user._id }, // Được chỉ định cụ thể
              {
                'recipients.department': {
                  $in: [user.department, ...(user.additionalDepartments || [])]
                }
              } // Thuộc bộ môn (bao gồm cả bộ môn phụ)
            ]
          },

          // 2. Thông báo admin gửi toàn trường (chỉ đã gửi)
          {
            type: ANNOUNCEMENT_TYPES.ADMIN_TO_ALL,
            'recipients.schoolWide': true,
            status: ANNOUNCEMENT_STATUS.SENT
          },

          // 3. Thông báo mutual communication
          mutualCommunicationQuery
        ]
      };
      break;

    default:
      // User khác chỉ có thể nhận mutual communication
      receivedQuery = mutualCommunicationQuery;
  }

  return receivedQuery;
};

// Helper function để áp dụng các filter bổ sung
const applyAdditionalFilters = (query, user, filters) => {
  // Áp dụng các filter bổ sung
  if (filters.type) {
    if (user.role.includes(ROLES.ADMIN)) {
      query.type = filters.type;
    } else {
      // Với non-admin, cần kết hợp với query hiện tại
      if (Object.keys(query).length > 0) {
        query = { $and: [query, { type: filters.type }] };
      } else {
        query.type = filters.type;
      }
    }
  }

  if (filters.classId && !user.role.includes(ROLES.STUDENT)) {
    const classFilter = { 'recipients.class': filters.classId };
    if (Object.keys(query).length > 0 && !user.role.includes(ROLES.ADMIN)) {
      query = { $and: [query, classFilter] };
    } else {
      Object.assign(query, classFilter);
    }
  }

  if (filters.department) {
    const deptFilter = { 'recipients.department': filters.department };
    if (Object.keys(query).length > 0 && !user.role.includes(ROLES.ADMIN)) {
      query = { $and: [query, deptFilter] };
    } else {
      Object.assign(query, deptFilter);
    }
  }

  if (filters.status) {
    const statusFilter = { status: filters.status };
    if (Object.keys(query).length > 0 && !user.role.includes(ROLES.ADMIN)) {
      query = { $and: [query, statusFilter] };
    } else {
      Object.assign(query, statusFilter);
    }
  }

  // Thêm filter cho startDate và endDate
  if (filters.startDate || filters.endDate) {
    const dateFilter = {};
    if (filters.startDate) {
      const start = new Date(filters.startDate);
      start.setHours(0, 0, 0, 0);
      dateFilter.$gte = start;
    }
    if (filters.endDate) {
      const end = new Date(filters.endDate);
      end.setHours(23, 59, 59, 999);
      dateFilter.$lte = end;
    }
    
    if (Object.keys(dateFilter).length > 0) {
      if (Object.keys(query).length > 0 && !user.role.includes(ROLES.ADMIN)) {
        query = { $and: [query, { createdAt: dateFilter }] };
      } else {
        Object.assign(query, { createdAt: dateFilter });
      }
    }
  }

  return query;
};

// Kiểm tra quyền xem chi tiết thông báo
const checkViewPermission = async (user, announcement) => {
  // Admin có thể xem tất cả
  if (user.role.includes(ROLES.ADMIN)) {
    return true;
  }

  // Người tạo có thể xem
  if (announcement.sender && announcement.sender.toString() === user._id.toString()) {
    return true;
  }

  // Kiểm tra theo type và recipients
  switch (announcement.type) {
    case ANNOUNCEMENT_TYPES.TEACHER_TO_STUDENT:
      // Học sinh chỉ xem được thông báo đã gửi cho lớp của mình
      if (user.role.includes(ROLES.STUDENT) && announcement.status === ANNOUNCEMENT_STATUS.SENT) {
        // Lấy lớp hiện tại từ StudentEnrollment
        const studentEnrollment = await StudentEnrollment.findOne({
          student: user._id,
          status: 'active'
        });
        
        let result = studentEnrollment &&
          announcement.recipients.class &&
          announcement.recipients.class._id.toString() === studentEnrollment.class.toString();
        return result;
      }
      return false;

    case ANNOUNCEMENT_TYPES.PRINCIPAL_TO_TEACHER:
    case ANNOUNCEMENT_TYPES.HEAD_TO_TEACHER:
      // Giáo viên chỉ xem được thông báo đã gửi và thuộc các điều kiện sau:
      if (!user.role.includes(ROLES.TEACHER) || announcement.status !== ANNOUNCEMENT_STATUS.SENT) {
        return false;
      }

      return announcement.recipients.schoolWide || // Gửi toàn trường
        (announcement.recipients.teachers && announcement.recipients.teachers.includes(user._id)) || // Được chỉ định
        (announcement.recipients.department &&
          (user.department === announcement.recipients.department ||
            (user.additionalDepartments && user.additionalDepartments.includes(announcement.recipients.department)))); // Thuộc bộ môn

    case ANNOUNCEMENT_TYPES.ADMIN_TO_ALL:
      // Tất cả user (trừ admin) chỉ xem được thông báo đã gửi toàn trường
      return announcement.status === ANNOUNCEMENT_STATUS.SENT &&
        announcement.recipients.schoolWide;

    case ANNOUNCEMENT_TYPES.MUTUAL_COMMUNICATION:
      // User chỉ xem được mutual communication nếu họ nằm trong specificUsers và thông báo đã gửi
      return announcement.status === ANNOUNCEMENT_STATUS.SENT &&
        announcement.recipients.specificUsers &&
        announcement.recipients.specificUsers.includes(user._id);

    default:
      return false;
  }
};