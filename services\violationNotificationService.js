// services/violationNotificationService.js
const Announcement = require('../models/Announcement');
const User = require('../models/User');
const Class = require('../models/Class');
const StudentEnrollment = require('../models/StudentEnrollment');
const { 
  DEFAULT_VIOLATION_MESSAGES,
  VIOLATION_TYPE_LABELS 
} = require('../constants/violationConstants');
const { ANNOUNCEMENT_STATUS } = require('../constants/announcementConstants');
const { sendImmediateZaloMessage } = require('../jobs/zaloMessageScheduler');

/**
 * Gửi thông báo vi phạm đến giáo viên chủ nhiệm
 * @param {Object} violation - Thông tin vi phạm
 * @param {Object} studentConduct - Thông tin điểm thi đua học sinh
 * @param {Object} reportedBy - Người báo cáo vi phạm
 */
const sendViolationNotificationToHomeroom = async (violation, studentConduct, reportedBy) => {
  try {
    // Lấy thông tin lớp và giáo viên chủ nhiệm
    const classInfo = await Class.findById(violation.class)
      .populate('homeroomTeacher', 'name phoneNumber zaloId');
    
    if (!classInfo || !classInfo.homeroomTeacher) {
      console.log('Không tìm thấy giáo viên chủ nhiệm cho lớp:', classInfo?.name);
      return null;
    }

    // Lấy thông tin học sinh
    const student = await User.findById(violation.student, 'name studentId');
    
    if (!student) {
      console.log('Không tìm thấy thông tin học sinh');
      return null;
    }

    // Tạo nội dung thông báo theo template
    const violationDate = new Date(violation.violationDate).toLocaleDateString('vi-VN');
    const content = DEFAULT_VIOLATION_MESSAGES.REPORT_TEMPLATE
      .replace('{homeroomTeacher}', classInfo.homeroomTeacher.name)
      .replace('{studentName}', student.name)
      .replace('{className}', classInfo.name)
      .replace('{violationType}', VIOLATION_TYPE_LABELS[violation.violationType])
      .replace('{violationDate}', violationDate)
      .replace('{totalViolations}', studentConduct.totalViolations)
      .replace('{remainingPoints}', studentConduct.currentPoints);

    // Tạo thông báo
    const announcement = new Announcement({
      sender: reportedBy._id,
      title: `Báo cáo vi phạm nội quy - ${student.name} (${classInfo.name})`,
      type: 'mutual_communication',
      recipients: {
        specificUsers: [classInfo.homeroomTeacher._id]
      },
      content: content,
      status: ANNOUNCEMENT_STATUS.SENT,
      zaloConfig: {
        enabled: false // Mặc định không gửi qua Zalo, có thể cấu hình sau
      }
    });

    await announcement.save();
    
    console.log(`✅ Đã gửi thông báo vi phạm đến GVCN: ${classInfo.homeroomTeacher.name}`);
    return announcement;

  } catch (error) {
    console.error('❌ Lỗi khi gửi thông báo vi phạm đến GVCN:', error);
    throw error;
  }
};

/**
 * Gửi thông báo vi phạm đến phụ huynh (qua Zalo nếu có)
 * @param {Object} violation - Thông tin vi phạm
 * @param {Object} studentConduct - Thông tin điểm thi đua học sinh
 * @param {Object} reportedBy - Người báo cáo vi phạm
 */
const sendViolationNotificationToParent = async (violation, studentConduct, reportedBy) => {
  try {
    // Lấy thông tin học sinh và phụ huynh
    const student = await User.findById(violation.student, 'name studentId parentPhone parentZaloId');
    const classInfo = await Class.findById(violation.class, 'name');
    
    if (!student || !student.parentPhone) {
      console.log('Không tìm thấy thông tin liên lạc phụ huynh');
      return null;
    }

    // Tạo nội dung thông báo cho phụ huynh
    const violationDate = new Date(violation.violationDate).toLocaleDateString('vi-VN');
    const content = DEFAULT_VIOLATION_MESSAGES.PARENT_NOTIFICATION
      .replace('{studentName}', student.name)
      .replace('{className}', classInfo.name)
      .replace('{violationType}', VIOLATION_TYPE_LABELS[violation.violationType])
      .replace('{violationDate}', violationDate)
      .replace('{remainingPoints}', studentConduct.currentPoints);

    // Tạo thông báo (có thể gửi qua Zalo nếu có parentZaloId)
    const announcement = new Announcement({
      sender: reportedBy._id,
      title: `Thông báo vi phạm nội quy - ${student.name}`,
      type: 'mutual_communication',
      recipients: {
        specificUsers: [] // Không gửi trong hệ thống, chỉ gửi qua Zalo
      },
      content: content,
      status: ANNOUNCEMENT_STATUS.SENT,
      zaloConfig: {
        enabled: !!student.parentZaloId,
        groupId: student.parentZaloId || null,
        groupName: `Phụ huynh ${student.name}`
      }
    });

    await announcement.save();

    // Gửi qua Zalo nếu có thông tin
    if (student.parentZaloId && announcement.zaloConfig.enabled) {
      try {
        await sendImmediateZaloMessage(announcement._id);
        console.log(`✅ Đã gửi thông báo vi phạm qua Zalo đến phụ huynh: ${student.name}`);
      } catch (zaloError) {
        console.error('❌ Lỗi khi gửi Zalo đến phụ huynh:', zaloError);
      }
    }

    return announcement;

  } catch (error) {
    console.error('❌ Lỗi khi gửi thông báo vi phạm đến phụ huynh:', error);
    throw error;
  }
};

/**
 * Gửi thông báo vi phạm đến ban giám hiệu (nếu vi phạm nghiêm trọng)
 * @param {Object} violation - Thông tin vi phạm
 * @param {Object} studentConduct - Thông tin điểm thi đua học sinh
 * @param {Object} reportedBy - Người báo cáo vi phạm
 */
const sendViolationNotificationToPrincipal = async (violation, studentConduct, reportedBy) => {
  try {
    // Chỉ gửi thông báo cho vi phạm nghiêm trọng (điểm trừ >= 5)
    if (violation.pointsDeducted < 5) {
      return null;
    }

    // Tìm ban giám hiệu (users có role admin hoặc principal)
    const principals = await User.find({
      $or: [
        { role: { $in: ['admin'] } },
        { specificRole: 'principal' }
      ]
    }, 'name');

    if (principals.length === 0) {
      console.log('Không tìm thấy ban giám hiệu');
      return null;
    }

    // Lấy thông tin học sinh và lớp
    const student = await User.findById(violation.student, 'name studentId');
    const classInfo = await Class.findById(violation.class, 'name');

    const violationDate = new Date(violation.violationDate).toLocaleDateString('vi-VN');
    const content = `BÁO CÁO VI PHẠM NGHIÊM TRỌNG\n\n` +
      `Học sinh: ${student.name} (${student.studentId})\n` +
      `Lớp: ${classInfo.name}\n` +
      `Loại vi phạm: ${VIOLATION_TYPE_LABELS[violation.violationType]}\n` +
      `Ngày vi phạm: ${violationDate}\n` +
      `Điểm bị trừ: ${violation.pointsDeducted}\n` +
      `Điểm thi đua còn lại: ${studentConduct.currentPoints}\n` +
      `Tổng số lần vi phạm: ${studentConduct.totalViolations}\n\n` +
      `Mô tả: ${violation.description}\n\n` +
      `Người báo cáo: ${reportedBy.name}\n` +
      `Đề nghị Ban Giám Hiệu xem xét và có biện pháp xử lý phù hợp.`;

    // Tạo thông báo đến ban giám hiệu
    const announcement = new Announcement({
      sender: reportedBy._id,
      title: `Vi phạm nghiêm trọng - ${student.name} (${classInfo.name})`,
      type: 'mutual_communication',
      recipients: {
        specificUsers: principals.map(p => p._id)
      },
      content: content,
      status: ANNOUNCEMENT_STATUS.SENT,
      zaloConfig: {
        enabled: false
      }
    });

    await announcement.save();
    
    console.log(`✅ Đã gửi báo cáo vi phạm nghiêm trọng đến Ban Giám Hiệu`);
    return announcement;

  } catch (error) {
    console.error('❌ Lỗi khi gửi thông báo vi phạm đến Ban Giám Hiệu:', error);
    throw error;
  }
};

/**
 * Gửi tất cả thông báo vi phạm
 * @param {Object} violation - Thông tin vi phạm
 * @param {Object} studentConduct - Thông tin điểm thi đua học sinh
 * @param {Object} reportedBy - Người báo cáo vi phạm
 */
const sendAllViolationNotifications = async (violation, studentConduct, reportedBy) => {
  const results = {
    homeroomNotification: null,
    parentNotification: null,
    principalNotification: null,
    errors: []
  };

  try {
    // Gửi thông báo đến GVCN
    results.homeroomNotification = await sendViolationNotificationToHomeroom(
      violation, 
      studentConduct, 
      reportedBy
    );
  } catch (error) {
    results.errors.push(`GVCN: ${error.message}`);
  }

  try {
    // Gửi thông báo đến phụ huynh
    results.parentNotification = await sendViolationNotificationToParent(
      violation, 
      studentConduct, 
      reportedBy
    );
  } catch (error) {
    results.errors.push(`Phụ huynh: ${error.message}`);
  }

  try {
    // Gửi thông báo đến ban giám hiệu (nếu cần)
    results.principalNotification = await sendViolationNotificationToPrincipal(
      violation, 
      studentConduct, 
      reportedBy
    );
  } catch (error) {
    results.errors.push(`Ban Giám Hiệu: ${error.message}`);
  }

  return results;
};

module.exports = {
  sendViolationNotificationToHomeroom,
  sendViolationNotificationToParent,
  sendViolationNotificationToPrincipal,
  sendAllViolationNotifications
};
