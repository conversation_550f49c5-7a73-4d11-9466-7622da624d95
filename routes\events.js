// Sửa lỗi trong routes/events.js
const express = require('express');
const router = express.Router();
const {
    createEvent,
    getEvents,
    getMyEvents,
    getUpcomingEvents,
} = require('../controllers/eventsController');
const { protect, authorize } = require('../middlewares/auth');

// @route   POST /api/events
// @desc    Tạo sự kiện mới (chỉ giáo viên và admin)
// @access  Private (yêu cầu x-auth-token, role: teacher, admin)
router.post('/', protect, authorize('teacher', 'admin'), createEvent);

// @route   GET /api/events
// @desc    Lấy danh sách sự kiện của một lớp (phân trang)
// @access  Private (yêu cầu x-auth-token)
router.get('/', protect, getEvents);

// @route   GET /api/events/my-events
// @desc    Lấy danh sách sự kiện do người dùng tạo
// @access  Private (yêu cầu x-auth-token, role: teacher, admin)
router.get('/my-events', protect, authorize('teacher', 'admin'), getMyEvents);

// @route   GET /api/events/upcoming
// @desc    Lấy sự kiện sắp tới của một lớp
// @access  Private (yêu cầu x-auth-token)
router.get('/upcoming', protect, getUpcomingEvents);

module.exports = router;