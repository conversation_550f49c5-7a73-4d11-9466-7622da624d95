// models/LeaveRequest.js
const mongoose = require('mongoose');

const LeaveRequestSchema = new mongoose.Schema({
  // Loại yêu cầu: học sinh hay giáo viên
  requestType: {
    type: String,
    enum: ['student', 'teacher'],
    required: true,
    default: 'student'
  },
  // Người yêu cầu (có thể là học sinh hoặc giáo viên)
  requester: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  // Để tương thích với code cũ, giữ lại trường student nhưng đặt alias
  student: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    get: function() {
      return this.requester;
    },
    set: function(value) {
      this.requester = value;
    }
  },
  // Lớp học (chỉ áp dụng cho học sinh)
  class: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Class',
    required: function() {
      return this.requestType === 'student';
    }
  },
  // Ngày bắt đầu nghỉ
  startDate: {
    type: Date,
    required: true
  },
  // Ngày kết thúc nghỉ (có thể trùng với ngày bắt đầu nếu chỉ nghỉ 1 ngày)
  endDate: {
    type: Date,
    required: true
  },
  // Buổi nghỉ (sáng, chiều hoặc cả ngày)
  sessions: [{
    type: String,
    enum: ['morning', 'afternoon', 'all-day'],
    required: true
  }],
  // Lý do nghỉ học
  reason: {
    type: String,
    required: true,
    trim: true
  },
  // Hình ảnh đính kèm (có thể là chữ ký phụ huynh, giấy xác nhận, v.v.)
  attachments: [{
    // Data URL của ảnh
    data: {
      type: String,
      required: true
    },
    // Chiều rộng của ảnh
    width: {
      type: Number,
      required: true
    },
    // Chiều cao của ảnh
    height: {
      type: Number,
      required: true
    },
    // Ngày tải lên
    uploadedAt: {
      type: Date,
      default: Date.now
    }
  }],
  // Trạng thái xin phép
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected'],
    default: 'pending',
    required: true
  },
  // Người phê duyệt (giáo viên hoặc admin)
  approvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  // Ngày phê duyệt
  approvedAt: {
    type: Date
  },
  // Ghi chú của người phê duyệt
  approverNotes: {
    type: String,
    trim: true
  },
  // Người tạo yêu cầu (thường là học sinh hoặc phụ huynh)
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Tiền xử lý trước khi lưu
LeaveRequestSchema.pre('save', function (next) {
  this.updatedAt = Date.now();
  next();
});

// Index để tối ưu hiệu suất truy vấn
LeaveRequestSchema.index({ student: 1, class: 1, startDate: 1, endDate: 1 });
LeaveRequestSchema.index({ status: 1 });

module.exports = mongoose.model('LeaveRequest', LeaveRequestSchema);
