const mongoose = require('mongoose');

const SchoolYearSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    // Ví dụ: "2024-2025"
  },
  startDate: {
    type: Date,
    required: true
  },
  endDate: {
    type: Date,
    required: true
  },
  isActive: {
    type: Boolean,
    default: false
  },
  description: {
    type: String,
    trim: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Index để tối ưu hiệu suất truy vấn
SchoolYearSchema.index({ isActive: 1 });
SchoolYearSchema.index({ startDate: 1, endDate: 1 });

// Tiền xử lý trước khi lưu
SchoolYearSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Static method để lấy năm học hiện tại
SchoolYearSchema.statics.getCurrentSchoolYear = function() {
  const now = new Date();
  return this.findOne({
    startDate: { $lte: now },
    endDate: { $gte: now },
    isActive: true
  });
};

// Static method để lấy tất cả năm học theo thứ tự thời gian
SchoolYearSchema.statics.getAllSchoolYears = function() {
  return this.find().sort({ startDate: -1 });
};

// Method để kiểm tra xem một ngày có nằm trong năm học này không
SchoolYearSchema.methods.isDateInSchoolYear = function(date) {
  const checkDate = new Date(date);
  return checkDate >= this.startDate && checkDate <= this.endDate;
};

module.exports = mongoose.model('SchoolYear', SchoolYearSchema); 