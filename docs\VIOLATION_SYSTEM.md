# Hệ Thống Báo Cáo Vi Phạm Học Sinh

## Tổng Quan

Hệ thống báo cáo vi phạm học sinh được thiết kế để quản lý và theo dõi các vi phạm nội quy của học sinh, tự động tính toán điểm thi đua và gửi thông báo đến các bên liên quan.

## Tính Năng Chính

### 1. Quản Lý Vi Phạm
- ✅ Tạo báo cáo vi phạm với đầy đủ thông tin
- ✅ Phân loại vi phạm theo 16 loại khác nhau
- ✅ Tự động tính toán điểm trừ thi đua
- ✅ Theo dõi trạng thái xử lý vi phạm
- ✅ Hỗ trợ khiếu nại và xử lý khiếu nại

### 2. Quản Lý Điểm Thi Đua
- ✅ Tự động trừ điểm khi có vi phạm
- ✅ <PERSON> dõi lịch sử thay đổi điểm
- ✅ Phân loại học sinh theo điểm thi đua
- ✅ Hỗ trợ cộng/trừ điểm thủ công

### 3. Hệ Thống Thông Báo
- ✅ Gửi thông báo đến giáo viên chủ nhiệm
- ✅ Gửi thông báo đến phụ huynh qua Zalo
- ✅ Báo cáo vi phạm nghiêm trọng đến ban giám hiệu
- ✅ Template thông báo theo mẫu truyền thống

### 4. Thống Kê và Báo Cáo
- ✅ Thống kê vi phạm theo loại, thời gian, lớp
- ✅ Top học sinh vi phạm nhiều nhất
- ✅ Thống kê điểm thi đua theo lớp
- ✅ Báo cáo timeline vi phạm

## Cấu Trúc Database

### Models

#### 1. Violation Model
```javascript
{
  student: ObjectId,           // Học sinh vi phạm
  class: ObjectId,            // Lớp học
  schoolYear: String,         // Năm học
  violationType: String,      // Loại vi phạm
  description: String,        // Mô tả chi tiết
  violationDate: Date,        // Ngày vi phạm
  pointsDeducted: Number,     // Điểm bị trừ
  status: String,             // Trạng thái xử lý
  reportedBy: ObjectId,       // Người báo cáo
  appealReason: String,       // Lý do khiếu nại
  appealedAt: Date,          // Thời gian khiếu nại
  appealResult: String,       // Kết quả khiếu nại
  notificationSent: Boolean,  // Đã gửi thông báo
  notificationSentAt: Date   // Thời gian gửi thông báo
}
```

#### 2. StudentConduct Model
```javascript
{
  student: ObjectId,          // Học sinh
  class: ObjectId,           // Lớp học
  schoolYear: String,        // Năm học
  currentPoints: Number,     // Điểm thi đua hiện tại (0-100)
  totalViolations: Number,   // Tổng số vi phạm
  pointHistory: [{           // Lịch sử thay đổi điểm
    date: Date,
    previousPoints: Number,
    newPoints: Number,
    pointsChanged: Number,
    reason: String,
    changedBy: ObjectId,
    violationId: ObjectId
  }],
  classification: String,    // Phân loại (excellent, good, fair, average, weak)
  lastUpdated: Date,
  lastUpdatedBy: ObjectId
}
```

## API Endpoints

### Violation Management
```
POST   /api/violations                    # Tạo vi phạm mới
GET    /api/violations                    # Lấy danh sách vi phạm
GET    /api/violations/:id                # Chi tiết vi phạm
PUT    /api/violations/:id                # Cập nhật vi phạm
DELETE /api/violations/:id                # Xóa vi phạm (Admin only)
```

### Appeal System
```
POST   /api/violations/:id/appeal         # Khiếu nại vi phạm
POST   /api/violations/:id/process-appeal # Xử lý khiếu nại
```

### Student-specific
```
GET    /api/violations/student/:studentId # Vi phạm của học sinh
GET    /api/violations/class/:classId     # Vi phạm của lớp
```

### Statistics
```
GET    /api/violations/stats              # Thống kê tổng quan
GET    /api/violations/stats/timeline     # Thống kê theo thời gian
GET    /api/violations/stats/top-students # Top học sinh vi phạm
GET    /api/violations/stats/by-class     # Thống kê theo lớp
```

### Student Conduct
```
GET    /api/violations/conduct/:studentId           # Điểm thi đua học sinh
PUT    /api/violations/conduct/:studentId           # Cập nhật điểm thi đua
GET    /api/violations/conduct/class/:classId/stats # Thống kê lớp
```

### Configuration
```
GET    /api/violations/config             # Cấu hình hệ thống
```

## Loại Vi Phạm và Điểm Trừ

| Loại Vi Phạm | Mã | Điểm Trừ |
|--------------|----|---------:|
| Vắng mặt không phép | absent | 5 |
| Đi học muộn | late | 2 |
| Không đúng trang phục | uniform | 1 |
| Vi phạm nội quy lớp học | behavior | 3 |
| Không làm bài tập | homework | 2 |
| Sử dụng điện thoại | phone | 3 |
| Nói chuyện riêng | talking | 1 |
| Ăn uống trong lớp | eating | 1 |
| Không tham gia hoạt động | activity | 2 |
| Gây rối trật tự | disruption | 5 |
| Không tôn trọng thầy cô | disrespect | 10 |
| Gian lận trong thi cử | cheating | 15 |
| Hút thuốc | smoking | 10 |
| Đánh nhau | fighting | 15 |
| Phá hoại tài sản | vandalism | 10 |
| Vi phạm khác | other | 3 |

## Phân Loại Điểm Thi Đua

| Phân Loại | Điểm | Mô Tả |
|-----------|-----:|-------|
| Xuất sắc | 90-100 | Excellent |
| Tốt | 80-89 | Good |
| Khá | 70-79 | Fair |
| Trung bình | 50-69 | Average |
| Yếu | 0-49 | Weak |

## Quy Trình Xử Lý Vi Phạm

### 1. Tạo Báo Cáo Vi Phạm
1. Giáo viên/Admin tạo báo cáo vi phạm
2. Hệ thống tự động trừ điểm thi đua
3. Gửi thông báo đến GVCN, phụ huynh
4. Nếu vi phạm nghiêm trọng (≥5 điểm), báo cáo BGH

### 2. Khiếu Nại Vi Phạm
1. Học sinh gửi khiếu nại với lý do
2. Giáo viên/Admin xem xét và quyết định
3. Nếu chấp nhận: hoàn tác điểm thi đua
4. Nếu từ chối: giữ nguyên vi phạm

### 3. Thông Báo Tự Động
- **GVCN**: Nhận thông báo qua hệ thống announcement
- **Phụ huynh**: Nhận thông báo qua Zalo (nếu có)
- **BGH**: Nhận báo cáo vi phạm nghiêm trọng

## Cách Sử Dụng

### 1. Tạo Vi Phạm Mới
```javascript
POST /api/violations
{
  "studentId": "student_id",
  "classId": "class_id", 
  "schoolYear": "2024-2025",
  "violationType": "late",
  "description": "Đi học muộn 15 phút",
  "violationDate": "2024-01-15T07:30:00Z"
}
```

### 2. Khiếu Nại Vi Phạm
```javascript
POST /api/violations/:id/appeal
{
  "appealReason": "Tôi có lý do chính đáng..."
}
```

### 3. Lấy Thống Kê
```javascript
GET /api/violations/stats?schoolYear=2024-2025&classId=class_id
```

## Testing

### Chạy Unit Tests
```bash
npm test tests/violation.test.js
```

### Chạy Integration Test
```bash
node scripts/testViolationSystem.js
```

## Lưu Ý Quan Trọng

1. **Quyền Hạn**: Chỉ Admin và Teacher mới có thể tạo/sửa vi phạm
2. **Khiếu Nại**: Chỉ học sinh bị vi phạm mới có thể khiếu nại
3. **Điểm Thi Đua**: Điểm mặc định là 50, không được âm
4. **Thông Báo**: Tự động gửi khi tạo vi phạm mới
5. **Backup**: Lưu trữ đầy đủ lịch sử thay đổi điểm

## Tích Hợp

Hệ thống tích hợp với:
- ✅ Hệ thống User Management
- ✅ Hệ thống Class Management  
- ✅ Hệ thống Announcement
- ✅ Zalo API cho thông báo phụ huynh
- ✅ Role-based Access Control
