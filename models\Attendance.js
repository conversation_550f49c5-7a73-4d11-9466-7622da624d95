// models/Attendance.js
const mongoose = require('mongoose');
const { ATTENDANCE_STATUS, SESSION } = require('../constants/attendanceConstants');

const AttendanceSchema = new mongoose.Schema({
  student: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  class: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Class',
    required: true
  },
  date: {
    type: Date,
    required: true,
    default: Date.now
  },
  session: {
    type: String,
    enum: Object.values(SESSION),
    required: true
  },
  status: {
    type: String,
    enum: Object.values(ATTENDANCE_STATUS),
    required: true,
    default: ATTENDANCE_STATUS.PRESENT
  },
  // Liên kết đến yêu cầu xin phép nghỉ học (nếu có)
  leaveRequest: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'LeaveRequest'
  },
  location: {
    type: String,
    trim: true,
    required: false
  },
  coordinates: {
    latitude: {
      type: Number
    },
    longitude: {
      type: Number
    }
  },
  notes: {
    type: String,
    trim: true
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User', // Người tạo điểm danh (học sinh hoặc giáo viên)
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User' // Người cập nhật điểm danh (thường là giáo viên)
  },
  rejectedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User' // Giáo viên từ chối điểm danh
  },
  rejectionReason: {
    type: String,
    trim: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Tiền xử lý trước khi lưu
AttendanceSchema.pre('save', function (next) {
  this.updatedAt = Date.now();
  next();
});

// Index để tối ưu hiệu suất truy vấn
AttendanceSchema.index({ student: 1, class: 1, date: 1, session: 1 });

module.exports = mongoose.model('Attendance', AttendanceSchema);
