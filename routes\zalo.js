const express = require('express');
const router = express.Router();
const {
  getAllZaloGroups,
  getUserZaloGroups,
  sendMessageToZaloGroup,
  sendMessageToZaloUser,
  adminAddUserToZaloGroup,
  adminRemoveUserFromZaloGroup,
  adminAddUsersToZaloGroup
} = require('../controllers/zaloController');
const { protect, authorize } = require('../middlewares/auth');
const ROLES = require('../constants/roleConstants');

// @route   GET /api/zalo/groups
// @desc    Lấy tất cả nhóm Zalo (Admin only)
// @access  Private
router.get('/groups', protect, authorize(ROLES.ADMIN), getAllZaloGroups);

// @route   GET /api/zalo/user/groups
// @desc    Lấy nhóm Zalo của user hiện tại (để chọn khi tạo thông báo)
// @access  Private
router.get('/user/groups', protect, getUserZaloGroups);

// @route   POST /api/zalo/send-message
// @desc    Gửi tin nhắn đến nhóm <PERSON>alo (chỉ được gửi đến nhóm mà user tham gia)
// @access  Private
router.post('/send-message', protect, sendMessageToZaloGroup);

// @route   POST /api/zalo/send-user-message
// @desc    Gửi tin nhắn đến người dùng Zalo
// @access  Private
router.post('/send-user-message', protect, sendMessageToZaloUser);

// ========== ADMIN ROUTES ==========

// @route   POST /api/zalo/admin/user/:userId/groups
// @desc    Admin thêm nhiều nhóm cho 1 user
// @access  Private (Admin only)
router.post('/admin/user/:userId/groups', protect, authorize(ROLES.ADMIN), adminAddUserToZaloGroup);

// @route   POST /api/zalo/admin/groups/:groupId/users
// @desc    Admin thêm nhiều user vào 1 nhóm
// @access  Private (Admin only)
router.post('/admin/groups/:groupId/users', protect, authorize(ROLES.ADMIN), adminAddUsersToZaloGroup);

// @route   DELETE /api/zalo/admin/user/:userId/groups/:groupId
// @desc    Admin xóa user khỏi nhóm Zalo
// @access  Private (Admin only)
router.delete('/admin/user/:userId/groups/:groupId', protect, authorize(ROLES.ADMIN), adminRemoveUserFromZaloGroup);

module.exports = router; 