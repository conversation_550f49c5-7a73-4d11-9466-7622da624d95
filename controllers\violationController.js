// controllers/violationController.js
const Violation = require('../models/Violation');
const StudentConduct = require('../models/StudentConduct');
const User = require('../models/User');
const Class = require('../models/Class');
const StudentEnrollment = require('../models/StudentEnrollment');
const asyncHandler = require('../middlewares/async');
const MESSAGES = require('../constants/messages');
const ROLES = require('../constants/roleConstants');
const {
  VIOLATION_TYPES,
  VIOLATION_TYPE_LABELS,
  VIOLATION_STATUS,
  VIOLATION_STATUS_LABELS,
  VIOLATION_POINTS,
  DEFAULT_VIOLATION_MESSAGES,
  VIOLATION_PERMISSIONS
} = require('../constants/violationConstants');
const { HTTP_STATUS, createSuccessResponse, createErrorResponse } = require('../constants/httpConstants');
const { sendAllViolationNotifications } = require('../services/violationNotificationService');

// @desc    Tạo báo cáo vi phạm mới
// @route   POST /api/violations
// @access  Private (Admin, Teacher)
exports.createViolation = asyncHandler(async (req, res) => {
  const {
    studentId,
    classId,
    schoolYear,
    violationType,
    description,
    violationDate,
    pointsDeducted,
    notes
  } = req.body;

  // Kiểm tra quyền hạn
  if (!VIOLATION_PERMISSIONS.CREATE.some(role => req.user.role.includes(role))) {
    return res.status(HTTP_STATUS.FORBIDDEN).json(
      createErrorResponse(MESSAGES.ERROR.ACCESS_DENIED)
    );
  }

  // Validate dữ liệu đầu vào
  if (!studentId || !classId || !schoolYear || !violationType || !description) {
    return res.status(HTTP_STATUS.BAD_REQUEST).json(
      createErrorResponse('Thiếu thông tin bắt buộc')
    );
  }

  // Kiểm tra học sinh tồn tại
  const student = await User.findById(studentId);
  if (!student || !student.role.includes(ROLES.STUDENT)) {
    return res.status(HTTP_STATUS.NOT_FOUND).json(
      createErrorResponse('Không tìm thấy học sinh')
    );
  }

  // Kiểm tra lớp tồn tại
  const classObj = await Class.findById(classId);
  if (!classObj) {
    return res.status(HTTP_STATUS.NOT_FOUND).json(
      createErrorResponse('Không tìm thấy lớp học')
    );
  }

  // Kiểm tra học sinh có trong lớp không
  const enrollment = await StudentEnrollment.findOne({
    student: studentId,
    class: classId,
    schoolYear: schoolYear,
    status: 'active'
  });

  if (!enrollment) {
    return res.status(HTTP_STATUS.BAD_REQUEST).json(
      createErrorResponse('Học sinh không thuộc lớp này trong năm học được chọn')
    );
  }

  // Tạo vi phạm mới
  const violation = new Violation({
    student: studentId,
    class: classId,
    schoolYear,
    violationType,
    description,
    violationDate: violationDate || new Date(),
    pointsDeducted: pointsDeducted || VIOLATION_POINTS[violationType] || 1,
    reportedBy: req.user.id,
    notes
  });

  await violation.save();

  // Cập nhật điểm thi đua của học sinh
  const studentConduct = await StudentConduct.getOrCreateStudentConduct(
    studentId, 
    schoolYear, 
    classId
  );

  await studentConduct.deductPoints(
    violation.pointsDeducted,
    `Vi phạm: ${VIOLATION_TYPE_LABELS[violationType]}`,
    violation._id,
    req.user.id
  );

  // Populate thông tin để trả về
  await violation.populate([
    { path: 'student', select: 'name studentId' },
    { path: 'class', select: 'name' },
    { path: 'reportedBy', select: 'name' }
  ]);

  // Gửi thông báo vi phạm
  try {
    const notificationResults = await sendAllViolationNotifications(
      violation,
      studentConduct,
      req.user
    );

    // Cập nhật trạng thái đã gửi thông báo
    violation.notificationSent = true;
    violation.notificationSentAt = new Date();
    await violation.save();

    console.log('✅ Đã gửi thông báo vi phạm:', notificationResults);
  } catch (notificationError) {
    console.error('❌ Lỗi khi gửi thông báo vi phạm:', notificationError);
    // Không throw error để không ảnh hưởng đến việc tạo vi phạm
  }

  res.status(HTTP_STATUS.CREATED).json(
    createSuccessResponse({
      violation,
      remainingPoints: studentConduct.currentPoints,
      totalViolations: studentConduct.totalViolations
    }, 'Tạo báo cáo vi phạm thành công')
  );
});

// @desc    Lấy danh sách vi phạm
// @route   GET /api/violations
// @access  Private
exports.getViolations = asyncHandler(async (req, res) => {
  const {
    page = 1,
    limit = 10,
    studentId,
    classId,
    schoolYear,
    violationType,
    status,
    startDate,
    endDate
  } = req.query;

  // Xây dựng query filter
  let filter = {};

  // Nếu là học sinh, chỉ xem vi phạm của mình
  if (req.user.role.includes(ROLES.STUDENT)) {
    filter.student = req.user.id;
  } else {
    // Giáo viên/Admin có thể filter theo studentId
    if (studentId) {
      filter.student = studentId;
    }
  }

  if (classId) filter.class = classId;
  if (schoolYear) filter.schoolYear = schoolYear;
  if (violationType) filter.violationType = violationType;
  if (status) filter.status = status;

  // Filter theo ngày
  if (startDate || endDate) {
    filter.violationDate = {};
    if (startDate) filter.violationDate.$gte = new Date(startDate);
    if (endDate) filter.violationDate.$lte = new Date(endDate);
  }

  const options = {
    page: parseInt(page),
    limit: parseInt(limit),
    sort: { violationDate: -1 },
    populate: [
      { path: 'student', select: 'name studentId' },
      { path: 'class', select: 'name' },
      { path: 'reportedBy', select: 'name' },
      { path: 'processedBy', select: 'name' }
    ]
  };

  const violations = await Violation.paginate(filter, options);

  res.status(HTTP_STATUS.OK).json(
    createSuccessResponse(violations, 'Lấy danh sách vi phạm thành công')
  );
});

// @desc    Lấy chi tiết vi phạm
// @route   GET /api/violations/:id
// @access  Private
exports.getViolationById = asyncHandler(async (req, res) => {
  const violation = await Violation.findById(req.params.id)
    .populate('student', 'name studentId phoneNumber')
    .populate('class', 'name')
    .populate('reportedBy', 'name')
    .populate('processedBy', 'name')
    .populate('appealProcessedBy', 'name');

  if (!violation) {
    return res.status(HTTP_STATUS.NOT_FOUND).json(
      createErrorResponse('Không tìm thấy vi phạm')
    );
  }

  // Kiểm tra quyền xem
  if (req.user.role.includes(ROLES.STUDENT) && 
      violation.student._id.toString() !== req.user.id) {
    return res.status(HTTP_STATUS.FORBIDDEN).json(
      createErrorResponse(MESSAGES.ERROR.ACCESS_DENIED)
    );
  }

  res.status(HTTP_STATUS.OK).json(
    createSuccessResponse(violation, 'Lấy chi tiết vi phạm thành công')
  );
});

// @desc    Cập nhật vi phạm
// @route   PUT /api/violations/:id
// @access  Private (Admin, Teacher)
exports.updateViolation = asyncHandler(async (req, res) => {
  // Kiểm tra quyền hạn
  if (!VIOLATION_PERMISSIONS.UPDATE.some(role => req.user.role.includes(role))) {
    return res.status(HTTP_STATUS.FORBIDDEN).json(
      createErrorResponse(MESSAGES.ERROR.ACCESS_DENIED)
    );
  }

  const violation = await Violation.findById(req.params.id);
  if (!violation) {
    return res.status(HTTP_STATUS.NOT_FOUND).json(
      createErrorResponse('Không tìm thấy vi phạm')
    );
  }

  // Chỉ cho phép cập nhật một số trường
  const allowedFields = ['description', 'notes', 'status'];
  const updateData = {};
  
  allowedFields.forEach(field => {
    if (req.body[field] !== undefined) {
      updateData[field] = req.body[field];
    }
  });

  // Nếu cập nhật trạng thái thành processed
  if (updateData.status === VIOLATION_STATUS.PROCESSED) {
    updateData.processedBy = req.user.id;
    updateData.processedAt = new Date();
  }

  Object.assign(violation, updateData);
  await violation.save();

  await violation.populate([
    { path: 'student', select: 'name studentId' },
    { path: 'class', select: 'name' },
    { path: 'reportedBy', select: 'name' },
    { path: 'processedBy', select: 'name' }
  ]);

  res.status(HTTP_STATUS.OK).json(
    createSuccessResponse(violation, 'Cập nhật vi phạm thành công')
  );
});

// @desc    Xóa vi phạm
// @route   DELETE /api/violations/:id
// @access  Private (Admin only)
exports.deleteViolation = asyncHandler(async (req, res) => {
  // Kiểm tra quyền hạn
  if (!VIOLATION_PERMISSIONS.DELETE.some(role => req.user.role.includes(role))) {
    return res.status(HTTP_STATUS.FORBIDDEN).json(
      createErrorResponse(MESSAGES.ERROR.ACCESS_DENIED)
    );
  }

  const violation = await Violation.findById(req.params.id);
  if (!violation) {
    return res.status(HTTP_STATUS.NOT_FOUND).json(
      createErrorResponse('Không tìm thấy vi phạm')
    );
  }

  // Hoàn tác điểm thi đua nếu vi phạm đã được xử lý
  if (violation.status === VIOLATION_STATUS.PROCESSED) {
    const studentConduct = await StudentConduct.findOne({
      student: violation.student,
      schoolYear: violation.schoolYear
    });

    if (studentConduct) {
      await studentConduct.revertViolation(violation._id, req.user.id);
    }
  }

  await violation.deleteOne();

  res.status(HTTP_STATUS.OK).json(
    createSuccessResponse(null, 'Xóa vi phạm thành công')
  );
});

// @desc    Khiếu nại vi phạm
// @route   POST /api/violations/:id/appeal
// @access  Private (Student)
exports.appealViolation = asyncHandler(async (req, res) => {
  const { appealReason } = req.body;

  if (!appealReason) {
    return res.status(HTTP_STATUS.BAD_REQUEST).json(
      createErrorResponse('Vui lòng nhập lý do khiếu nại')
    );
  }

  const violation = await Violation.findById(req.params.id);
  if (!violation) {
    return res.status(HTTP_STATUS.NOT_FOUND).json(
      createErrorResponse('Không tìm thấy vi phạm')
    );
  }

  // Kiểm tra quyền khiếu nại (chỉ học sinh bị vi phạm)
  if (violation.student.toString() !== req.user.id) {
    return res.status(HTTP_STATUS.FORBIDDEN).json(
      createErrorResponse('Bạn chỉ có thể khiếu nại vi phạm của chính mình')
    );
  }

  // Kiểm tra trạng thái vi phạm
  if (violation.status !== VIOLATION_STATUS.PROCESSED) {
    return res.status(HTTP_STATUS.BAD_REQUEST).json(
      createErrorResponse('Chỉ có thể khiếu nại vi phạm đã được xử lý')
    );
  }

  await violation.appealViolation(appealReason);

  res.status(HTTP_STATUS.OK).json(
    createSuccessResponse(violation, 'Gửi khiếu nại thành công')
  );
});

// @desc    Xử lý khiếu nại vi phạm
// @route   POST /api/violations/:id/process-appeal
// @access  Private (Admin, Teacher)
exports.processAppeal = asyncHandler(async (req, res) => {
  const { result } = req.body; // 'approved' hoặc 'rejected'

  if (!['approved', 'rejected'].includes(result)) {
    return res.status(HTTP_STATUS.BAD_REQUEST).json(
      createErrorResponse('Kết quả xử lý không hợp lệ')
    );
  }

  const violation = await Violation.findById(req.params.id);
  if (!violation) {
    return res.status(HTTP_STATUS.NOT_FOUND).json(
      createErrorResponse('Không tìm thấy vi phạm')
    );
  }

  if (violation.status !== VIOLATION_STATUS.APPEALED) {
    return res.status(HTTP_STATUS.BAD_REQUEST).json(
      createErrorResponse('Vi phạm này không trong trạng thái khiếu nại')
    );
  }

  await violation.processAppeal(result, req.user.id);

  // Nếu khiếu nại được chấp nhận, hoàn tác điểm thi đua
  if (result === 'approved') {
    const studentConduct = await StudentConduct.findOne({
      student: violation.student,
      schoolYear: violation.schoolYear
    });

    if (studentConduct) {
      await studentConduct.revertViolation(violation._id, req.user.id);
    }
  }

  res.status(HTTP_STATUS.OK).json(
    createSuccessResponse(violation, `${result === 'approved' ? 'Chấp nhận' : 'Từ chối'} khiếu nại thành công`)
  );
});

// @desc    Lấy vi phạm của học sinh
// @route   GET /api/violations/student/:studentId
// @access  Private
exports.getStudentViolations = asyncHandler(async (req, res) => {
  const { studentId } = req.params;
  const { schoolYear } = req.query;

  // Kiểm tra quyền xem
  if (req.user.role.includes(ROLES.STUDENT) && studentId !== req.user.id) {
    return res.status(HTTP_STATUS.FORBIDDEN).json(
      createErrorResponse(MESSAGES.ERROR.ACCESS_DENIED)
    );
  }

  const violations = await Violation.getStudentViolations(studentId, schoolYear);

  // Lấy thông tin điểm thi đua
  const studentConduct = await StudentConduct.getStudentConduct(studentId, schoolYear);

  res.status(HTTP_STATUS.OK).json(
    createSuccessResponse({
      violations,
      conduct: studentConduct
    }, 'Lấy vi phạm của học sinh thành công')
  );
});

// @desc    Lấy vi phạm của lớp
// @route   GET /api/violations/class/:classId
// @access  Private (Admin, Teacher)
exports.getClassViolations = asyncHandler(async (req, res) => {
  const { classId } = req.params;
  const { schoolYear } = req.query;

  const violations = await Violation.getClassViolations(classId, schoolYear);

  res.status(HTTP_STATUS.OK).json(
    createSuccessResponse(violations, 'Lấy vi phạm của lớp thành công')
  );
});

// @desc    Lấy thống kê vi phạm
// @route   GET /api/violations/stats
// @access  Private (Admin, Teacher)
exports.getViolationStats = asyncHandler(async (req, res) => {
  const { schoolYear, classId, startDate, endDate } = req.query;

  let matchCondition = {};
  if (schoolYear) matchCondition.schoolYear = schoolYear;
  if (classId) matchCondition.class = mongoose.Types.ObjectId(classId);
  if (startDate || endDate) {
    matchCondition.violationDate = {};
    if (startDate) matchCondition.violationDate.$gte = new Date(startDate);
    if (endDate) matchCondition.violationDate.$lte = new Date(endDate);
  }

  const stats = await Violation.aggregate([
    { $match: matchCondition },
    {
      $group: {
        _id: '$violationType',
        count: { $sum: 1 },
        totalPoints: { $sum: '$pointsDeducted' }
      }
    },
    {
      $lookup: {
        from: 'users',
        localField: '_id',
        foreignField: '_id',
        as: 'typeInfo'
      }
    },
    {
      $project: {
        violationType: '$_id',
        violationTypeLabel: {
          $switch: {
            branches: Object.entries(VIOLATION_TYPE_LABELS).map(([key, value]) => ({
              case: { $eq: ['$_id', key] },
              then: value
            })),
            default: '$_id'
          }
        },
        count: 1,
        totalPoints: 1
      }
    },
    { $sort: { count: -1 } }
  ]);

  // Thống kê tổng quan
  const totalStats = await Violation.aggregate([
    { $match: matchCondition },
    {
      $group: {
        _id: null,
        totalViolations: { $sum: 1 },
        totalPointsDeducted: { $sum: '$pointsDeducted' },
        uniqueStudents: { $addToSet: '$student' }
      }
    },
    {
      $project: {
        totalViolations: 1,
        totalPointsDeducted: 1,
        uniqueStudentsCount: { $size: '$uniqueStudents' }
      }
    }
  ]);

  res.status(HTTP_STATUS.OK).json(
    createSuccessResponse({
      byType: stats,
      overall: totalStats[0] || {
        totalViolations: 0,
        totalPointsDeducted: 0,
        uniqueStudentsCount: 0
      }
    }, 'Lấy thống kê vi phạm thành công')
  );
});

// @desc    Lấy điểm thi đua của học sinh
// @route   GET /api/student-conduct/:studentId
// @access  Private
exports.getStudentConduct = asyncHandler(async (req, res) => {
  const { studentId } = req.params;
  const { schoolYear } = req.query;

  // Kiểm tra quyền xem
  if (req.user.role.includes(ROLES.STUDENT) && studentId !== req.user.id) {
    return res.status(HTTP_STATUS.FORBIDDEN).json(
      createErrorResponse(MESSAGES.ERROR.ACCESS_DENIED)
    );
  }

  const conduct = await StudentConduct.getStudentConduct(studentId, schoolYear);

  if (!conduct) {
    return res.status(HTTP_STATUS.NOT_FOUND).json(
      createErrorResponse('Không tìm thấy thông tin điểm thi đua')
    );
  }

  res.status(HTTP_STATUS.OK).json(
    createSuccessResponse(conduct, 'Lấy điểm thi đua thành công')
  );
});

// @desc    Cập nhật điểm thi đua của học sinh
// @route   PUT /api/student-conduct/:studentId
// @access  Private (Admin, Teacher)
exports.updateStudentConduct = asyncHandler(async (req, res) => {
  const { studentId } = req.params;
  const { schoolYear, points, reason, type } = req.body;

  // Kiểm tra quyền hạn
  if (!req.user.role.includes(ROLES.ADMIN) && !req.user.role.includes(ROLES.TEACHER)) {
    return res.status(HTTP_STATUS.FORBIDDEN).json(
      createErrorResponse(MESSAGES.ERROR.ACCESS_DENIED)
    );
  }

  if (!schoolYear || !points || !reason || !type) {
    return res.status(HTTP_STATUS.BAD_REQUEST).json(
      createErrorResponse('Thiếu thông tin bắt buộc')
    );
  }

  const conduct = await StudentConduct.findOne({
    student: studentId,
    schoolYear: schoolYear
  });

  if (!conduct) {
    return res.status(HTTP_STATUS.NOT_FOUND).json(
      createErrorResponse('Không tìm thấy thông tin điểm thi đua')
    );
  }

  // Thực hiện cập nhật điểm theo loại
  switch (type) {
    case 'add':
      await conduct.addPoints(points, reason, req.user.id);
      break;
    case 'deduct':
      await conduct.deductPoints(points, reason, null, req.user.id);
      break;
    case 'adjust':
      await conduct.adjustPoints(points, reason, req.user.id);
      break;
    default:
      return res.status(HTTP_STATUS.BAD_REQUEST).json(
        createErrorResponse('Loại cập nhật không hợp lệ')
      );
  }

  await conduct.populate([
    { path: 'student', select: 'name studentId' },
    { path: 'class', select: 'name' },
    { path: 'lastUpdatedBy', select: 'name' }
  ]);

  res.status(HTTP_STATUS.OK).json(
    createSuccessResponse(conduct, 'Cập nhật điểm thi đua thành công')
  );
});

// @desc    Lấy thống kê điểm thi đua của lớp
// @route   GET /api/student-conduct/class/:classId/stats
// @access  Private (Admin, Teacher)
exports.getClassConductStats = asyncHandler(async (req, res) => {
  const { classId } = req.params;
  const { schoolYear } = req.query;

  const stats = await StudentConduct.getClassConductStats(classId, schoolYear);

  res.status(HTTP_STATUS.OK).json(
    createSuccessResponse(stats, 'Lấy thống kê điểm thi đua của lớp thành công')
  );
});

// @desc    Lấy cấu hình vi phạm
// @route   GET /api/violations/config
// @access  Private
exports.getViolationConfig = asyncHandler(async (req, res) => {
  const config = {
    violationTypes: Object.entries(VIOLATION_TYPE_LABELS).map(([key, value]) => ({
      code: key,
      label: value,
      points: VIOLATION_POINTS[key] || 1
    })),
    violationStatuses: Object.entries(VIOLATION_STATUS_LABELS).map(([key, value]) => ({
      code: key,
      label: value
    })),
    permissions: VIOLATION_PERMISSIONS
  };

  res.status(HTTP_STATUS.OK).json(
    createSuccessResponse(config, 'Lấy cấu hình vi phạm thành công')
  );
});

// @desc    Lấy thống kê vi phạm theo thời gian
// @route   GET /api/violations/stats/timeline
// @access  Private (Admin, Teacher)
exports.getViolationTimelineStats = asyncHandler(async (req, res) => {
  const { schoolYear, classId, period = 'month' } = req.query;

  let matchCondition = {};
  if (schoolYear) matchCondition.schoolYear = schoolYear;
  if (classId) matchCondition.class = mongoose.Types.ObjectId(classId);

  // Xác định format ngày theo period
  let dateFormat;
  switch (period) {
    case 'day':
      dateFormat = '%Y-%m-%d';
      break;
    case 'week':
      dateFormat = '%Y-%U'; // Year-Week
      break;
    case 'month':
      dateFormat = '%Y-%m';
      break;
    case 'year':
      dateFormat = '%Y';
      break;
    default:
      dateFormat = '%Y-%m';
  }

  const timelineStats = await Violation.aggregate([
    { $match: matchCondition },
    {
      $group: {
        _id: {
          period: { $dateToString: { format: dateFormat, date: '$violationDate' } },
          violationType: '$violationType'
        },
        count: { $sum: 1 },
        totalPoints: { $sum: '$pointsDeducted' },
        students: { $addToSet: '$student' }
      }
    },
    {
      $group: {
        _id: '$_id.period',
        violations: {
          $push: {
            type: '$_id.violationType',
            count: '$count',
            totalPoints: '$totalPoints',
            studentCount: { $size: '$students' }
          }
        },
        totalViolations: { $sum: '$count' },
        totalPoints: { $sum: '$totalPoints' }
      }
    },
    { $sort: { _id: 1 } }
  ]);

  res.status(HTTP_STATUS.OK).json(
    createSuccessResponse(timelineStats, 'Lấy thống kê vi phạm theo thời gian thành công')
  );
});

// @desc    Lấy thống kê top học sinh vi phạm
// @route   GET /api/violations/stats/top-students
// @access  Private (Admin, Teacher)
exports.getTopViolationStudents = asyncHandler(async (req, res) => {
  const { schoolYear, classId, limit = 10 } = req.query;

  let matchCondition = {};
  if (schoolYear) matchCondition.schoolYear = schoolYear;
  if (classId) matchCondition.class = mongoose.Types.ObjectId(classId);

  const topStudents = await Violation.aggregate([
    { $match: matchCondition },
    {
      $group: {
        _id: '$student',
        violationCount: { $sum: 1 },
        totalPointsDeducted: { $sum: '$pointsDeducted' },
        violationTypes: { $addToSet: '$violationType' },
        lastViolation: { $max: '$violationDate' }
      }
    },
    {
      $lookup: {
        from: 'users',
        localField: '_id',
        foreignField: '_id',
        as: 'studentInfo'
      }
    },
    {
      $lookup: {
        from: 'studentconducts',
        let: { studentId: '$_id', year: schoolYear },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ['$student', '$$studentId'] },
                  { $eq: ['$schoolYear', '$$year'] }
                ]
              }
            }
          }
        ],
        as: 'conductInfo'
      }
    },
    {
      $project: {
        student: { $arrayElemAt: ['$studentInfo', 0] },
        conduct: { $arrayElemAt: ['$conductInfo', 0] },
        violationCount: 1,
        totalPointsDeducted: 1,
        violationTypes: 1,
        lastViolation: 1
      }
    },
    { $sort: { violationCount: -1, totalPointsDeducted: -1 } },
    { $limit: parseInt(limit) }
  ]);

  res.status(HTTP_STATUS.OK).json(
    createSuccessResponse(topStudents, 'Lấy thống kê top học sinh vi phạm thành công')
  );
});

// @desc    Lấy thống kê vi phạm theo lớp
// @route   GET /api/violations/stats/by-class
// @access  Private (Admin, Teacher)
exports.getViolationStatsByClass = asyncHandler(async (req, res) => {
  const { schoolYear } = req.query;

  let matchCondition = {};
  if (schoolYear) matchCondition.schoolYear = schoolYear;

  const clasStats = await Violation.aggregate([
    { $match: matchCondition },
    {
      $group: {
        _id: '$class',
        violationCount: { $sum: 1 },
        totalPointsDeducted: { $sum: '$pointsDeducted' },
        uniqueStudents: { $addToSet: '$student' },
        violationTypes: {
          $push: {
            type: '$violationType',
            points: '$pointsDeducted'
          }
        }
      }
    },
    {
      $lookup: {
        from: 'classes',
        localField: '_id',
        foreignField: '_id',
        as: 'classInfo'
      }
    },
    {
      $lookup: {
        from: 'studentenrollments',
        let: { classId: '$_id', year: schoolYear },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ['$class', '$$classId'] },
                  { $eq: ['$schoolYear', '$$year'] },
                  { $eq: ['$status', 'active'] }
                ]
              }
            }
          },
          { $count: 'totalStudents' }
        ],
        as: 'enrollmentInfo'
      }
    },
    {
      $project: {
        class: { $arrayElemAt: ['$classInfo', 0] },
        violationCount: 1,
        totalPointsDeducted: 1,
        violatingStudentCount: { $size: '$uniqueStudents' },
        totalStudents: {
          $ifNull: [
            { $arrayElemAt: ['$enrollmentInfo.totalStudents', 0] },
            0
          ]
        },
        violationRate: {
          $multiply: [
            {
              $divide: [
                { $size: '$uniqueStudents' },
                {
                  $ifNull: [
                    { $arrayElemAt: ['$enrollmentInfo.totalStudents', 0] },
                    1
                  ]
                }
              ]
            },
            100
          ]
        },
        avgViolationsPerStudent: {
          $divide: [
            '$violationCount',
            { $size: '$uniqueStudents' }
          ]
        }
      }
    },
    { $sort: { violationCount: -1 } }
  ]);

  res.status(HTTP_STATUS.OK).json(
    createSuccessResponse(clasStats, 'Lấy thống kê vi phạm theo lớp thành công')
  );
});
