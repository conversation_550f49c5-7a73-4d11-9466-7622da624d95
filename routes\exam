// routes/examRoutes.js
const express = require('express');
const router = express.Router();
const examController = require('../controllers/examController');
const { protect, authorize } = require('../middlewares/auth');

router.route('/statistics')
    .get(protect, examController.getExamStatistics);

// Đường dẫn cho đề thi
router.route('/')
    .get(protect, examController.getExams)
    .post(protect, authorize('teacher', 'admin'), examController.createExam);

// Đường dẫn để lấy đề thi do người dùng tạo (phải đặt trước route /:id)
router.route('/my-exams')
    .get(protect, authorize('teacher', 'admin'), examController.getMyExams);

router.route('/:id')
    .get(protect, examController.getExamById)
    .put(protect, authorize('teacher', 'admin'), examController.updateExam)
    .delete(protect, authorize('teacher', 'admin'), examController.deleteExam);

// Đường dẫn cho câu hỏi của đề thi
router.route('/:id/questions')
    .get(protect, authorize('teacher', 'admin'), examController.getQuestions)
    .post(protect, authorize('teacher', 'admin'), examController.addQuestion);

// Đường dẫn cho kết quả của đề thi
router.route('/:id/results')
    .get(protect, examController.getExamResults);

// Đường dẫn để bắt đầu làm bài
router.route('/:id/attempt')
    .post(protect, authorize('student'), examController.startExam);

// Đường dẫn cho câu hỏi riêng lẻ
router.route('/questions/:id')
    .put(protect, authorize('teacher', 'admin'), examController.updateQuestion)
    .delete(protect, authorize('teacher', 'admin'), examController.deleteQuestion);

// Đường dẫn cho lần làm bài
router.route('/exam-attempts/:id')
    .get(protect, examController.getAttemptDetail);

router.route('/exam-attempts/:id/answer')
    .put(protect, authorize('student'), examController.answerQuestion);

router.route('/exam-attempts/:id/submit')
    .put(protect, authorize('student'), examController.submitExam);

// Đường dẫn cho lần làm bài của học sinh
router.route('/students/:studentId/exam-attempts')
    .get(protect, examController.getStudentAttempts);

// Đường dẫn Phân công đề thi cho các lớp
router.route('/:id/assign-classes')
    .put(protect, authorize('teacher', 'admin'), examController.assignClassesToExam);

// Đường dẫn Hủy phân công đề thi cho lớp
router.route('/:id/unassign-class/:classId')
    .put(protect, authorize('teacher', 'admin'), examController.unassignClassFromExam);

module.exports = router;