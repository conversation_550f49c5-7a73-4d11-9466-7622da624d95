// src/models/User.js
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const ROLES = require('../constants/roleConstants');
const { GENDER, STUDENT_SPECIFIC_ROLES, TEACHER_SPECIFIC_ROLES, DISPLAY_ROLES, DEFAULT_SPECIFIC_ROLES } = require('../constants/userConstants');
const { VALID_DEPARTMENT_GROUPS, getDepartmentGroupBySubject } = require('../constants/departmentConstants');

const UserSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true
  },
  studentId: {
    type: String,
    unique: true,
    sparse: true, // Cho phép null với giáo viên và admin
    required: function () {
      return this.role.includes(ROLES.STUDENT);
    }
  },
  password: {
    type: String,
    required: true,
  },
  zaloId: {
    type: String,
    unique: true,
    sparse: true, // <PERSON> phép null nhưng vẫn unique
  },
  phoneNumber: {
    type: String,
    required: true,
    unique: true, // <PERSON><PERSON><PERSON> bảo mỗi số điện thoại chỉ thuộc về một người dùng
  },
  avatar: {
    type: String,
  },
  role: {
    type: [String],
    enum: Object.values(ROLES),
    default: [ROLES.STUDENT],
    validate: {
      validator: function(v) {
        return v && v.length > 0;
      },
      message: 'User phải có ít nhất một role'
    }
  },
  // Trường class đã được chuyển sang StudentEnrollment model để tránh data redundancy
  // và hỗ trợ quản lý học sinh theo năm học tốt hơn
  gender: {
    type: String,
    enum: Object.values(GENDER),
    required: true
  },
  specificRole: {
    type: String,
    enum: [
      ...Object.values(STUDENT_SPECIFIC_ROLES),
      ...Object.values(TEACHER_SPECIFIC_ROLES),
      null // Cho phép null cho admin
    ],
    default: function () {
      if (this.role.includes(ROLES.STUDENT)) return DEFAULT_SPECIFIC_ROLES[ROLES.STUDENT];
      if (this.role.includes(ROLES.TEACHER)) return DEFAULT_SPECIFIC_ROLES[ROLES.TEACHER];
      return null; // Mặc định là null cho admin
    },
    required: function () {
      return this.role.includes(ROLES.STUDENT) || this.role.includes(ROLES.TEACHER);
    }
  },
  // === BỘ MÔN FIELDS (CHỈ CHO TEACHER) ===
  teachingSubjects: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Subject',
    required: function () {
      return this.role.includes(ROLES.TEACHER);
    }
  }],
  department: {
    type: String,
    required: function () {
      return this.role.includes(ROLES.TEACHER);
    },
    enum: VALID_DEPARTMENT_GROUPS,
    trim: true
  },
  additionalDepartments: [{
    type: String,
    enum: VALID_DEPARTMENT_GROUPS,
    trim: true
  }],
  departmentHeadOf: {
    type: String,
    required: function () {
      return this.role.includes(ROLES.TEACHER) && this.specificRole === TEACHER_SPECIFIC_ROLES.DEPARTMENT_HEAD;
    },
    enum: VALID_DEPARTMENT_GROUPS,
    trim: true
  },
  // Trường group cũng được chuyển sang StudentEnrollment để quản lý theo năm học
  // group: {
  //   type: String,
  //   required: function () {
  //     return this.role === ROLES.STUDENT;
  //   }
  // },
  favorite: {
    type: Boolean,
    default: false
  },
  favoriteContacts: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  groupZaloIds: [{
    groupId: {
      type: String,
      required: true
    },
    groupName: {
      type: String,
      required: true
    },
    addedAt: {
      type: Date,
      default: Date.now
    }
  }],
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// === VALIDATION ===
UserSchema.pre('validate', function(next) {
  // Validate department head
  if (this.role.includes(ROLES.TEACHER) && this.specificRole === TEACHER_SPECIFIC_ROLES.DEPARTMENT_HEAD) {
    if (!this.departmentHeadOf) {
      return next(new Error('Department head phải có departmentHeadOf'));
    }
    const allDepartments = [this.department, ...this.additionalDepartments];
    if (!allDepartments.includes(this.departmentHeadOf)) {
      return next(new Error('Department head chỉ có thể làm trưởng của tổ mình dạy'));
    }
  }

  // Validate department field cho teacher
  if (this.role.includes(ROLES.TEACHER) && !this.department) {
    return next(new Error('Teacher phải có department'));
  }

  next();
});

// === INDEXES ===
UserSchema.index({ role: 1, department: 1 });
UserSchema.index({ role: 1, specificRole: 1 });
// Index cho class đã được chuyển sang StudentEnrollment model
// UserSchema.index({ class: 1, role: 1 });
UserSchema.index({ phoneNumber: 1 }, { unique: true });
UserSchema.index({ studentId: 1 }, { unique: true, sparse: true });
UserSchema.index({ zaloId: 1 }, { unique: true, sparse: true });

// Mã hóa mật khẩu trước khi lưu
UserSchema.pre('save', async function (next) {
  if (!this.isModified('password')) {
    return next();
  }
  const salt = await bcrypt.genSalt(10);
  this.password = await bcrypt.hash(this.password, salt);
  
  if (this.role.includes(ROLES.TEACHER) && this.teachingSubjects && this.teachingSubjects.length > 0) {
    await this.calculateDepartments();
  }
  next();
});

// Auto update department từ teachingSubject khi populate
UserSchema.post('populate', async function() {
  if (this.role.includes(ROLES.TEACHER) && this.teachingSubjects && this.teachingSubjects.length > 0) {
    await this.calculateDepartments();
  }
});

// So sánh mật khẩu
UserSchema.methods.matchPassword = async function (enteredPassword) {
  return await bcrypt.compare(enteredPassword, this.password);
};

// Thêm phương thức tạo thông tin hiển thị cho danh bạ
UserSchema.methods.getContactInfo = function () {
  let displayRole = '';
  let displayMeta = '';

  if (this.role.includes(ROLES.TEACHER)) {
    displayRole = DISPLAY_ROLES[this.specificRole] || 'Giáo viên';
    displayMeta = this.department || 'Đang tải thông tin tổ bộ môn...';
  } else if (this.role.includes(ROLES.STUDENT)) {
    displayRole = DISPLAY_ROLES[this.specificRole] || 'Học sinh';
  } else if (this.role.includes(ROLES.ADMIN)) {
    displayRole = 'Hiệu trưởng';
    displayMeta = 'Ban Giám Hiệu';
  }

  return {
    displayRole,
    displayMeta
  };
};

// Thêm phương thức mới để lấy thông tin chi tiết (bao gồm populate)
UserSchema.methods.getDetailedContactInfo = async function () {
  const basicInfo = this.getContactInfo();

  if (this.role.includes(ROLES.TEACHER) && this.teachingSubjects && this.teachingSubjects.length > 0) {
    await this.populate('teachingSubjects');
    await this.calculateDepartments();
    if (this.department) {
      basicInfo.displayMeta = this.department;
    }
  }

  return basicInfo;
};

// Method kiểm tra quyền department head
UserSchema.methods.isDepartmentHead = function(departmentGroup) {
  return this.role.includes(ROLES.TEACHER) && 
         this.specificRole === TEACHER_SPECIFIC_ROLES.DEPARTMENT_HEAD &&
         this.departmentHeadOf === departmentGroup;
};

// Method kiểm tra thuộc tổ bộ môn
UserSchema.methods.belongsToDepartment = function(departmentGroup) {
  return this.role.includes(ROLES.TEACHER) && this.department === departmentGroup;
};

// Method kiểm tra có dạy trong tổ bộ môn không (bao gồm cả tổ phụ)
UserSchema.methods.teachesInDepartment = function(departmentGroup) {
  return this.role.includes(ROLES.TEACHER) && 
         (this.department === departmentGroup || 
          (this.additionalDepartments && this.additionalDepartments.includes(departmentGroup)));
};

// Method để tính toán department và additionalDepartments từ teachingSubjects
UserSchema.methods.calculateDepartments = async function() {
  if (!this.role.includes(ROLES.TEACHER) || !this.teachingSubjects || this.teachingSubjects.length === 0) {
    return;
  }

  if (!this.teachingSubjects[0].name) {
    await this.populate('teachingSubjects');
  }

  const departmentGroups = [...new Set(
    this.teachingSubjects
      .map(subject => getDepartmentGroupBySubject(subject.name))
      .filter(group => group !== null)
  )];

  if (departmentGroups.length > 0) {
    this.department = departmentGroups[0];
    this.additionalDepartments = departmentGroups.slice(1);
  }
};

// Method để lấy lớp hiện tại của học sinh theo năm học
UserSchema.methods.getCurrentClass = async function(schoolYear = null) {
  if (!this.role.includes(ROLES.STUDENT)) {
    return null;
  }

  const StudentEnrollment = require('./StudentEnrollment');
  return await StudentEnrollment.getCurrentClass(this._id, schoolYear);
};

// Method để lấy lịch sử các lớp đã học
UserSchema.methods.getClassHistory = async function() {
  if (!this.role.includes(ROLES.STUDENT)) {
    return [];
  }

  const StudentEnrollment = require('./StudentEnrollment');
  return await StudentEnrollment.find({ student: this._id })
    .populate('class', 'name group schoolYear')
    .sort({ schoolYear: -1, enrollmentDate: -1 });
};

module.exports = mongoose.model('User', UserSchema);