/**
 * T<PERSON><PERSON> hợp các hằng số liên quan đến người dùng trong ứng dụng
 * Gi<PERSON><PERSON> thống nhất cách sử dụng và dễ dàng thay đổi sau này
 */

// Import roles từ file roles.js để tái sử dụng
const ROLES = require('./roleConstants');

// Giới tính
const GENDER = {
  MALE: 'male',
  FEMALE: 'female',
  OTHER: 'other'
};

// Vai trò cụ thể cho học sinh
const STUDENT_SPECIFIC_ROLES = {
  CLASS_MONITOR: 'class_monitor',
  VICE_MONITOR: 'vice_monitor',
  SECRETARY: 'secretary',
  REGULAR: 'regular'
};

// Vai trò cụ thể cho giáo viên
const TEACHER_SPECIFIC_ROLES = {
  HOMEROOM_TEACHER: 'homeroom_teacher',
  SUBJECT_TEACHER: 'subject_teacher',
  DEPARTMENT_HEAD: 'department_head'
};

// Tên hiển thị cho vai trò cụ thể (tiếng Việt)
const DISPLAY_ROLES = {
  // Học sinh
  [STUDENT_SPECIFIC_ROLES.CLASS_MONITOR]: 'Lớp trưởng',
  [STUDENT_SPECIFIC_ROLES.VICE_MONITOR]: 'Lớp phó',
  [STUDENT_SPECIFIC_ROLES.SECRETARY]: 'Bí thư lớp',
  [STUDENT_SPECIFIC_ROLES.REGULAR]: 'Học sinh',
  
  // Giáo viên
  [TEACHER_SPECIFIC_ROLES.HOMEROOM_TEACHER]: 'Giáo viên chủ nhiệm',
  [TEACHER_SPECIFIC_ROLES.SUBJECT_TEACHER]: 'Giáo viên bộ môn',
  [TEACHER_SPECIFIC_ROLES.DEPARTMENT_HEAD]: 'Trưởng bộ môn'
};

// Giá trị mặc định cho vai trò cụ thể dựa trên vai trò chính
const DEFAULT_SPECIFIC_ROLES = {
  [ROLES.STUDENT]: STUDENT_SPECIFIC_ROLES.REGULAR,
  [ROLES.TEACHER]: TEACHER_SPECIFIC_ROLES.SUBJECT_TEACHER
};

module.exports = {
  GENDER,
  STUDENT_SPECIFIC_ROLES,
  TEACHER_SPECIFIC_ROLES,
  DISPLAY_ROLES,
  DEFAULT_SPECIFIC_ROLES
};
