// src/controllers/directoryController.js
const User = require('../models/User');
const Class = require('../models/Class');
const Directory = require('../models/Directory');
const Schedule = require('../models/Schedule');
const StudentEnrollment = require('../models/StudentEnrollment');
const StudentEnrollmentService = require('../services/studentEnrollmentService');
const asyncHandler = require('../middlewares/async');
const MESSAGES = require('../constants/messages');
const { STUDENT_SPECIFIC_ROLES, TEACHER_SPECIFIC_ROLES } = require('../constants/userConstants');
const ROLES = require('../constants/roleConstants');
const SchoolYear = require('../models/SchoolYear');

// @desc    Lấy danh sách người dùng (có thể lọc theo role)
// @route   GET /api/directory
// @access  Private (Tất cả người dùng đã đăng nhập)
exports.getUsers = asyncHandler(async (req, res) => {
    const { role, search, favorite, classId, subject } = req.query;

    let query = {};

    // Lọc theo vai trò
    if (role && Object.values(ROLES).includes(role)) {
        query.role = role;
    }

    // Tìm kiếm theo tên, lớp, bộ môn
    if (search) {
        query.$or = [
            { name: { $regex: search, $options: 'i' } },
            { studentId: { $regex: search, $options: 'i' } }
        ];

        // Nếu là giáo viên thì tìm theo môn học
        if (role === ROLES.TEACHER || !role) {
            query.$or.push({ department: { $regex: search, $options: 'i' } });
        }
    }

    // Lọc theo lớp
    if (classId) {
        query.class = classId;
    }

    // Lọc theo bộ môn
    if (subject) {
        query.department = subject;
    }

    // Lấy danh sách người dùng yêu thích
    if (favorite === 'true') {
        const userDirectory = await Directory.findOne({ user: req.user.id });
        if (userDirectory && userDirectory.favoriteContacts.length > 0) {
            query._id = { $in: userDirectory.favoriteContacts };
        } else {
            // Nếu không có người dùng yêu thích nào, trả về danh sách rỗng
            return res.json({
                success: true,
                count: 0,
                data: []
            });
        }
    }

    // Thực hiện truy vấn (không populate class vì đã chuyển sang StudentEnrollment)
    const users = await User.find(query)
        .select('-password')
        .sort({ name: 1 });

    // Thêm thông tin yêu thích
    let favoriteContacts = [];
    if (req.user) {
        const userDirectory = await Directory.findOne({ user: req.user.id });
        if (userDirectory) {
            favoriteContacts = userDirectory.favoriteContacts.map(id => id.toString());
        }
    }

    // Định dạng dữ liệu trả về
    const formattedUsers = users.map(user => {
        const contactInfo = user.getContactInfo();
        return {
            id: user._id,
            name: user.name,
            role: user.role,
            specificRole: user.specificRole,
            gender: user.gender,
            class: user.class ? user.class.name : null,
            group: user.role === 'student' ? (user.class ? user.class.group : user.group) : null,
            subject: user.department,
            displayRole: contactInfo.displayRole,
            displayMeta: contactInfo.displayMeta,
            favorite: favoriteContacts.includes(user._id.toString())
        };
    });

    res.json({
        success: true,
        count: formattedUsers.length,
        data: formattedUsers
    });
});

// @desc    Lấy danh sách người dùng theo lớp
// @route   GET /api/directory/class/:classId
// @access  Private (Tất cả người dùng đã đăng nhập)
exports.getUsersByClass = asyncHandler(async (req, res) => {
    const { classId } = req.params;
    const { schoolYear } = req.query;

    // Kiểm tra lớp có tồn tại không
    const classObj = await Class.findById(classId);
    if (!classObj) {
        return res.status(404).json({
            success: false,
            msg: MESSAGES.CLASS.NOT_FOUND
        });
    }

    // Sử dụng schoolYear từ query hoặc từ class
    const targetYear = schoolYear || classObj.schoolYear;

    // Lấy danh sách học sinh trong lớp thông qua StudentEnrollment
    const enrollments = await StudentEnrollmentService.getClassStudents(classId, targetYear);
    const students = enrollments.map(enrollment => enrollment.student);

    // Lấy giáo viên chủ nhiệm
    const homeroomTeacher = await User.findById(classObj.homeroomTeacher)
        .select('-password');

    // Lấy các giáo viên bộ môn của lớp
    const subjectTeacherIds = classObj.subjectTeachers.map(st => st.teacher);
    const subjectTeachers = await User.find({ _id: { $in: subjectTeacherIds } })
        .select('-password');

    // Thêm thông tin yêu thích
    let favoriteContacts = [];
    if (req.user) {
        const userDirectory = await Directory.findOne({ user: req.user.id });
        if (userDirectory) {
            favoriteContacts = userDirectory.favoriteContacts.map(id => id.toString());
        }
    }

    // Định dạng dữ liệu học sinh
    const formattedStudents = students.map(student => {
        const contactInfo = student.getContactInfo();
        return {
            id: student._id,
            name: student.name,
            role: student.role,
            specificRole: student.specificRole,
            gender: student.gender,
            class: classObj.name,
            group: student.group || classObj.group,
            displayRole: contactInfo.displayRole,
            displayMeta: contactInfo.displayMeta,
            favorite: favoriteContacts.includes(student._id.toString())
        };
    });

    // Định dạng dữ liệu giáo viên
    const formattedTeachers = [];

    if (homeroomTeacher) {
        const contactInfo = homeroomTeacher.getContactInfo();
        formattedTeachers.push({
            id: homeroomTeacher._id,
            name: homeroomTeacher.name,
            role: homeroomTeacher.role,
            specificRole: TEACHER_SPECIFIC_ROLES.HOMEROOM_TEACHER,
            gender: homeroomTeacher.gender,
            subject: homeroomTeacher.department,
            displayRole: 'Giáo viên chủ nhiệm',
            displayMeta: contactInfo.displayMeta,
            favorite: favoriteContacts.includes(homeroomTeacher._id.toString())
        });
    }

    subjectTeachers.forEach(teacher => {
        if (teacher._id.toString() !== (homeroomTeacher ? homeroomTeacher._id.toString() : null)) {
            const contactInfo = teacher.getContactInfo();
            const subjectDetail = classObj.subjectTeachers.find(
                st => st.teacher.toString() === teacher._id.toString()
            );

            formattedTeachers.push({
                id: teacher._id,
                name: teacher.name,
                role: teacher.role,
                specificRole: teacher.specificRole,
                gender: teacher.gender,
                subject: subjectDetail ? subjectDetail.subject : teacher.department,
                displayRole: contactInfo.displayRole,
                displayMeta: contactInfo.displayMeta,
                favorite: favoriteContacts.includes(teacher._id.toString())
            });
        }
    });

    res.json({
        success: true,
        data: {
            classInfo: {
                id: classObj._id,
                name: classObj.name,
                group: classObj.group,
                schoolYear: classObj.schoolYear,
                classRoom: classObj.classRoom
            },
            students: formattedStudents,
            teachers: formattedTeachers
        }
    });
});

// @desc    Lấy danh sách giáo viên theo bộ môn
// @route   GET /api/directory/department/:subject
// @access  Private (Tất cả người dùng đã đăng nhập)
exports.getTeachersByDepartment = asyncHandler(async (req, res) => {
    const { subject } = req.params;

    // Lấy danh sách giáo viên theo bộ môn
    const teachers = await User.find({
        role: ROLES.TEACHER,
        department: subject
    })
        .select('-password')
        .sort({ specificRole: -1, name: 1 }); // Sắp xếp theo vai trò cụ thể (trưởng bộ môn lên đầu)

    // Thêm thông tin yêu thích
    let favoriteContacts = [];
    if (req.user) {
        const userDirectory = await Directory.findOne({ user: req.user.id });
        if (userDirectory) {
            favoriteContacts = userDirectory.favoriteContacts.map(id => id.toString());
        }
    }

    // Định dạng dữ liệu trả về
    const formattedTeachers = teachers.map(teacher => {
        const contactInfo = teacher.getContactInfo();
        return {
            id: teacher._id,
            name: teacher.name,
            role: teacher.role,
            specificRole: teacher.specificRole,
            gender: teacher.gender,
            subject: teacher.department,
            displayRole: contactInfo.displayRole,
            displayMeta: contactInfo.displayMeta,
            favorite: favoriteContacts.includes(teacher._id.toString())
        };
    });

    // Lấy thông tin về bộ môn
    let departmentName = '';
    switch (subject) {
        case 'math': departmentName = 'Toán học'; break;
        case 'literature': departmentName = 'Ngữ văn'; break;
        case 'english': departmentName = 'Tiếng Anh'; break;
        case 'physics': departmentName = 'Vật lý'; break;
        case 'chemistry': departmentName = 'Hóa học'; break;
        case 'biology': departmentName = 'Sinh học'; break;
        case 'history': departmentName = 'Lịch sử'; break;
        case 'geography': departmentName = 'Địa lý'; break;
        default: departmentName = subject;
    }

    res.json({
        success: true,
        data: {
            department: {
                id: subject,
                name: departmentName
            },
            teachers: formattedTeachers
        }
    });
});

// @desc    Thêm/xóa người dùng khỏi danh sách yêu thích
// @route   POST /api/directory/favorite/:userId
// @access  Private (Tất cả người dùng đã đăng nhập)
exports.toggleFavorite = asyncHandler(async (req, res) => {
    const { userId } = req.params;

    // Kiểm tra người dùng cần thêm vào yêu thích có tồn tại không
    const userToFavorite = await User.findById(userId);
    if (!userToFavorite) {
        return res.status(404).json({
            success: false,
            msg: MESSAGES.USER.NOT_FOUND
        });
    }

    // Lấy hoặc tạo mới directory của người dùng
    let directory = await Directory.findOne({ user: req.user.id });
    if (!directory) {
        directory = new Directory({
            user: req.user.id,
            favoriteContacts: [],
            recentContacts: []
        });
    }

    // Kiểm tra xem đã yêu thích chưa
    const favoriteIndex = directory.favoriteContacts.indexOf(userId);
    let isFavorite;

    if (favoriteIndex === -1) {
        // Chưa yêu thích, thêm vào
        directory.favoriteContacts.push(userId);
        isFavorite = true;
    } else {
        // Đã yêu thích, xóa đi
        directory.favoriteContacts.splice(favoriteIndex, 1);
        isFavorite = false;
    }

    directory.updatedAt = Date.now();
    await directory.save();

    res.json({
        success: true,
        data: {
            isFavorite
        }
    });
});

// @desc    Lấy thông tin chi tiết của một người dùng
// @route   GET /api/directory/user/:userId
// @access  Private (Tất cả người dùng đã đăng nhập)
exports.getUserDetail = asyncHandler(async (req, res) => {
    const { userId } = req.params;

    // Lấy thông tin người dùng (không populate class vì đã chuyển sang StudentEnrollment)
    const user = await User.findById(userId)
        .select('-password');

    if (!user) {
        return res.status(404).json({
            success: false,
            msg: MESSAGES.USER.NOT_FOUND
        });
    }

    // Kiểm tra xem người dùng có được yêu thích không
    let isFavorite = false;
    const directory = await Directory.findOne({ user: req.user.id });
    if (directory) {
        isFavorite = directory.favoriteContacts.includes(userId);
    }

    // Thêm vào danh sách liên hệ gần đây
    if (directory && userId !== req.user.id) {
        const recentIndex = directory.recentContacts.findIndex(
            contact => contact.contact.toString() === userId
        );

        if (recentIndex !== -1) {
            // Đã có trong danh sách gần đây, cập nhật thời gian
            directory.recentContacts[recentIndex].lastInteraction = Date.now();
        } else {
            // Chưa có trong danh sách gần đây, thêm vào
            directory.recentContacts.push({
                contact: userId,
                lastInteraction: Date.now()
            });
        }

        // Giới hạn danh sách liên hệ gần đây
        if (directory.recentContacts.length > 20) {
            // Sắp xếp theo thời gian tương tác gần nhất
            directory.recentContacts.sort((a, b) =>
                b.lastInteraction.getTime() - a.lastInteraction.getTime()
            );

            // Giữ lại 20 liên hệ gần nhất
            directory.recentContacts = directory.recentContacts.slice(0, 20);
        }

        directory.updatedAt = Date.now();
        await directory.save();
    }

    // Định dạng thông tin trả về
    const contactInfo = user.getContactInfo();

    // Lấy thêm thông tin lớp học nếu là giáo viên chủ nhiệm
    let homeroomClass = null;
    if (user.role === ROLES.TEACHER && user.specificRole === TEACHER_SPECIFIC_ROLES.HOMEROOM_TEACHER) {
        homeroomClass = await Class.findOne({ homeroomTeacher: userId })
            .select('name group schoolYear');
    }

    // Lấy thông tin lớp hiện tại từ StudentEnrollment
    if (req.user.role.includes(ROLES.TEACHER)) {
        const classes = await Class.find({ 'subjectTeachers.teacher': userId })
            .select('name group schoolYear');

        teachingClasses = classes.map(c => ({
            id: c._id,
            name: c.name,
            group: c.group,
            schoolYear: c.schoolYear
        }));
    }

    const userDetail = {
        id: user._id,
        name: user.name,
        studentId: user.studentId || null,
        phoneNumber: user.phoneNumber,
        avatar: user.avatar,
        role: user.role,
        zaloId: user.zaloId,
        specificRole: user.specificRole,
        gender: user.gender,
        class: user.class ? {
            id: user.class._id,
            name: user.class.name,
            group: user.class.group,
            schoolYear: user.class.schoolYear
        } : null,
        group: user.group,
        department: user.department,
        displayRole: contactInfo.displayRole,
        displayMeta: contactInfo.displayMeta,
        favorite: isFavorite
    };

    if (homeroomClass) {
        userDetail.homeroomClass = {
            id: homeroomClass._id,
            name: homeroomClass.name,
            group: homeroomClass.group,
            schoolYear: homeroomClass.schoolYear
        };
    }

    if (teachingClasses.length > 0) {
        userDetail.teachingClasses = teachingClasses;
    }

    res.json({
        success: true,
        data: userDetail
    });
});

// @desc    Lấy danh sách lớp học
// @route   GET /api/directory/classes
// @access  Private (admin, teacher có quyền xem tất cả; student chỉ xem lớp của mình)
exports.getClasses = asyncHandler(async (req, res) => {
    // Tùy thuộc vào vai trò mà hiển thị các lớp khác nhau
    let query = {};
    
    if (req.user.role.includes('teacher')) {
        // Giáo viên chỉ xem được các lớp mình dạy hoặc làm chủ nhiệm
        query = {
            $or: [
                { homeroomTeacher: req.user.id },
                { 'subjectTeachers.teacher': req.user.id }
            ]
        };
    } else if (req.user.role.includes('student')) {
        // Học sinh chỉ xem được lớp của mình
        // Logic này cần được implement dựa trên StudentEnrollment
        // Tạm thời return empty cho student
        return res.json({
            success: true,
            count: 0,
            data: []
        });
    }
    // Admin có thể xem tất cả các lớp (query = {})

    try {
        const classes = await Class.find(query)
            .populate('homeroomTeacher', 'name role specificRole')
            .sort({ schoolYear: -1, name: 1 });

        const formattedClasses = classes.map(cls => ({
            id: cls._id,
            name: cls.name,
            group: cls.group,
            schoolYear: cls.schoolYear,
            classRoom: cls.classRoom,
            homeroomTeacher: cls.homeroomTeacher ? {
                id: cls.homeroomTeacher._id,
                name: cls.homeroomTeacher.name,
                role: cls.homeroomTeacher.role,
                specificRole: cls.homeroomTeacher.specificRole
            } : null,
            subjectTeachers: cls.subjectTeachers || []
        }));

        res.json({
            success: true,
            count: formattedClasses.length,
            data: formattedClasses
        });
    } catch (error) {
        console.error('Error fetching classes:', error);
        res.status(500).json({
            success: false,
            msg: MESSAGES.ERROR.GENERAL
        });
    }
});

// @desc    Lấy danh sách bộ môn
// @route   GET /api/directory/departments
// @access  Private (tất cả người dùng đã đăng nhập)
exports.getDepartments = asyncHandler(async (req, res) => {
    res.json({
        success: true,
        data: VALID_DEPARTMENT_GROUPS
    });
});

// @desc    Tạo người dùng mới (học sinh hoặc giáo viên)
// @route   POST /api/directory/user
// @access  Private (admin)
exports.createUser = asyncHandler(async (req, res) => {
    const {
        name,
        studentId,
        password,
        phoneNumber,
        role,
        classId,
        gender,
        specificRole,
        teachingSubject,
        group
    } = req.body;

    // Kiểm tra dữ liệu đầu vào
    if (!name || !password || !phoneNumber || !gender) {
        return res.status(400).json({
            success: false,
            msg: MESSAGES.ERROR.VALIDATION_FAILED
        });
    }

    // Kiểm tra role hợp lệ
    if (!role || !['student', 'teacher', 'admin'].includes(role)) {
        return res.status(400).json({
            success: false,
            msg: MESSAGES.ERROR.VALIDATION_FAILED
        });
    }

    // Kiểm tra trường bắt buộc cho từng role
    if (role === ROLES.STUDENT && (!studentId || !classId)) {
        return res.status(400).json({
            success: false,
            msg: MESSAGES.ERROR.VALIDATION_FAILED
        });
    }

    if (role === ROLES.TEACHER && !teachingSubject) {
        return res.status(400).json({
            success: false,
            msg: MESSAGES.ERROR.VALIDATION_FAILED
        });
    }

    // Kiểm tra studentId có trùng không
    if (studentId) {
        const existingStudent = await User.findOne({ studentId });
        if (existingStudent) {
            return res.status(400).json({
                success: false,
                msg: MESSAGES.USER.ALREADY_EXISTS
            });
        }
    }

    // Kiểm tra số điện thoại có trùng không
    const existingPhone = await User.findOne({ phoneNumber });
    if (existingPhone) {
        return res.status(400).json({
            success: false,
            msg: MESSAGES.USER.ALREADY_EXISTS
        });
    }

    // Tạo người dùng mới
    const user = new User({
        name,
        studentId,
        password,
        phoneNumber,
        role: Array.isArray(role) ? role : [role], // Đảm bảo role là mảng
        gender,
        specificRole: specificRole || (role === ROLES.STUDENT ? STUDENT_SPECIFIC_ROLES.REGULAR : TEACHER_SPECIFIC_ROLES.SUBJECT_TEACHER),
        teachingSubject
    });

    await user.save();

    // Nếu là học sinh, tạo ghi danh vào lớp
    if (role === ROLES.STUDENT && classId) {
        // Lấy thông tin lớp để biết năm học
        const classObj = await Class.findById(classId);
        if (classObj) {
            await StudentEnrollmentService.enrollStudent(
                user._id,
                classId,
                classObj.schoolYear,
                group || 'A'
            );
        }
    }

    // Nếu là giáo viên chủ nhiệm, cập nhật lớp học
    if (role === ROLES.TEACHER && specificRole === TEACHER_SPECIFIC_ROLES.HOMEROOM_TEACHER && classId) {
        await Class.findByIdAndUpdate(
            classId,
            { homeroomTeacher: user._id }
        );
    }

    res.status(201).json({
        success: true,
        data: {
            id: user._id,
            name: user.name,
            studentId: user.studentId,
            phoneNumber: user.phoneNumber,
            role: user.role,
            gender: user.gender,
            specificRole: user.specificRole
        }
    });
});

// @desc    Cập nhật thông tin người dùng
// @route   PUT /api/directory/user/:id
// @access  Private (admin)
exports.updateUser = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const {
        name,
        studentId,
        phoneNumber,
        gender,
        specificRole,
        classId,
        group
    } = req.body;

    const user = await User.findById(id);
    if (!user) {
        return res.status(404).json({
            success: false,
            msg: MESSAGES.USER.NOT_FOUND
        });
    }

    // Cập nhật thông tin cơ bản
    if (name) user.name = name;
    if (phoneNumber) user.phoneNumber = phoneNumber;
    if (gender) user.gender = gender;
    if (specificRole) user.specificRole = specificRole;
    if (studentId && user.role.includes('student')) user.studentId = studentId;

    await user.save();

    // Xử lý cập nhật lớp cho học sinh
    if (user.role.includes('student') && classId) {
        // Cập nhật StudentEnrollment
        const { getCurrentSchoolYear } = require('../utils/schoolYear');
        const currentSchoolYear = getCurrentSchoolYear();

        await StudentEnrollmentService.updateStudentClass(
            id,
            classId,
            currentSchoolYear,
            group || 'A'
        );
    }

    res.json({
        success: true,
        data: {
            id: user._id,
            name: user.name,
            studentId: user.studentId,
            phoneNumber: user.phoneNumber,
            role: user.role,
            gender: user.gender,
            specificRole: user.specificRole
        }
    });
});

// @desc    Xóa người dùng
// @route   DELETE /api/directory/user/:id
// @access  Private (admin)
exports.deleteUser = asyncHandler(async (req, res) => {
    const { id } = req.params;

    const user = await User.findById(id);
    if (!user) {
        return res.status(404).json({
            success: false,
            msg: MESSAGES.USER.NOT_FOUND
        });
    }

    // Xóa các thông tin liên quan
    if (user.role.includes('student')) {
        await StudentEnrollment.deleteMany({ student: id });
    }

    await User.findByIdAndDelete(id);

    res.json({
        success: true,
        data: {}
    });
});

// @desc    Tạo lớp học mới
// @route   POST /api/directory/class
// @access  Private (admin)
exports.createClass = asyncHandler(async (req, res) => {
    const { name, group, schoolYear, classRoom, homeroomTeacherId } = req.body;

    // Kiểm tra dữ liệu đầu vào
    if (!name || !schoolYear) {
        return res.status(400).json({
            success: false,
            msg: MESSAGES.ERROR.VALIDATION_FAILED
        });
    }

    // Kiểm tra lớp đã tồn tại chưa
    const existingClass = await Class.findOne({ name, schoolYear });
    if (existingClass) {
        return res.status(400).json({
            success: false,
            msg: 'Lớp đã tồn tại trong năm học này'
        });
    }

    // Kiểm tra giáo viên chủ nhiệm nếu có
    if (homeroomTeacherId) {
        const teacher = await User.findById(homeroomTeacherId);
        if (!teacher || !teacher.role.includes('teacher')) {
            return res.status(400).json({
                success: false,
                msg: 'Giáo viên chủ nhiệm không hợp lệ'
            });
        }
    }

    const classObj = new Class({
        name,
        group: group || 'A',
        schoolYear,
        classRoom,
        homeroomTeacher: homeroomTeacherId || null
    });

    await classObj.save();

    res.status(201).json({
        success: true,
        data: {
            id: classObj._id,
            name: classObj.name,
            group: classObj.group,
            schoolYear: classObj.schoolYear,
            classRoom: classObj.classRoom,
            homeroomTeacher: classObj.homeroomTeacher
        }
    });
});

// @desc    Cập nhật thông tin lớp học
// @route   PUT /api/directory/class/:id
// @access  Private (admin)
exports.updateClass = asyncHandler(async (req, res) => {
    const { id } = req.params;
    const { name, group, classRoom, homeroomTeacherId } = req.body;

    const classObj = await Class.findById(id);
    if (!classObj) {
        return res.status(404).json({
            success: false,
            msg: MESSAGES.CLASS.NOT_FOUND
        });
    }

    // Kiểm tra giáo viên chủ nhiệm có hợp lệ không
    if (req.user.role.includes('teacher') && classObj.homeroomTeacher.toString() !== req.user.id) {
        return res.status(403).json({
            success: false,
            msg: MESSAGES.ERROR.FORBIDDEN
        });
    }

    // Cập nhật thông tin
    if (name) classObj.name = name;
    if (group) classObj.group = group;
    if (classRoom) classObj.classRoom = classRoom;
    if (homeroomTeacherId !== undefined) {
        if (homeroomTeacherId) {
            const teacher = await User.findById(homeroomTeacherId);
            if (!teacher || !teacher.role.includes('teacher')) {
                return res.status(400).json({
                    success: false,
                    msg: 'Giáo viên chủ nhiệm không hợp lệ'
                });
            }
        }
        classObj.homeroomTeacher = homeroomTeacherId;
    }

    await classObj.save();

    res.json({
        success: true,
        data: {
            id: classObj._id,
            name: classObj.name,
            group: classObj.group,
            schoolYear: classObj.schoolYear,
            classRoom: classObj.classRoom,
            homeroomTeacher: classObj.homeroomTeacher
        }
    });
});

// @desc    Xóa lớp học
// @route   DELETE /api/directory/class/:id
// @access  Private (admin)
exports.deleteClass = asyncHandler(async (req, res) => {
    const { id } = req.params;

    const classObj = await Class.findById(id);
    if (!classObj) {
        return res.status(404).json({
            success: false,
            msg: MESSAGES.CLASS.NOT_FOUND
        });
    }

    // Kiểm tra giáo viên có quyền xóa không
    if (req.user.role.includes('teacher') && classObj.homeroomTeacher.toString() !== req.user.id) {
        return res.status(403).json({
            success: false,
            msg: MESSAGES.ERROR.FORBIDDEN
        });
    }

    // Xóa các StudentEnrollment liên quan
    await StudentEnrollment.deleteMany({ class: id });

    await Class.findByIdAndDelete(id);

    res.json({
        success: true,
        data: {}
    });
});

// @desc    Lấy các lớp mà giáo viên dạy
// @route   GET /api/directory/teacher/classes
// @access  Private (giáo viên)
exports.getTeacherClasses = asyncHandler(async (req, res) => {
    const teacherId = req.user.id;
    const { schoolYear } = req.query;

    // Kiểm tra quyền truy cập
    if (!req.user.role.includes(ROLES.TEACHER)) {
        return res.status(403).json({
            success: false,
            msg: MESSAGES.ERROR.FORBIDDEN
        });
    }

    // Tìm các lớp mà giáo viên dạy (bao gồm cả chủ nhiệm và dạy môn)
    const query = {
        $or: [
            { homeroomTeacher: teacherId },
            { 'subjectTeachers.teacher': teacherId }
        ]
    };

    // Thêm filter schoolYear nếu có
    if (schoolYear) {
        query.schoolYear = schoolYear;
    }

    const classes = await Class.find(query)
        .populate('homeroomTeacher', 'name')
        .populate('subjectTeachers.teacher', 'name')
        .sort({ name: 1 });

    // Lấy thời khóa biểu cho tất cả các lớp mà giáo viên dạy
    const classIds = classes.map(cls => cls._id);
    const scheduleQuery = {
        class: { $in: classIds },
        'periods.teacher': teacherId
    };

    // Thêm filter schoolYear cho schedule nếu có
    if (schoolYear) {
        scheduleQuery.schoolYear = schoolYear;
    }

    const schedules = await Schedule.find(scheduleQuery)
        .populate('class', 'name')
        .sort({ day: 1, 'periods.periodNumber': 1 });

    const formattedClasses = await Promise.all(classes.map(async (cls) => {
        // Tìm các môn học mà giáo viên này dạy trong lớp
        const teachingSubjects = (cls.subjectTeachers || [])
            .filter(st => st.teacher && st.teacher._id && st.teacher._id.toString() === teacherId)
            .map(st => st.subject);

        // Kiểm tra xem có phải giáo viên chủ nhiệm không
        const isHomeroomTeacher = cls.homeroomTeacher &&
            cls.homeroomTeacher._id.toString() === teacherId;

        // Lấy các tiết dạy của giáo viên trong lớp này
        const classSchedules = schedules.filter(schedule =>
            schedule.class._id.toString() === cls._id.toString()
        );

        const teachingPeriods = [];
        classSchedules.forEach(schedule => {
            if (schedule.periods && Array.isArray(schedule.periods)) {
                schedule.periods.forEach(period => {
                    if (period.teacher && period.teacher.toString() === teacherId) {
                        teachingPeriods.push({
                            day: schedule.day,
                            periodNumber: period.periodNumber,
                            subject: period.subject,
                            room: period.room || cls.classRoom,
                            schoolYear: schedule.schoolYear
                        });
                    }
                });
            }
        });

        // Sắp xếp các tiết dạy theo ngày và tiết
        teachingPeriods.sort((a, b) => {
            const dayOrder = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
            const dayComparison = dayOrder.indexOf(a.day) - dayOrder.indexOf(b.day);
            if (dayComparison !== 0) return dayComparison;
            return a.periodNumber - b.periodNumber;
        });

        return {
            id: cls._id,
            name: cls.name,
            group: cls.group,
            schoolYear: cls.schoolYear,
            classRoom: cls.classRoom,
            studentCount: await StudentEnrollmentService.getStudentCount(cls._id.toString(), cls.schoolYear).catch(() => 0),
            role: isHomeroomTeacher ? 'homeroom' : 'subject',
            teachingSubjects: teachingSubjects,
            teachingPeriods: teachingPeriods,
            homeroomTeacher: cls.homeroomTeacher ? {
                id: cls.homeroomTeacher._id,
                name: cls.homeroomTeacher.name
            } : null
        };
    }));

    res.json({
        success: true,
        count: formattedClasses.length,
        data: formattedClasses
    });
});

// @desc    Lấy danh sách năm học
// @route   GET /api/directory/school-years
// @access  Private
const getSchoolYears = async (req, res) => {
  try {
    const schoolYears = await SchoolYear.find().sort({ startDate: -1 });
    const formattedYears = schoolYears.map(year => year.name);
    
    res.json({
      success: true,
      data: formattedYears
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Lỗi khi lấy danh sách năm học'
    });
  }
};

// @desc    Tạo năm học mới
// @route   POST /api/directory/school-year
// @access  Private (admin)
const createSchoolYear = async (req, res) => {
  try {
    const { startYear, endYear } = req.body;

    // Validate input
    if (!startYear || !endYear) {
      return res.status(400).json({
        success: false,
        error: 'Vui lòng cung cấp năm bắt đầu và năm kết thúc'
      });
    }

    // Validate year format
    if (!/^\d{4}$/.test(startYear) || !/^\d{4}$/.test(endYear)) {
      return res.status(400).json({
        success: false,
        error: 'Năm không đúng định dạng'
      });
    }

    // Validate year range
    if (parseInt(startYear) >= parseInt(endYear)) {
      return res.status(400).json({
        success: false,
        error: 'Năm bắt đầu phải nhỏ hơn năm kết thúc'
      });
    }

    const name = `${startYear}-${endYear}`;
    
    // Check if school year already exists
    const existingYear = await SchoolYear.findOne({ name });
    if (existingYear) {
      return res.status(400).json({
        success: false,
        error: 'Năm học này đã tồn tại'
      });
    }

    // Create start and end dates
    const startDate = new Date(`${startYear}-09-01`); // Bắt đầu từ 1/9
    const endDate = new Date(`${endYear}-05-31`); // Kết thúc vào 31/5

    const schoolYear = await SchoolYear.create({
      name,
      startDate,
      endDate,
      isActive: false // Mặc định là không active
    });

    res.status(201).json({
      success: true,
      data: schoolYear
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Lỗi khi tạo năm học mới'
    });
  }
};

// @desc    Xóa năm học
// @route   DELETE /api/directory/school-year/:schoolYear
// @access  Private (admin)
const deleteSchoolYear = async (req, res) => {
  try {
    const { schoolYear } = req.params;

    const year = await SchoolYear.findOne({ name: schoolYear });
    if (!year) {
      return res.status(404).json({
        success: false,
        error: 'Không tìm thấy năm học'
      });
    }

    // Kiểm tra xem năm học có đang active không
    if (year.isActive) {
      return res.status(400).json({
        success: false,
        error: 'Không thể xóa năm học đang active'
      });
    }

    await year.deleteOne();

    res.json({
      success: true,
      data: {}
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Lỗi khi xóa năm học'
    });
  }
};

// @desc    Lấy danh sách giáo viên
// @route   GET /api/directory/teachers
// @access  Private (Admin, Teacher)
exports.getTeachers = async (req, res) => {
  try {
    const { search, department, page = 1, limit = 10 } = req.query;
    const skip = (page - 1) * limit;

    // Xây dựng query
    let query = { role: 'teacher' }; // MongoDB sẽ tự động tìm trong mảng
    
    // Tìm kiếm theo tên hoặc email
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }

    // Lọc theo phòng ban
    if (department) {
      query.department = department;
    }

    // Thực hiện query với populate
    const teachers = await User.find(query)
      .select('name email department specificRoles phone avatar role')
      .sort({ name: 1 })
      .skip(skip)
      .limit(parseInt(limit));

    // Đếm tổng số giáo viên
    const total = await User.countDocuments(query);

    res.json({
      teachers,
      pagination: {
        current: parseInt(page),
        pages: Math.ceil(total / limit),
        total
      }
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ msg: 'Lỗi server' });
  }
};

module.exports = {
    getUsers: exports.getUsers,
    getUsersByClass: exports.getUsersByClass,
    getTeachersByDepartment: exports.getTeachersByDepartment,
    toggleFavorite: exports.toggleFavorite,
    getUserDetail: exports.getUserDetail,
    getClasses: exports.getClasses,
    getDepartments: exports.getDepartments,
    createUser: exports.createUser,
    updateUser: exports.updateUser,
    deleteUser: exports.deleteUser,
    createClass: exports.createClass,
    updateClass: exports.updateClass,
    deleteClass: exports.deleteClass,
    getTeacherClasses: exports.getTeacherClasses,
    getSchoolYears,
    createSchoolYear,
    deleteSchoolYear,
    getTeachers: exports.getTeachers
};