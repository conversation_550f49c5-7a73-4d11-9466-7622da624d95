// routes/violation.js
const express = require('express');
const router = express.Router();
const {
  createViolation,
  getViolations,
  getViolationById,
  updateViolation,
  deleteViolation,
  appealViolation,
  processAppeal,
  getStudentViolations,
  getClassViolations,
  getViolationStats,
  getViolationTimelineStats,
  getTopViolationStudents,
  getViolationStatsByClass,
  getStudentConduct,
  updateStudentConduct,
  getClassConductStats,
  getViolationConfig
} = require('../controllers/violationController');
const { protect, authorize } = require('../middlewares/auth');
const { check } = require('express-validator');
const { ROLES } = require('../constants/roleConstants');

// Validation middleware
const validateViolation = [
  check('studentId', 'Student ID là bắt buộc').notEmpty(),
  check('classId', 'Class ID là bắt buộc').notEmpty(),
  check('schoolYear', '<PERSON>ăm học là bắt buộc').notEmpty(),
  check('violationType', 'Loại vi phạm là bắt buộc').notEmpty(),
  check('description', 'Mô tả vi phạm là bắt buộc').notEmpty().isLength({ min: 5 })
];

const validateAppeal = [
  check('appealReason', 'Lý do khiếu nại là bắt buộc').notEmpty().isLength({ min: 10 })
];

const validateConductUpdate = [
  check('schoolYear', 'Năm học là bắt buộc').notEmpty(),
  check('points', 'Số điểm là bắt buộc').isNumeric(),
  check('reason', 'Lý do là bắt buộc').notEmpty(),
  check('type', 'Loại cập nhật là bắt buộc').isIn(['add', 'deduct', 'adjust'])
];

// @route   POST /api/violations
// @desc    Tạo báo cáo vi phạm mới
// @access  Private (Admin, Teacher)
router.post('/', 
  protect, 
  authorize([ROLES.ADMIN, ROLES.TEACHER]), 
  validateViolation, 
  createViolation
);

// @route   GET /api/violations
// @desc    Lấy danh sách vi phạm (có phân trang và filter)
// @access  Private
router.get('/', protect, getViolations);

// @route   GET /api/violations/config
// @desc    Lấy cấu hình vi phạm (types, statuses, permissions)
// @access  Private
router.get('/config', protect, getViolationConfig);

// @route   GET /api/violations/stats
// @desc    Lấy thống kê vi phạm
// @access  Private (Admin, Teacher)
router.get('/stats',
  protect,
  authorize([ROLES.ADMIN, ROLES.TEACHER]),
  getViolationStats
);

// @route   GET /api/violations/stats/timeline
// @desc    Lấy thống kê vi phạm theo thời gian
// @access  Private (Admin, Teacher)
router.get('/stats/timeline',
  protect,
  authorize([ROLES.ADMIN, ROLES.TEACHER]),
  getViolationTimelineStats
);

// @route   GET /api/violations/stats/top-students
// @desc    Lấy thống kê top học sinh vi phạm
// @access  Private (Admin, Teacher)
router.get('/stats/top-students',
  protect,
  authorize([ROLES.ADMIN, ROLES.TEACHER]),
  getTopViolationStudents
);

// @route   GET /api/violations/stats/by-class
// @desc    Lấy thống kê vi phạm theo lớp
// @access  Private (Admin, Teacher)
router.get('/stats/by-class',
  protect,
  authorize([ROLES.ADMIN, ROLES.TEACHER]),
  getViolationStatsByClass
);

// @route   GET /api/violations/student/:studentId
// @desc    Lấy vi phạm của học sinh cụ thể
// @access  Private
router.get('/student/:studentId', protect, getStudentViolations);

// @route   GET /api/violations/class/:classId
// @desc    Lấy vi phạm của lớp
// @access  Private (Admin, Teacher)
router.get('/class/:classId', 
  protect, 
  authorize([ROLES.ADMIN, ROLES.TEACHER]), 
  getClassViolations
);

// @route   GET /api/violations/:id
// @desc    Lấy chi tiết vi phạm
// @access  Private
router.get('/:id', protect, getViolationById);

// @route   PUT /api/violations/:id
// @desc    Cập nhật vi phạm
// @access  Private (Admin, Teacher)
router.put('/:id', 
  protect, 
  authorize([ROLES.ADMIN, ROLES.TEACHER]), 
  updateViolation
);

// @route   DELETE /api/violations/:id
// @desc    Xóa vi phạm
// @access  Private (Admin only)
router.delete('/:id', 
  protect, 
  authorize([ROLES.ADMIN]), 
  deleteViolation
);

// @route   POST /api/violations/:id/appeal
// @desc    Khiếu nại vi phạm
// @access  Private (Student)
router.post('/:id/appeal', 
  protect, 
  authorize([ROLES.STUDENT]), 
  validateAppeal, 
  appealViolation
);

// @route   POST /api/violations/:id/process-appeal
// @desc    Xử lý khiếu nại vi phạm
// @access  Private (Admin, Teacher)
router.post('/:id/process-appeal', 
  protect, 
  authorize([ROLES.ADMIN, ROLES.TEACHER]), 
  [check('result', 'Kết quả xử lý là bắt buộc').isIn(['approved', 'rejected'])],
  processAppeal
);

// Student Conduct Routes

// @route   GET /api/student-conduct/:studentId
// @desc    Lấy điểm thi đua của học sinh
// @access  Private
router.get('/conduct/:studentId', protect, getStudentConduct);

// @route   PUT /api/student-conduct/:studentId
// @desc    Cập nhật điểm thi đua của học sinh
// @access  Private (Admin, Teacher)
router.put('/conduct/:studentId', 
  protect, 
  authorize([ROLES.ADMIN, ROLES.TEACHER]), 
  validateConductUpdate, 
  updateStudentConduct
);

// @route   GET /api/student-conduct/class/:classId/stats
// @desc    Lấy thống kê điểm thi đua của lớp
// @access  Private (Admin, Teacher)
router.get('/conduct/class/:classId/stats', 
  protect, 
  authorize([ROLES.ADMIN, ROLES.TEACHER]), 
  getClassConductStats
);

module.exports = router;
