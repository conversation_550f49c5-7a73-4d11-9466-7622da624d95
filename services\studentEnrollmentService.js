// services/studentEnrollmentService.js - Service để quản lý việc ghi danh học sinh
const StudentEnrollment = require('../models/StudentEnrollment');
const User = require('../models/User');
const Class = require('../models/Class');
const { ROLES } = require('../constants/roleConstants');

class StudentEnrollmentService {
  
  /**
   * <PERSON>hi danh học sinh vào lớp
   * @param {string} studentId - ID của học sinh
   * @param {string} classId - ID của lớp
   * @param {string} schoolYear - Năm học (VD: "2024-2025")
   * @param {string} group - Nh<PERSON><PERSON> học sinh (VD: "A", "B")
   * @returns {Promise<Object>} Thông tin ghi danh
   */
  static async enrollStudent(studentId, classId, schoolYear, group = 'A') {
    // Kiểm tra học sinh có tồn tại không
    const student = await User.findById(studentId);
    if (!student || student.role !== ROLES.STUDENT) {
      throw new Error('<PERSON><PERSON><PERSON> sinh không tồn tại');
    }

    // Kiểm tra lớp có tồn tại không
    const classObj = await Class.findById(classId);
    if (!classObj) {
      throw new Error('Lớp học không tồn tại');
    }

    // Kiểm tra học sinh đã có lớp trong năm học này chưa
    const existingEnrollment = await StudentEnrollment.findOne({
      student: studentId,
      schoolYear: schoolYear,
      status: 'active'
    });

    if (existingEnrollment) {
      throw new Error('Học sinh đã có lớp trong năm học này');
    }

    // Tạo ghi danh mới
    const enrollment = new StudentEnrollment({
      student: studentId,
      class: classId,
      schoolYear: schoolYear,
      group: group,
      status: 'active'
    });

    await enrollment.save();
    
    return await enrollment.populate([
      { path: 'student', select: 'name studentId phoneNumber' },
      { path: 'class', select: 'name group schoolYear' }
    ]);
  }

  /**
   * Chuyển học sinh sang lớp khác
   * @param {string} studentId - ID của học sinh
   * @param {string} newClassId - ID của lớp mới
   * @param {string} schoolYear - Năm học
   * @param {string} reason - Lý do chuyển lớp
   * @returns {Promise<Object>} Thông tin ghi danh mới
   */
  static async transferStudent(studentId, newClassId, schoolYear, reason = '') {
    // Tìm ghi danh hiện tại
    const currentEnrollment = await StudentEnrollment.findOne({
      student: studentId,
      schoolYear: schoolYear,
      status: 'active'
    });

    if (!currentEnrollment) {
      throw new Error('Không tìm thấy ghi danh hiện tại của học sinh');
    }

    // Kiểm tra lớp mới có tồn tại không
    const newClass = await Class.findById(newClassId);
    if (!newClass) {
      throw new Error('Lớp mới không tồn tại');
    }

    // Cập nhật ghi danh cũ
    currentEnrollment.status = 'transferred';
    currentEnrollment.endDate = new Date();
    currentEnrollment.notes = reason;
    await currentEnrollment.save();

    // Tạo ghi danh mới
    const newEnrollment = new StudentEnrollment({
      student: studentId,
      class: newClassId,
      schoolYear: schoolYear,
      group: currentEnrollment.group, // Giữ nguyên group
      status: 'active'
    });

    await newEnrollment.save();
    
    return await newEnrollment.populate([
      { path: 'student', select: 'name studentId phoneNumber' },
      { path: 'class', select: 'name group schoolYear' }
    ]);
  }

  /**
   * Lấy danh sách học sinh của lớp theo năm học
   * @param {string} classId - ID của lớp
   * @param {string} schoolYear - Năm học
   * @param {string} status - Trạng thái (mặc định: 'active')
   * @returns {Promise<Array>} Danh sách học sinh
   */
  static async getClassStudents(classId, schoolYear, status = 'active') {
    return await StudentEnrollment.find({
      class: classId,
      schoolYear: schoolYear,
      status: status
    })
    .populate('student', 'name studentId phoneNumber avatar gender')
    .sort({ 'student.name': 1 });
  }

  /**
   * Lấy lớp hiện tại của học sinh
   * @param {string} studentId - ID của học sinh
   * @param {string} schoolYear - Năm học
   * @returns {Promise<Object|null>} Thông tin lớp hiện tại
   */
  static async getStudentCurrentClass(studentId, schoolYear) {
    return await StudentEnrollment.findOne({
      student: studentId,
      schoolYear: schoolYear,
      status: 'active'
    }).populate('class', 'name group schoolYear homeroomTeacher');
  }

  /**
   * Đếm số học sinh trong lớp
   * @param {string} classId - ID của lớp
   * @param {string} schoolYear - Năm học
   * @param {string} status - Trạng thái (mặc định: 'active')
   * @returns {Promise<number>} Số lượng học sinh
   */
  static async getStudentCount(classId, schoolYear, status = 'active') {
    return await StudentEnrollment.countDocuments({
      class: classId,
      schoolYear: schoolYear,
      status: status
    });
  }

  /**
   * Lấy lịch sử học tập của học sinh
   * @param {string} studentId - ID của học sinh
   * @returns {Promise<Array>} Lịch sử các lớp đã học
   */
  static async getStudentHistory(studentId) {
    return await StudentEnrollment.find({ student: studentId })
      .populate('class', 'name group schoolYear')
      .sort({ schoolYear: -1, enrollmentDate: -1 });
  }

  /**
   * Cập nhật trạng thái ghi danh (tốt nghiệp, thôi học, etc.)
   * @param {string} studentId - ID của học sinh
   * @param {string} schoolYear - Năm học
   * @param {string} newStatus - Trạng thái mới
   * @param {string} reason - Lý do thay đổi
   * @returns {Promise<Object>} Thông tin ghi danh đã cập nhật
   */
  static async updateEnrollmentStatus(studentId, schoolYear, newStatus, reason = '') {
    const enrollment = await StudentEnrollment.findOne({
      student: studentId,
      schoolYear: schoolYear,
      status: 'active'
    });

    if (!enrollment) {
      throw new Error('Không tìm thấy ghi danh hiện tại');
    }

    enrollment.status = newStatus;
    enrollment.endDate = new Date();
    enrollment.notes = reason;
    
    await enrollment.save();
    
    return await enrollment.populate([
      { path: 'student', select: 'name studentId phoneNumber' },
      { path: 'class', select: 'name group schoolYear' }
    ]);
  }

  /**
   * Lấy thống kê ghi danh theo lớp
   * @param {string} classId - ID của lớp
   * @param {string} schoolYear - Năm học
   * @returns {Promise<Object>} Thống kê chi tiết
   */
  static async getClassEnrollmentStats(classId, schoolYear) {
    const stats = await StudentEnrollment.aggregate([
      {
        $match: {
          class: classId,
          schoolYear: schoolYear
        }
      },
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    const result = {
      active: 0,
      transferred: 0,
      graduated: 0,
      dropped: 0,
      total: 0
    };

    stats.forEach(stat => {
      result[stat._id] = stat.count;
      result.total += stat.count;
    });

    return result;
  }
}

module.exports = StudentEnrollmentService;
