// controllers/attendanceConfigController.js
const AttendanceConfig = require('../models/AttendanceConfig');
const asyncHandler = require('../middlewares/async');
const MESSAGES = require('../constants/messages');

// @desc    Lấy cấu hình điểm danh hiện tại
// @route   GET /api/attendance-config
// @access  Public
exports.getAttendanceConfig = asyncHandler(async (req, res) => {
  // Lấy cấu hình đang hoạt động
  let config = await AttendanceConfig.findOne({ isActive: true });
  
  // Nếu không có cấu hình nào, tạo cấu hình mặc định
  if (!config) {
    config = await AttendanceConfig.create({
      isActive: true
    });
  }
  
  // Chuyển đổi cấu hình sang định dạng dễ sử dụng
  const formattedConfig = {
    sessionStartTime: {
      morning: {
        hour: config.morningStartHour,
        minute: config.morningStartMinute
      },
      afternoon: {
        hour: config.afternoonStartHour,
        minute: config.afternoonStartMinute
      }
    },
    sessionEndTime: {
      morning: {
        hour: config.morningEndHour,
        minute: config.morningEndMinute
      },
      afternoon: {
        hour: config.afternoonEndHour,
        minute: config.afternoonEndMinute
      }
    },
    lateThreshold: config.lateThreshold,
    maxAttendanceTime: config.maxAttendanceTime
  };
  
  res.json({
    success: true,
    data: formattedConfig
  });
});

// @desc    Cập nhật cấu hình điểm danh
// @route   PUT /api/attendance-config
// @access  Private (Admin only)
exports.updateAttendanceConfig = asyncHandler(async (req, res) => {
  // Chỉ admin mới được phép cập nhật cấu hình
  if (!req.user.role.includes('admin')) {
    return res.status(403).json({
      success: false,
      msg: MESSAGES.ERROR.FORBIDDEN
    });
  }

  const {
    morningStartHour,
    morningStartMinute,
    morningEndHour,
    morningEndMinute,
    afternoonStartHour,
    afternoonStartMinute,
    afternoonEndHour,
    afternoonEndMinute,
    lateThreshold,
    maxAttendanceTime
  } = req.body;
  
  // Lấy cấu hình đang hoạt động
  let config = await AttendanceConfig.findOne({ isActive: true });
  
  // Nếu không có cấu hình nào, tạo cấu hình mới
  if (!config) {
    config = new AttendanceConfig({
      isActive: true
    });
  }
  
  // Cập nhật các trường
  if (morningStartHour !== undefined) config.morningStartHour = morningStartHour;
  if (morningStartMinute !== undefined) config.morningStartMinute = morningStartMinute;
  if (morningEndHour !== undefined) config.morningEndHour = morningEndHour;
  if (morningEndMinute !== undefined) config.morningEndMinute = morningEndMinute;
  if (afternoonStartHour !== undefined) config.afternoonStartHour = afternoonStartHour;
  if (afternoonStartMinute !== undefined) config.afternoonStartMinute = afternoonStartMinute;
  if (afternoonEndHour !== undefined) config.afternoonEndHour = afternoonEndHour;
  if (afternoonEndMinute !== undefined) config.afternoonEndMinute = afternoonEndMinute;
  if (lateThreshold !== undefined) config.lateThreshold = lateThreshold;
  if (maxAttendanceTime !== undefined) config.maxAttendanceTime = maxAttendanceTime;
  
  // Cập nhật thông tin người chỉnh sửa
  config.updatedBy = req.user.id;
  
  // Lưu cấu hình
  await config.save();
  
  // Chuyển đổi cấu hình sang định dạng dễ sử dụng
  const formattedConfig = {
    sessionStartTime: {
      morning: {
        hour: config.morningStartHour,
        minute: config.morningStartMinute
      },
      afternoon: {
        hour: config.afternoonStartHour,
        minute: config.afternoonStartMinute
      }
    },
    sessionEndTime: {
      morning: {
        hour: config.morningEndHour,
        minute: config.morningEndMinute
      },
      afternoon: {
        hour: config.afternoonEndHour,
        minute: config.afternoonEndMinute
      }
    },
    lateThreshold: config.lateThreshold,
    maxAttendanceTime: config.maxAttendanceTime
  };
  
  res.json({
    success: true,
    data: formattedConfig,
    message: 'Cập nhật cấu hình điểm danh thành công'
  });
});
