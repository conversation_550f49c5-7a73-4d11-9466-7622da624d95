// models/Violation.js
const mongoose = require('mongoose');
const { 
  VIOLATION_TYPES, 
  VIOLATION_STATUS, 
  VIOLATION_POINTS,
  DEFAULT_CONDUCT_POINTS 
} = require('../constants/violationConstants');

const ViolationSchema = new mongoose.Schema({
  // Học sinh vi phạm
  student: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  // Lớp của học sinh
  class: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Class',
    required: true
  },
  
  // Năm học
  schoolYear: {
    type: String,
    required: true,
    trim: true // Ví dụ: "2024-2025"
  },
  
  // Loại vi phạm
  violationType: {
    type: String,
    required: true,
    enum: Object.values(VIOLATION_TYPES),
    trim: true
  },
  
  // Mô tả chi tiết vi phạm
  description: {
    type: String,
    required: true,
    trim: true
  },
  
  // Ngày vi phạm
  violationDate: {
    type: Date,
    required: true,
    default: Date.now
  },
  
  // Số điểm bị trừ
  pointsDeducted: {
    type: Number,
    required: true,
    min: 0,
    max: 50,
    default: function() {
      return VIOLATION_POINTS[this.violationType] || 1;
    }
  },
  
  // Người báo cáo vi phạm
  reportedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  // Trạng thái xử lý
  status: {
    type: String,
    enum: Object.values(VIOLATION_STATUS),
    default: VIOLATION_STATUS.PENDING,
    required: true
  },
  
  // Ghi chú thêm
  notes: {
    type: String,
    trim: true
  },
  
  // Thông tin xử lý
  processedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  processedAt: {
    type: Date
  },
  
  // Lý do khiếu nại (nếu có)
  appealReason: {
    type: String,
    trim: true
  },
  
  appealedAt: {
    type: Date
  },
  
  // Kết quả khiếu nại
  appealResult: {
    type: String,
    enum: ['approved', 'rejected', 'pending'],
    trim: true
  },
  
  // Người xử lý khiếu nại
  appealProcessedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  appealProcessedAt: {
    type: Date
  },
  
  // Thông tin thông báo
  notificationSent: {
    type: Boolean,
    default: false
  },
  
  notificationSentAt: {
    type: Date
  },
  
  // Metadata
  createdAt: {
    type: Date,
    default: Date.now
  },
  
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Index để tối ưu hiệu suất truy vấn
ViolationSchema.index({ student: 1, schoolYear: 1 });
ViolationSchema.index({ class: 1, schoolYear: 1 });
ViolationSchema.index({ violationDate: -1 });
ViolationSchema.index({ status: 1 });
ViolationSchema.index({ violationType: 1 });
ViolationSchema.index({ reportedBy: 1 });

// Compound index cho các truy vấn phức tạp
ViolationSchema.index({ student: 1, violationDate: -1 });
ViolationSchema.index({ class: 1, violationDate: -1 });
ViolationSchema.index({ schoolYear: 1, status: 1 });

// Middleware để cập nhật updatedAt
ViolationSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Virtual để lấy tên hiển thị của loại vi phạm
ViolationSchema.virtual('violationTypeLabel').get(function() {
  const { VIOLATION_TYPE_LABELS } = require('../constants/violationConstants');
  return VIOLATION_TYPE_LABELS[this.violationType] || this.violationType;
});

// Virtual để lấy tên hiển thị của trạng thái
ViolationSchema.virtual('statusLabel').get(function() {
  const { VIOLATION_STATUS_LABELS } = require('../constants/violationConstants');
  return VIOLATION_STATUS_LABELS[this.status] || this.status;
});

// Static method để lấy vi phạm của học sinh trong năm học
ViolationSchema.statics.getStudentViolations = function(studentId, schoolYear) {
  return this.find({
    student: studentId,
    schoolYear: schoolYear,
    status: { $ne: VIOLATION_STATUS.CANCELLED }
  })
  .populate('reportedBy', 'name')
  .populate('processedBy', 'name')
  .sort({ violationDate: -1 });
};

// Static method để lấy vi phạm của lớp
ViolationSchema.statics.getClassViolations = function(classId, schoolYear) {
  return this.find({
    class: classId,
    schoolYear: schoolYear,
    status: { $ne: VIOLATION_STATUS.CANCELLED }
  })
  .populate('student', 'name studentId')
  .populate('reportedBy', 'name')
  .sort({ violationDate: -1 });
};

// Static method để đếm số lần vi phạm của học sinh
ViolationSchema.statics.countStudentViolations = function(studentId, schoolYear) {
  return this.countDocuments({
    student: studentId,
    schoolYear: schoolYear,
    status: { $ne: VIOLATION_STATUS.CANCELLED }
  });
};

// Static method để tính tổng điểm bị trừ của học sinh
ViolationSchema.statics.getTotalPointsDeducted = function(studentId, schoolYear) {
  return this.aggregate([
    {
      $match: {
        student: mongoose.Types.ObjectId(studentId),
        schoolYear: schoolYear,
        status: { $ne: VIOLATION_STATUS.CANCELLED }
      }
    },
    {
      $group: {
        _id: null,
        totalPoints: { $sum: '$pointsDeducted' }
      }
    }
  ]);
};

// Instance method để xử lý vi phạm
ViolationSchema.methods.processViolation = function(processedBy) {
  this.status = VIOLATION_STATUS.PROCESSED;
  this.processedBy = processedBy;
  this.processedAt = new Date();
  return this.save();
};

// Instance method để khiếu nại vi phạm
ViolationSchema.methods.appealViolation = function(appealReason) {
  this.status = VIOLATION_STATUS.APPEALED;
  this.appealReason = appealReason;
  this.appealedAt = new Date();
  this.appealResult = 'pending';
  return this.save();
};

// Instance method để xử lý khiếu nại
ViolationSchema.methods.processAppeal = function(result, processedBy) {
  this.appealResult = result;
  this.appealProcessedBy = processedBy;
  this.appealProcessedAt = new Date();
  
  if (result === 'approved') {
    this.status = VIOLATION_STATUS.CANCELLED;
  } else {
    this.status = VIOLATION_STATUS.PROCESSED;
  }
  
  return this.save();
};

module.exports = mongoose.model('Violation', ViolationSchema);
