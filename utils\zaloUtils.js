const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

const refreshZaloToken = async () => {
  try {
    const params = new URLSearchParams();
    params.append('refresh_token', process.env.ZALO_REFRESH_TOKEN);
    params.append('app_id', process.env.ZALO_APP_ID);
    params.append('grant_type', 'refresh_token');

    const response = await axios.post('https://oauth.zaloapp.com/v4/oa/access_token', params, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'secret_key': process.env.ZALO_APP_SECRET
      }
    });

    console.log(`resonse: ${response.data}`);
    if (response.data && response.data.access_token) {
      // Update .env file
      const envPath = path.resolve(process.cwd(), '.env');
      let envContent = fs.readFileSync(envPath, 'utf8');

      // Update ZALO_ACCESS_TOKEN
      envContent = envContent.replace(
        /ZALO_ACCESS_TOKEN=.*/,
        `ZALO_ACCESS_TOKEN=${response.data.access_token}`
      );

      // Update ZALO_REFRESH_TOKEN
      envContent = envContent.replace(
        /ZALO_REFRESH_TOKEN=.*/,
        `ZALO_REFRESH_TOKEN=${response.data.refresh_token}`
      );

      fs.writeFileSync(envPath, envContent);

      // Update process.env
      process.env.ZALO_ACCESS_TOKEN = response.data.access_token;
      process.env.ZALO_REFRESH_TOKEN = response.data.refresh_token;

      return response.data.access_token;
    }

    throw new Error('Invalid response from token refresh');
  } catch (error) {
    console.error('Error refreshing Zalo token:', error);
    throw error;
  }
};

module.exports = {
  refreshZaloToken
}; 