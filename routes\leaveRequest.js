// routes/leaveRequest.js
const express = require('express');
const router = express.Router();
const {
  createLeaveRequest,
  getClassLeaveRequests,
  getStudentLeaveRequests,
  getUserLeaveRequests,
  getTeacherLeaveRequests,
  getMyStudentLeaveRequests,
  updateLeaveRequestStatus,
  deleteLeaveRequest
} = require('../controllers/leaveRequestController');
const { protect, authorize } = require('../middlewares/auth');

// @route   POST /api/leave-requests
// @desc    Tạo yêu cầu xin phép nghỉ học mới
// @access  Private (học sinh, giáo viên, admin)
router.post(
  '/',
  protect,
  createLeaveRequest
);

// @route   GET /api/leave-requests/teachers
// @desc    Lấy danh sách yêu cầu xin phép của tất cả giáo viên
// @access  Private (chỉ admin/hiệu trưởng)
router.get(
  '/teachers',
  protect,
  authorize('admin'),
  getTeacherLeaveRequests
);

// @route   GET /api/leave-requests/my-students
// @desc    Lấy danh sách yêu cầu xin phép của học sinh trong các lớp giáo viên làm chủ nhiệm
// @access  Private (chỉ giáo viên)
router.get(
  '/my-students',
  protect,
  authorize('teacher'),
  getMyStudentLeaveRequests
);

// @route   GET /api/leave-requests/class/:classId
// @desc    Lấy danh sách yêu cầu xin phép nghỉ học của một lớp
// @access  Private (giáo viên chủ nhiệm của lớp đó, admin)
router.get(
  '/class/:classId',
  protect,
  authorize('teacher', 'admin'),
  getClassLeaveRequests
);

// @route   GET /api/leave-requests/user/:userId
// @desc    Lấy danh sách yêu cầu xin phép nghỉ học của một người dùng (học sinh hoặc giáo viên)
// @access  Private (người dùng chỉ xem được của mình, giáo viên và admin xem được tất cả)
router.get(
  '/user/:userId',
  protect,
  getUserLeaveRequests
);

// @route   GET /api/leave-requests/student/:studentId (deprecated - sử dụng /user/:userId)
// @desc    Lấy danh sách yêu cầu xin phép nghỉ học của một học sinh
// @access  Private (học sinh chỉ xem được của mình, giáo viên xem được tất cả)
router.get(
  '/student/:studentId',
  protect,
  getStudentLeaveRequests
);

// @route   PUT /api/leave-requests/:id
// @desc    Phê duyệt hoặc từ chối yêu cầu xin phép nghỉ học
// @access  Private (giáo viên duyệt đơn học sinh lớp mình làm chủ nhiệm, admin duyệt đơn giáo viên)
router.put(
  '/:id',
  protect,
  updateLeaveRequestStatus
);

// @route   DELETE /api/leave-requests/:id
// @desc    Xóa yêu cầu xin phép nghỉ học
// @access  Private (người dùng chỉ xóa được của mình và chỉ khi đang ở trạng thái pending, giáo viên và admin xóa được tất cả)
router.delete(
  '/:id',
  protect,
  deleteLeaveRequest
);

module.exports = router;
