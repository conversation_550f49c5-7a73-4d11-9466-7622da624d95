/**
 * T<PERSON><PERSON> hợp các hằng số liên quan đến thông báo trong ứng dụng
 * Gi<PERSON><PERSON> thống nhất cách sử dụng và dễ dàng thay đổi sau này
 */

// Loại thông báo
const ANNOUNCEMENT_TYPES = {
  TEACHER_TO_STUDENT: 'teacher_to_student',
  PRINCIPAL_TO_TEACHER: 'principal_to_teacher',
  HEAD_TO_TEACHER: 'head_to_teacher',
  ADMIN_TO_ALL: 'admin_to_all',
  MUTUAL_COMMUNICATION: 'mutual_communication',
};

// Trạng thái thông báo
const ANNOUNCEMENT_STATUS = {
  DRAFT: 'draft',
  SCHEDULED: 'scheduled',
  SENT: 'sent'
};

// Tên hiển thị cho loại thông báo
const ANNOUNCEMENT_TYPE_LABELS = {
  [ANNOUNCEMENT_TYPES.TEACHER_TO_STUDENT]: '<PERSON><PERSON><PERSON><PERSON> viên gửi học sinh',
  [ANNOUNCEMENT_TYPES.PRINCIPAL_TO_TEACHER]: 'Hiệu trưởng gửi giáo viên',
  [ANNOUNCEMENT_TYPES.HEAD_TO_TEACHER]: 'Trưởng bộ môn gửi giáo viên',
  [ANNOUNCEMENT_TYPES.ADMIN_TO_ALL]: 'Quản trị viên gửi toàn trường',
  [ANNOUNCEMENT_TYPES.MUTUAL_COMMUNICATION]: 'Thông báo cho nhau'
};

// Tên hiển thị cho trạng thái thông báo
const ANNOUNCEMENT_STATUS_LABELS = {
  [ANNOUNCEMENT_STATUS.DRAFT]: 'Bản nháp',
  [ANNOUNCEMENT_STATUS.SCHEDULED]: 'Đã lên lịch',
  [ANNOUNCEMENT_STATUS.SENT]: 'Đã gửi'
};

// Các bộ môn phổ biến
const DEPARTMENTS = {
  MATH: 'Toán',
  PHYSICS: 'Vật lý',
  CHEMISTRY: 'Hóa học',
  BIOLOGY: 'Sinh học',
  LITERATURE: 'Ngữ văn',
  ENGLISH: 'Tiếng Anh',
  HISTORY: 'Lịch sử',
  GEOGRAPHY: 'Địa lý',
  CIVIC_EDUCATION: 'Giáo dục công dân',
  PHYSICAL_EDUCATION: 'Thể dục',
  TECHNOLOGY: 'Công nghệ',
  INFORMATICS: 'Tin học'
};

module.exports = {
  ANNOUNCEMENT_TYPES,
  ANNOUNCEMENT_STATUS,
  ANNOUNCEMENT_TYPE_LABELS,
  ANNOUNCEMENT_STATUS_LABELS,
  DEPARTMENTS
};
