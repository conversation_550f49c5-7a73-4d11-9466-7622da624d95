// Tính sessionNumber dựa trên vị trí trong array
const calculateSessionNumber = (sessions, targetSessionId) => {
  const index = sessions.findIndex(s => s._id.toString() === targetSessionId);
  return index !== -1 ? index + 1 : null;
};

// Sắp xếp sessions theo date và timeSlot
const sortSessions = (sessions) => {
  return sessions.sort((a, b) => {
    const dateA = new Date(a.date);
    const dateB = new Date(b.date);
    
    if (dateA.getTime() !== dateB.getTime()) {
      return dateA - dateB;
    }
    // <PERSON><PERSON><PERSON> cùng ngày, sáng trước chiều
    if (a.timeSlot === 'morning' && b.timeSlot === 'afternoon') return -1;
    if (a.timeSlot === 'afternoon' && b.timeSlot === 'morning') return 1;
    return 0;
  });
};

// <PERSON><PERSON><PERSON> tra thời hạn báo cáo (2 ngày trước)
const checkReportDeadline = (sessionDate) => {
  const currentDate = new Date();
  const diffTime = sessionDate - currentDate;
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays >= 2;
};

module.exports = {
  calculateSessionNumber,
  sortSessions,
  checkReportDeadline
}; 