const mongoose = require('mongoose');

const eventSchema = new mongoose.Schema({
  class: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Class',
    required: true,
  },
  title: {
    type: String,
    required: true,
    trim: true,
  },
  startTime: {
    type: Date,
    required: true,
  },
  endTime: {
    type: Date,
    required: false, // <PERSON>hông bắt buộc nếu là sự kiện như "Hạn chót"
  },
  description: {
    type: String,
    trim: true,
    default: '', // L<PERSON>u thông tin bổ sung như "Tiết 3" hoặc "Hạn chót"
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

module.exports = mongoose.model('Event', eventSchema);