/**
 * <PERSON><PERSON><PERSON> hà<PERSON> gọi Zalo API
 */
const axios = require('axios');
const config = require('../config/default');
const { ZALO_API, GEOAPIFY_API, API_ERROR_MESSAGES, DEFAULT_VALUES } = require('../constants/zaloConstants');

/**
 * Lấy thông tin người dùng từ Zalo bằng access token
 * @param {string} accessToken - Access token từ Zalo
 * @returns {Promise<Object>} - Thông tin người dùng từ Zalo
 */
const getUserInfo = async (accessToken) => {
  try {
    const userResponse = await axios.get(`${ZALO_API.BASE_URL}${ZALO_API.USER_INFO}`, {
      headers: {
        access_token: accessToken,
      },
      params: {
        fields: 'id,name,picture',
      },
    });

    return userResponse.data;
  } catch (error) {
    console.error(`${API_ERROR_MESSAGES.USER_INFO}:`, error.response?.data || error.message);
    throw error;
  }
};

/**
 * <PERSON><PERSON><PERSON> thông tin từ Zalo sử dụng token và access token
 * @param {string} token - Token từ Zalo
 * @param {string} accessToken - Access token từ Zalo
 * @param {string} [infoType='user'] - Loại thông tin cần lấy ('user', 'phone', 'location')
 * @returns {Promise<Object>} - Thông tin từ Zalo
 */
const getZaloInfo = async (token, accessToken, infoType = 'user') => {
  try {
    const zaloResponse = await axios.get(`${ZALO_API.BASE_URL}${ZALO_API.USER_INFO_WITH_TOKEN}`, {
      headers: {
        access_token: accessToken,
        code: token,
        secret_key: config.zaloSecretKey,
      },
    });

    return zaloResponse.data;
  } catch (error) {
    const errorMessages = {
      user: API_ERROR_MESSAGES.USER_INFO,
      phone: API_ERROR_MESSAGES.PHONE_NUMBER,
      location: API_ERROR_MESSAGES.LOCATION
    };

    const errorMessage = errorMessages[infoType] || errorMessages.user;
    console.error(`${errorMessage}:`, error.response?.data || error.message);
    throw error;
  }
};

/**
 * Lấy thông tin người dùng từ Zalo sử dụng token và access token
 * @param {string} token - Token từ Zalo
 * @param {string} accessToken - Access token từ Zalo
 * @returns {Promise<Object>} - Thông tin người dùng từ Zalo
 */
const getUserInfoWithToken = async (token, accessToken) => {
  return getZaloInfo(token, accessToken, 'user');
};

/**
 * Lấy số điện thoại người dùng từ Zalo
 * @param {string} token - Token từ Zalo
 * @param {string} accessToken - Access token từ Zalo
 * @returns {Promise<Object>} - Thông tin số điện thoại từ Zalo
 */
const getPhoneNumber = async (token, accessToken) => {
  return getZaloInfo(token, accessToken, 'phone');
};

/**
 * Lấy thông tin vị trí người dùng từ Zalo
 * @param {string} token - Token từ Zalo
 * @param {string} accessToken - Access token từ Zalo
 * @returns {Promise<Object>} - Thông tin vị trí từ Zalo
 */
const getLocation = async (token, accessToken) => {
  return getZaloInfo(token, accessToken, 'location');
};

/**
 * Lấy địa chỉ từ tọa độ sử dụng Geoapify API
 * @param {number} latitude - Vĩ độ
 * @param {number} longitude - Kinh độ
 * @returns {Promise<string>} - Địa chỉ được định dạng
 */
const getAddressFromCoordinates = async (latitude, longitude) => {
  try {
    const response = await axios.get(`${GEOAPIFY_API.BASE_URL}${GEOAPIFY_API.REVERSE_GEOCODE}`, {
      params: {
        lat: latitude,
        lon: longitude,
        apiKey: config.geoapifyApiKey
      }
    });

    if (response.data && response.data.features && response.data.features.length > 0) {
      return response.data.features[0].properties.formatted;
    }

    return DEFAULT_VALUES.UNKNOWN_ADDRESS;
  } catch (error) {
    console.error(`${API_ERROR_MESSAGES.ADDRESS}:`, error.response?.data || error.message);
    return DEFAULT_VALUES.UNKNOWN_ADDRESS;
  }
};

/**
 * Lấy thông tin vị trí đầy đủ từ Zalo và chuyển đổi thành địa chỉ
 * @param {string} token - Token từ Zalo
 * @param {string} accessToken - Access token từ Zalo
 * @returns {Promise<Object>} - Thông tin vị trí và địa chỉ
 */
const getLocationWithAddress = async (token, accessToken) => {
  try {
    // Lấy thông tin vị trí từ Zalo
    const locationInfo = await getZaloInfo(token, accessToken, 'location');

    if (locationInfo && locationInfo.data) {
      const { latitude, longitude } = locationInfo.data;

      // Tạo đối tượng chứa thông tin tọa độ
      const coordinates = {
        latitude: parseFloat(latitude),
        longitude: parseFloat(longitude)
      };

      // Lấy địa chỉ từ tọa độ
      const address = await getAddressFromCoordinates(coordinates.latitude, coordinates.longitude);

      return {
        success: true,
        coordinates,
        location: address,
        rawData: locationInfo
      };
    }

    return {
      success: false,
      message: API_ERROR_MESSAGES.LOCATION,
      rawData: locationInfo
    };
  } catch (error) {
    console.error(`${API_ERROR_MESSAGES.LOCATION}:`, error.message);
    return {
      success: false,
      message: API_ERROR_MESSAGES.LOCATION,
      error: error.message
    };
  }
};

/**
 * Đổi code lấy access token từ Zalo
 * @param {string} code - Code từ Zalo
 * @param {string} codeVerifier - Code verifier đã lưu trước đó
 * @returns {Promise<Object>} - Thông tin token từ Zalo
 */
const exchangeCodeForToken = async (code, codeVerifier) => {
  try {
    const tokenResponse = await axios.post(
      ZALO_API.ACCESS_TOKEN,
      new URLSearchParams({
        app_id: config.zaloAppId,
        code,
        code_verifier: codeVerifier,
        grant_type: 'authorization_code',
      }),
      {
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      }
    );

    return tokenResponse.data;
  } catch (error) {
    console.error(`${API_ERROR_MESSAGES.ACCESS_TOKEN}:`, error.response?.data || error.message);
    throw error;
  }
};

/**
 * Chuẩn hóa số điện thoại và tạo các biến thể để tìm kiếm
 * @param {string} phoneNumber - Số điện thoại cần chuẩn hóa
 * @returns {Object} - Các biến thể của số điện thoại và số đã chuẩn hóa
 */
const normalizePhoneNumber = (phoneNumber) => {
  let phoneVariants = [];

  // Xử lý các trường hợp khác nhau của số điện thoại
  if (phoneNumber.startsWith('+84')) {
    // Định dạng +84...
    phoneVariants.push(phoneNumber); // +84916847863
    phoneVariants.push(`0${phoneNumber.substring(3)}`); // 0916847863
  } else if (phoneNumber.startsWith('84')) {
    // Định dạng 84...
    phoneVariants.push(`+${phoneNumber}`); // +84916847863
    phoneVariants.push(`0${phoneNumber.substring(2)}`); // 0916847863
  } else if (phoneNumber.startsWith('0')) {
    // Định dạng 0...
    phoneVariants.push(`+84${phoneNumber.substring(1)}`); // +84916847863
    phoneVariants.push(phoneNumber); // 0916847863
  } else {
    // Trường hợp khác
    phoneVariants.push(`+84${phoneNumber}`); // +84916847863
    phoneVariants.push(`0${phoneNumber}`); // 0916847863
  }

  // Lấy định dạng quốc tế làm định dạng chuẩn hóa
  const normalizedPhoneNumber = phoneVariants[0];

  return {
    phoneVariants,
    normalizedPhoneNumber
  };
};

module.exports = {
  getUserInfo,
  getZaloInfo,
  getUserInfoWithToken,
  getPhoneNumber,
  getLocation,
  getAddressFromCoordinates,
  getLocationWithAddress,
  exchangeCodeForToken,
  normalizePhoneNumber
};
