const cron = require('node-cron');
const ExamSupervision = require('../models/ExamSupervision');
const AttendanceConfig = require('../models/AttendanceConfig');

// Hàm xử lý kiểm tra và cập nhật status sessions đã hoàn thành
const processExamSupervisionStatus = async () => {
  try {
    // Lấy cấu hình thời gian làm việc
    const config = await AttendanceConfig.findOne({ isActive: true });
    if (!config) {
      console.log('Không tìm thấy cấu hình thời gian làm việc active');
      return;
    }

    // Tìm tất cả phân công coi thi có sessions chưa hoàn thành
    const supervisions = await ExamSupervision.find({
      'sessions.status': { $in: ['DA_PHAN_CONG', 'YEU_CAU_DOI_BUOI'] }
    });

    if (supervisions.length === 0) {
      return;
    }

    console.log(`🔍 Đang kiểm tra ${supervisions.length} phân công coi thi...`);

    const now = new Date();
    let updatedCount = 0;

    for (const supervision of supervisions) {
      let hasUpdates = false;

      for (const session of supervision.sessions) {
        // Chỉ xử lý các session chưa hoàn thành
        if (session.status === 'DA_HOAN_THANH') {
          continue;
        }

        // Tính thời gian kết thúc của session
        const sessionDate = new Date(session.date);
        let sessionEndTime;

        if (session.timeSlot === 'morning') {
          sessionEndTime = new Date(sessionDate);
          sessionEndTime.setHours(config.morningEndHour, config.morningEndMinute, 0, 0);
        } else if (session.timeSlot === 'afternoon') {
          sessionEndTime = new Date(sessionDate);
          sessionEndTime.setHours(config.afternoonEndHour, config.afternoonEndMinute, 0, 0);
        } else {
          continue; // Bỏ qua nếu timeSlot không hợp lệ
        }

        // Nếu đã qua thời gian kết thúc session
        if (now > sessionEndTime) {
          console.log(`✅ Cập nhật session ${session._id} thành DA_HOAN_THANH (${session.timeSlot} ${sessionDate.toLocaleDateString()})`);
          session.status = 'DA_HOAN_THANH';
          hasUpdates = true;
          updatedCount++;
        }
      }

      // Lưu nếu có cập nhật
      if (hasUpdates) {
        await supervision.save();
      }
    }

    if (updatedCount > 0) {
      console.log(`🎯 Đã cập nhật ${updatedCount} session thành DA_HOAN_THANH`);
    }

  } catch (err) {
    console.error('❌ Lỗi trong processExamSupervisionStatus:', err);
  }
};

// Định nghĩa cron job chạy mỗi 30 phút
const initExamSupervisionStatusChecker = () => {
  console.log('🕐 Exam Supervision Status Checker started - checking every 30 minutes');
  
  // Chạy mỗi 30 phút
  cron.schedule('*/30 * * * *', async () => {
    try {
      await processExamSupervisionStatus();
    } catch (err) {
      console.error('❌ Error in Exam Supervision Status Checker:', err);
    }
  });
  
  // Chạy ngay lần đầu sau 1 phút để test
  setTimeout(() => {
    processExamSupervisionStatus();
  }, 60000);
};

module.exports = initExamSupervisionStatusChecker; 