const mongoose = require('mongoose');

const announcementReplySchema = new mongoose.Schema({
  announcement: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Announcement',
    required: true
  },
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  content: {
    type: String,
    required: true,
    trim: true
  },
  parentReply: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'AnnouncementReply',
    default: null
  },
  replyTo: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  isEdited: {
    type: Boolean,
    default: false
  },
  editedAt: {
    type: Date
  },
  isDeleted: {
    type: Boolean,
    default: false
  },
  deletedAt: {
    type: Date
  },
  replyCount: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
announcementReplySchema.index({ announcement: 1, createdAt: -1 });
announcementReplySchema.index({ user: 1 });
announcementReplySchema.index({ parentReply: 1 });
announcementReplySchema.index({ replyTo: 1 });

// Virtual for nested replies
announcementReplySchema.virtual('replies', {
  ref: 'AnnouncementReply',
  localField: '_id',
  foreignField: 'parentReply'
});

// Virtual for time ago
announcementReplySchema.virtual('timeAgo').get(function() {
  const now = new Date();
  const diffInSeconds = Math.floor((now - this.createdAt) / 1000);

  if (diffInSeconds < 60) return 'Vừa xong';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} phút trước`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} giờ trước`;
  if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} ngày trước`;
  if (diffInSeconds < 31536000) return `${Math.floor(diffInSeconds / 2592000)} tháng trước`;
  return `${Math.floor(diffInSeconds / 31536000)} năm trước`;
});

// Soft delete method
announcementReplySchema.methods.softDelete = async function() {
  this.isDeleted = true;
  this.deletedAt = new Date();
  await this.save();
};

// Edit method
announcementReplySchema.methods.edit = async function(newContent) {
  this.content = newContent;
  this.isEdited = true;
  this.editedAt = new Date();
  await this.save();
};

// Update reply count
announcementReplySchema.methods.updateReplyCount = async function() {
  const count = await this.model('AnnouncementReply').countDocuments({
    parentReply: this._id,
    isDeleted: false
  });
  this.replyCount = count;
  await this.save();
};

// Static method to update reply counts for multiple replies
announcementReplySchema.statics.updateReplyCounts = async function(replyIds) {
  const replies = await this.find({ _id: { $in: replyIds } });
  await Promise.all(replies.map(reply => reply.updateReplyCount()));
};

// Pre-save middleware to update parent's reply count
announcementReplySchema.pre('save', async function(next) {
  if (this.isNew && this.parentReply) {
    const parent = await this.model('AnnouncementReply').findById(this.parentReply);
    if (parent) {
      await parent.updateReplyCount();
    }
  }
  next();
});

const AnnouncementReply = mongoose.model('AnnouncementReply', announcementReplySchema);

module.exports = AnnouncementReply; 