/**
 * Các URL API và endpoint sử dụng trong ứng dụng
 */

// Zalo API
const ZALO_API = {
  BASE_URL: 'https://graph.zalo.me',
  USER_INFO: '/v2.0/me',
  USER_INFO_WITH_TOKEN: '/v2.0/me/info',
  ACCESS_TOKEN: 'https://oauth.zaloapp.com/v4/access_token'
};

// Geoapify API
const GEOAPIFY_API = {
  BASE_URL: 'https://api.geoapify.com',
  REVERSE_GEOCODE: '/v1/geocode/reverse'
};

// Thông báo lỗi API
const API_ERROR_MESSAGES = {
  USER_INFO: 'Lỗi khi lấy thông tin người dùng từ Zalo',
  PHONE_NUMBER: 'Lỗi khi lấy số điện thoại từ Zalo',
  LOCATION: 'Lỗi khi lấy thông tin vị trí từ Zalo',
  ADDRESS: 'Lỗi khi lấy địa chỉ từ tọa độ',
  ACCESS_TOKEN: 'Lỗi khi đổi code lấy access token từ Zalo'
};

// Giá trị mặc định
const DEFAULT_VALUES = {
  UNKNOWN_ADDRESS: 'Không xác định được địa chỉ'
};

module.exports = {
  ZALO_API,
  GEOAPIFY_API,
  API_ERROR_MESSAGES,
  DEFAULT_VALUES
};
