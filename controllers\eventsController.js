const asyncHandler = require('express-async-handler');
const Event = require('../models/Event');
const MESSAGES = require('../constants/messages');

// @desc    Tạo sự kiện mới
// @route   POST /api/events
// @access  Private (yêu cầu x-auth-token, role: teacher, admin)
exports.createEvent = asyncHandler(async (req, res) => {
    const { classId, title, startTime, endTime, description } = req.body;

    // Kiểm tra các trường bắt buộc
    if (!classId || !title || !startTime) {
        return res.status(400).json({ msg: MESSAGES.ERROR.VALIDATION_FAILED });
    }

    // Kiểm tra user
    if (!req.user || !req.user._id) {
        return res.status(401).json({ msg: MESSAGES.ERROR.UNAUTHORIZED });
    }

    // Validate startTime và endTime (nếu có)
    const start = new Date(startTime);
    if (isNaN(start.getTime())) {
        return res.status(400).json({ msg: MESSAGES.ERROR.VALIDATION_FAILED });
    }

    let end;
    if (endTime) {
        end = new Date(endTime);
        if (isNaN(end.getTime())) {
            return res.status(400).json({ msg: MESSAGES.ERROR.VALIDATION_FAILED });
        }
        if (end <= start) {
            return res.status(400).json({ msg: MESSAGES.ERROR.VALIDATION_FAILED });
        }
    }

    const event = new Event({
        class: classId,
        title,
        startTime,
        endTime: endTime || null,
        description: description || '',
        createdBy: req.user._id,
    });

    await event.save();
    
    // Populate thông tin người tạo trước khi trả về
    await event.populate('createdBy', 'name');
    
    res.status(201).json(event);
});

// @desc    Lấy danh sách sự kiện (phân trang)
// @route   GET /api/events?classId=&page=&limit=&createdByMe=&startFrom=&startTo=
// @access  Private (yêu cầu x-auth-token)
exports.getEvents = asyncHandler(async (req, res) => {
    const { classId, page = 1, limit = 10, createdByMe, startFrom, startTo } = req.query;

    if (!classId) {
        return res.status(400).json({ msg: MESSAGES.ERROR.VALIDATION_FAILED });
    }

    // Xây dựng query
    const query = { class: classId };
    
    // Filter theo người tạo nếu có
    if (createdByMe === 'true') {
        query.createdBy = req.user._id;
    }
    
    // Filter theo khoảng thời gian startTime
    if (startFrom || startTo) {
        query.startTime = {};
        
        if (startFrom) {
            const fromDate = new Date(startFrom);
            fromDate.setHours(0, 0, 0, 0);
            query.startTime.$gte = fromDate;
        }
        
        if (startTo) {
            const toDate = new Date(startTo);
            toDate.setHours(23, 59, 59, 999);
            query.startTime.$lte = toDate;
        }
    }

    const events = await Event.find(query)
        .populate('class', 'name')
        .populate('createdBy', 'name')
        .sort({ startTime: 1 })
        .skip((page - 1) * limit)
        .limit(parseInt(limit));

    const totalEvents = await Event.countDocuments(query);
    const totalPages = Math.ceil(totalEvents / limit);

    res.json({
        events,
        currentPage: parseInt(page),
        totalPages,
        totalEvents,
    });
});

// @desc    Lấy danh sách sự kiện do người dùng tạo
// @route   GET /api/events/my-events?page=&limit=&startFrom=&startTo=
// @access  Private (yêu cầu x-auth-token, role: teacher, admin)
exports.getMyEvents = asyncHandler(async (req, res) => {
    const { page = 1, limit = 10, startFrom, startTo } = req.query;

    // Xây dựng query
    const query = { createdBy: req.user._id };
    
    // Filter theo khoảng thời gian startTime
    if (startFrom || startTo) {
        query.startTime = {};
        
        if (startFrom) {
            const fromDate = new Date(startFrom);
            fromDate.setHours(0, 0, 0, 0);
            query.startTime.$gte = fromDate;
        }
        
        if (startTo) {
            const toDate = new Date(startTo);
            toDate.setHours(23, 59, 59, 999);
            query.startTime.$lte = toDate;
        }
    }

    // Tính toán pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);

    const events = await Event.find(query)
        .populate('class', 'name grade')
        .sort({ startTime: -1 }) // Sắp xếp theo thời gian mới nhất
        .skip(skip)
        .limit(parseInt(limit));

    const totalEvents = await Event.countDocuments(query);
    const totalPages = Math.ceil(totalEvents / parseInt(limit));

    // Thống kê sự kiện
    const now = new Date();
    const upcomingEvents = await Event.countDocuments({
        createdBy: req.user._id,
        startTime: { $gte: now }
    });
    
    const pastEvents = await Event.countDocuments({
        createdBy: req.user._id,
        startTime: { $lt: now }
    });

    res.json({
        success: true,
        data: events,
        pagination: {
            currentPage: parseInt(page),
            totalPages,
            totalItems: totalEvents,
            itemsPerPage: parseInt(limit)
        },
        statistics: {
            totalEvents,
            upcomingEvents,
            pastEvents
        }
    });
});

// @desc    Lấy sự kiện sắp tới
// @route   GET /api/events/upcoming?classId=&limit=
// @access  Private (yêu cầu x-auth-token)
// @desc    Lấy sự kiện sắp tới với phân trang
// @route   GET /api/events/upcoming?classId=&page=&limit=
// @access  Private (yêu cầu x-auth-token)
exports.getUpcomingEvents = asyncHandler(async (req, res) => {
    const { classId, page = 1, limit = 3 } = req.query;
    const pageNumber = parseInt(page);
    const limitNumber = parseInt(limit);

    if (!classId) {
        return res.status(400).json({ msg: MESSAGES.ERROR.VALIDATION_FAILED });
    }

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    try {
        // Đếm tổng số sự kiện
        const totalEvents = await Event.countDocuments({
            class: classId,
            startTime: { $gte: today },
        });

        // Tính toán số trang
        const totalPages = Math.ceil(totalEvents / limitNumber);

        // Lấy sự kiện với skip và limit
        const events = await Event.find({
            class: classId,
            startTime: { $gte: today }, // Chỉ lấy sự kiện từ hôm nay trở đi
        })
            .populate('class', 'name')
            .sort({ startTime: 1 })
            .skip((pageNumber - 1) * limitNumber)
            .limit(limitNumber);

        // Trả về định dạng phân trang
        res.json({
            events,
            currentPage: pageNumber,
            totalPages,
            totalEvents,
        });
    } catch (error) {
        console.error('Error fetching upcoming events:', error);
        res.status(500).json({ msg: MESSAGES.ERROR.GENERAL });
    }
});