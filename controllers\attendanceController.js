// controllers/attendanceController.js
const Attendance = require('../models/Attendance');
const User = require('../models/User');
const Class = require('../models/Class');
const AttendanceConfig = require('../models/AttendanceConfig');
const LeaveRequest = require('../models/LeaveRequest');
const asyncHandler = require('../middlewares/async');
const MESSAGES = require('../constants/messages');
const ROLES = require('../constants/roleConstants');
const {
  ATTENDANCE_STATUS,
  SESSION,
  DISPLAY_SESSION,
  DEFAULT_NOTES
} = require('../constants/attendanceConstants');
const { getLocationWithAddress } = require('../services/zaloService');

// @desc    Tạo điểm danh mới
// @route   POST /api/attendance
// @access  Private (tất cả người dùng đã đăng nhập)
exports.createAttendance = asyncHandler(async (req, res) => {
  const {
    studentId,
    classId,
    date,
    session,
    status,
    location,
    coordinates,
    notes,
    accessToken,
    token
  } = req.body;

  let targetStudentId;
  let creatorId = req.user.id;

  // Xác định học sinh được điểm danh
  if (req.user.role.includes('student')) {
    // Trường hợp 1: Học sinh tự điểm danh
    targetStudentId = req.user.id;

    // Kiểm tra dữ liệu đầu vào
    if (!classId || !session) {
      return res.status(400).json({
        success: false,
        msg: MESSAGES.ERROR.VALIDATION_FAILED
      });
    }
  } else if (req.user.role.includes('teacher') || req.user.role.includes('admin')) {
    // Trường hợp 2: Giáo viên điểm danh cho học sinh (trường hợp ngoại lệ)
    if (!studentId) {
      return res.status(400).json({
        success: false,
        msg: MESSAGES.ERROR.VALIDATION_FAILED
      });
    }
    targetStudentId = studentId;
  } else {
    return res.status(403).json({
      success: false,
      msg: MESSAGES.ERROR.FORBIDDEN
    });
  }

  // Kiểm tra buổi học hợp lệ
  if (session && !Object.values(SESSION).includes(session)) {
    return res.status(400).json({
      success: false,
      msg: `Buổi học không hợp lệ. Chỉ chấp nhận "${SESSION.MORNING}" hoặc "${SESSION.AFTERNOON}"`
    });
  }

  // Kiểm tra học sinh có tồn tại không
  const student = await User.findById(targetStudentId);
  if (!student || !student.role.includes('student')) {
    return res.status(404).json({
      success: false,
      msg: MESSAGES.STUDENT.NOT_FOUND
    });
  }

  // Kiểm tra lớp có tồn tại không
  const classObj = await Class.findById(classId);
  if (!classObj) {
    return res.status(404).json({
      success: false,
      msg: MESSAGES.CLASS.NOT_FOUND
    });
  }

  // Nếu là học sinh tự điểm danh, kiểm tra học sinh có thuộc lớp không
  if (req.user.role.includes('student')) {
    if (student.class && student.class.toString() !== classId) {
      return res.status(403).json({
        success: false,
        msg: MESSAGES.ERROR.FORBIDDEN
      });
    }
  }

  // Xác định ngày điểm danh
  const attendanceDate = date ? new Date(date) : new Date();
  const startOfDay = new Date(attendanceDate);
  startOfDay.setHours(0, 0, 0, 0);

  const endOfDay = new Date(attendanceDate);
  endOfDay.setHours(23, 59, 59, 999);

  // Xác định buổi học (sáng/chiều) nếu không được cung cấp
  const currentSession = session || (() => {
    const currentHour = new Date().getHours();
    return currentHour < 12 ? SESSION.MORNING : SESSION.AFTERNOON;
  })();

  // Kiểm tra thời gian điểm danh để xác định trạng thái (đúng giờ hay đi muộn)
  const determineAttendanceStatus = async () => {
    // Nếu giáo viên đã chỉ định trạng thái, sử dụng trạng thái đó
    const validStatuses = [
      ATTENDANCE_STATUS.PRESENT,
      ATTENDANCE_STATUS.ABSENT,
      ATTENDANCE_STATUS.LATE,
      ATTENDANCE_STATUS.REJECTED
    ];

    if (status && validStatuses.includes(status)) {
      return status;
    }

    // Lấy cấu hình điểm danh từ database
    let config = await AttendanceConfig.findOne({ isActive: true });

    // Nếu không có cấu hình nào, sử dụng cấu hình mặc định
    if (!config) {
      config = new AttendanceConfig();
    }

    const now = new Date();
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();

    // Lấy thời gian bắt đầu buổi học từ cấu hình
    let sessionStartHour, sessionStartMinute;
    if (currentSession === SESSION.MORNING) {
      sessionStartHour = config.morningStartHour;
      sessionStartMinute = config.morningStartMinute;
    } else {
      sessionStartHour = config.afternoonStartHour;
      sessionStartMinute = config.afternoonStartMinute;
    }

    // Tính số phút trễ
    const sessionStartMinutes = sessionStartHour * 60 + sessionStartMinute;
    const currentTimeMinutes = currentHour * 60 + currentMinute;
    const minutesLate = currentTimeMinutes - sessionStartMinutes;

    // Kiểm tra xem có đi muộn không
    if (minutesLate > config.lateThreshold) {
      return ATTENDANCE_STATUS.LATE;
    }

    // Kiểm tra xem có quá thời gian tối đa cho phép điểm danh không
    if (minutesLate > config.maxAttendanceTime) {
      return ATTENDANCE_STATUS.ABSENT;
    }

    return ATTENDANCE_STATUS.PRESENT;
  };

  // Xác định trạng thái điểm danh
  const attendanceStatus = req.user.role.includes(ROLES.STUDENT) ? await determineAttendanceStatus() : (status || ATTENDANCE_STATUS.PRESENT);

  // Lấy thông tin vị trí từ Zalo nếu là học sinh tự điểm danh và không có thông tin vị trí
  let locationInfo = {
    location: location || DEFAULT_NOTES.NO_LOCATION,
    coordinates: coordinates || null
  };
  if (req.user.role.includes(ROLES.STUDENT) && (!req.body.location || !req.body.coordinates)) {
    try {
      // Sử dụng token và accessToken từ request body
      if (token && accessToken) {
        // Gọi hàm getLocationWithAddress để lấy thông tin vị trí và địa chỉ
        const locationData = await getLocationWithAddress(token, accessToken);

        if (locationData.success) {
          locationInfo = {
            location: locationData.location || DEFAULT_NOTES.NO_LOCATION,
            coordinates: locationData.coordinates || null
          };
        }
      }
    } catch (error) {
      console.error('Lỗi khi lấy thông tin vị trí từ Zalo:', error.message);
      // Không trả về lỗi, tiếp tục xử lý với thông tin vị trí hiện có
    }
  }

  // Kiểm tra xem học sinh đã điểm danh cho buổi học này trong ngày chưa
  const existingAttendance = await Attendance.findOne({
    student: targetStudentId,
    class: classId,
    date: { $gte: startOfDay, $lte: endOfDay },
    session: currentSession
  });

  // Kiểm tra xem học sinh có yêu cầu xin phép nghỉ học được phê duyệt không
  const approvedLeaveRequest = await LeaveRequest.findOne({
    student: targetStudentId,
    class: classId,
    status: 'approved',
    $and: [
      { startDate: { $lte: attendanceDate } },
      { endDate: { $gte: attendanceDate } }
    ],
    sessions: { $in: [currentSession, 'all-day'] }
  });

  if (existingAttendance) {
    // Nếu là học sinh tự điểm danh và đã điểm danh rồi, thông báo
    if (req.user.role.includes('student')) {
      return res.status(400).json({
        success: false,
        msg: MESSAGES.ERROR.VALIDATION_FAILED
      });
    }

    // Nếu là giáo viên, cho phép cập nhật điểm danh
    existingAttendance.status = status || (req.user.role.includes(ROLES.STUDENT) ? attendanceStatus : existingAttendance.status);
    existingAttendance.location = locationInfo.location || existingAttendance.location;
    existingAttendance.coordinates = locationInfo.coordinates || existingAttendance.coordinates;
    existingAttendance.notes = notes || existingAttendance.notes;
    existingAttendance.updatedAt = Date.now();
    existingAttendance.updatedBy = req.user.id;

    await existingAttendance.save();

    return res.status(200).json({
      success: true,
      data: existingAttendance,
      message: MESSAGES.ATTENDANCE.UPDATED
    });
  }

  // Nếu học sinh có yêu cầu xin phép nghỉ học được phê duyệt
  if (approvedLeaveRequest) {
    // Tạo điểm danh với trạng thái "excused" (được phép nghỉ)
    const attendance = new Attendance({
      student: targetStudentId,
      class: classId,
      date: attendanceDate,
      session: currentSession,
      status: ATTENDANCE_STATUS.EXCUSED,
      location: locationInfo.location || DEFAULT_NOTES.NO_LOCATION,
      coordinates: locationInfo.coordinates,
      notes: notes || DEFAULT_NOTES[ATTENDANCE_STATUS.EXCUSED],
      leaveRequest: approvedLeaveRequest._id,
      createdBy: creatorId
    });

    await attendance.save();

    return res.status(201).json({
      success: true,
      data: attendance,
      message: 'Học sinh có đơn xin phép nghỉ học được phê duyệt'
    });
  }

  // Tạo điểm danh mới
  const attendance = new Attendance({
    student: targetStudentId,
    class: classId,
    date: attendanceDate,
    session: currentSession,
    status: attendanceStatus,
    location: locationInfo.location || DEFAULT_NOTES.NO_LOCATION,
    coordinates: locationInfo.coordinates,
    notes,
    createdBy: creatorId
  });

  await attendance.save();

  res.status(201).json({
    success: true,
    data: attendance
  });
});

// @desc    Lấy danh sách điểm danh của một lớp
// @route   GET /api/attendance/class/:classId
// @access  Private (giáo viên, admin)
exports.getClassAttendance = asyncHandler(async (req, res) => {
  const { classId } = req.params;
  const { date, session } = req.query;

  const query = { class: classId };

  // Lọc theo ngày nếu có
  if (date) {
    const startDate = new Date(date);
    startDate.setHours(0, 0, 0, 0);

    const endDate = new Date(date);
    endDate.setHours(23, 59, 59, 999);

    query.date = { $gte: startDate, $lte: endDate };
  }

  // Lọc theo buổi học (sáng/chiều) nếu có
  if (session && Object.values(SESSION).includes(session)) {
    query.session = session;
  }

  const attendances = await Attendance.find(query)
    .populate('student', 'name studentId')
    .populate('createdBy', 'name')
    .populate('updatedBy', 'name')
    .populate('rejectedBy', 'name')
    .sort({ date: -1, session: 1 });

  res.json({
    success: true,
    count: attendances.length,
    data: attendances
  });
});

// @desc    Lấy lịch sử điểm danh của một học sinh
// @route   GET /api/attendance/student/:studentId
// @access  Private (học sinh chỉ xem được của mình, giáo viên xem được tất cả)
exports.getStudentAttendance = asyncHandler(async (req, res) => {
  const { studentId } = req.params;
  const { date, session, startDate, endDate } = req.query;

  // Kiểm tra quyền truy cập
  if (req.user.role.includes('student') && req.user.id !== studentId) {
    return res.status(403).json({
      success: false,
      msg: 'Không có quyền truy cập dữ liệu của học sinh khác'
    });
  }

  // Xây dựng query
  const query = { student: studentId };

  // Lọc theo ngày nếu có
  if (startDate || endDate) {
    query.date = {};

    if (startDate) {
      const start = new Date(startDate);
      start.setHours(0, 0, 0, 0);
      query.date.$gte = start;
    }

    if (endDate) {
      const end = new Date(endDate);
      end.setHours(23, 59, 59, 999);
      query.date.$lte = end;
    }
  } else if (date) {
    // Nếu chỉ có date, lọc theo ngày cụ thể
    const startDate = new Date(date);
    startDate.setHours(0, 0, 0, 0);

    const endDate = new Date(date);
    endDate.setHours(23, 59, 59, 999);

    query.date = { $gte: startDate, $lte: endDate };
  }

  // Lọc theo buổi học (sáng/chiều) nếu có
  if (session && Object.values(SESSION).includes(session)) {
    query.session = session;
  }

  const attendances = await Attendance.find(query)
    .populate('class', 'name')
    .populate('createdBy', 'name')
    .populate('updatedBy', 'name')
    .populate('rejectedBy', 'name')
    .sort({ date: -1, session: 1 });

  res.json({
    success: true,
    count: attendances.length,
    data: attendances
  });
});

// @desc    Cập nhật thông tin điểm danh
// @route   PUT /api/attendance/:id
// @access  Private (giáo viên, admin)
exports.updateAttendance = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { status, notes } = req.body;

  let attendance = await Attendance.findById(id);

  if (!attendance) {
    return res.status(404).json({
      success: false,
      msg: MESSAGES.ATTENDANCE.NOT_FOUND
    });
  }

  // Kiểm tra quyền truy cập
  if (!req.user.role.includes(ROLES.TEACHER) && !req.user.role.includes(ROLES.ADMIN)) {
    return res.status(403).json({
      success: false,
      msg: MESSAGES.ERROR.FORBIDDEN
    });
  }

  // Cập nhật thông tin
  attendance = await Attendance.findByIdAndUpdate(
    id,
    {
      status,
      notes,
      updatedAt: Date.now(),
      updatedBy: req.user.id
    },
    { new: true, runValidators: true }
  );

  res.json({
    success: true,
    data: attendance
  });
});

// @desc    Từ chối điểm danh (giáo viên từ chối khi học sinh bỏ về giữa chừng)
// @route   PUT /api/attendance/:id/reject
// @access  Private (giáo viên, admin)
exports.rejectAttendance = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { reason } = req.body;

  // Kiểm tra quyền truy cập
  if (!req.user.role.includes(ROLES.TEACHER) && !req.user.role.includes(ROLES.ADMIN)) {
    return res.status(403).json({
      success: false,
      msg: MESSAGES.ERROR.FORBIDDEN
    });
  }

  let attendance = await Attendance.findById(id);

  if (!attendance) {
    return res.status(404).json({
      success: false,
      msg: MESSAGES.ATTENDANCE.NOT_FOUND
    });
  }

  // Kiểm tra trạng thái hiện tại
  if (attendance.status === ATTENDANCE_STATUS.REJECTED) {
    return res.status(400).json({
      success: false,
      msg: 'Điểm danh này đã bị từ chối trước đó'
    });
  }

  // Cập nhật trạng thái thành rejected
  attendance = await Attendance.findByIdAndUpdate(
    id,
    {
      status: ATTENDANCE_STATUS.REJECTED,
      rejectionReason: reason || DEFAULT_NOTES[ATTENDANCE_STATUS.REJECTED],
      rejectedBy: req.user.id,
      updatedAt: Date.now(),
      updatedBy: req.user.id
    },
    { new: true, runValidators: true }
  );

  res.json({
    success: true,
    data: attendance,
    message: 'Đã từ chối điểm danh'
  });
});

// @desc    Xóa thông tin điểm danh
// @route   DELETE /api/attendance/:id
// @access  Private (giáo viên, admin)
exports.deleteAttendance = asyncHandler(async (req, res) => {
  const { id } = req.params;

  const attendance = await Attendance.findById(id);

  if (!attendance) {
    return res.status(404).json({
      success: false,
      msg: MESSAGES.ATTENDANCE.NOT_FOUND
    });
  }

  await attendance.remove();

  res.json({
    success: true,
    data: {}
  });
});

// @desc    Thống kê điểm danh theo lớp
// @route   GET /api/attendance/statistics/class/:classId
// @access  Private (giáo viên, admin)
exports.getAttendanceStatistics = asyncHandler(async (req, res) => {
  const { classId } = req.params;
  const { startDate, endDate, session, period = 'month' } = req.query;

  // Kiểm tra quyền truy cập
  if (!req.user.role.includes(ROLES.TEACHER) && !req.user.role.includes(ROLES.ADMIN)) {
    return res.status(403).json({
      success: false,
      msg: MESSAGES.ERROR.FORBIDDEN
    });
  }

  // Lấy thông tin lớp
  const classInfo = await Class.findById(classId);
  if (!classInfo) {
    return res.status(404).json({
      success: false,
      msg: MESSAGES.CLASS.NOT_FOUND
    });
  }

  try {
    // Lấy số lượng học sinh từ StudentEnrollment
    const StudentEnrollmentService = require('../services/studentEnrollmentService');
    const totalStudents = await StudentEnrollmentService.getStudentCount(
      classId,
      classInfo.schoolYear
    );

    // Lấy và xử lý dữ liệu điểm danh
    const { validAttendances, statusCounts, studentStats, timeStats } = await processAttendanceData(
      classId,
      startDate,
      endDate,
      session,
      period
    );

    // Tính toán thống kê tổng quan
    const overview = calculateOverviewStats(
      validAttendances, 
      totalStudents, 
      statusCounts,
      startDate,
      endDate,
      session
    );

    res.json({
      success: true,
      data: {
        class: {
          id: classInfo._id,
          name: classInfo.name,
          schoolYear: classInfo.schoolYear,
          totalStudents
        },
        overview,
        students: Object.values(studentStats),
        timeStats: Object.values(timeStats).sort((a, b) => {
          if (period === 'year') {
            return parseInt(a.period) - parseInt(b.period);
          }
          return a.period.localeCompare(b.period);
        }),
        period
      }
    });
  } catch (error) {
    return res.status(500).json({
      success: false,
      msg: 'Lỗi khi lấy thống kê điểm danh'
    });
  }
});

// Hàm xử lý dữ liệu điểm danh
const processAttendanceData = async (classId, startDate, endDate, session, period) => {
  // Xây dựng query
  const query = buildAttendanceQuery(classId, startDate, endDate, session);
  console.log('Query:', JSON.stringify(query, null, 2));

  // Lấy danh sách điểm danh
  const attendances = await Attendance.find(query)
    .populate('student', 'name studentId')
    .sort({ date: 1 });
  console.log('Total attendances found:', attendances.length);
  console.log('Sample attendances:', attendances.map(a => ({
    id: a._id,
    student: a.student?._id,
    status: a.status,
    date: a.date
  })));

  // Lọc bỏ các bản ghi không có student
  const validAttendances = attendances.filter(attendance => attendance.student);
  console.log('Valid attendances:', validAttendances.length);
  console.log('Valid attendances details:', validAttendances.map(a => ({
    id: a._id,
    student: a.student?._id,
    status: a.status,
    date: a.date
  })));

  // Thống kê theo trạng thái
  const statusCounts = calculateStatusCounts(validAttendances);
  console.log('Status counts:', statusCounts);

  // Thống kê theo học sinh
  const studentStats = calculateStudentStats(validAttendances);
  console.log('Student stats:', Object.keys(studentStats).length);

  // Thống kê theo thời gian
  const timeStats = calculateTimeStats(validAttendances, period);
  console.log('Time stats:', Object.keys(timeStats).length);

  return { validAttendances, statusCounts, studentStats, timeStats };
};

// Hàm xây dựng query
const buildAttendanceQuery = (classId, startDate, endDate, session) => {
  const query = { class: classId };

  if (session && Object.values(SESSION).includes(session)) {
    query.session = session;
  }

  if (startDate || endDate) {
    query.date = {};

    if (startDate) {
      const start = new Date(startDate);
      start.setHours(0, 0, 0, 0);
      query.date.$gte = start;
    }

    if (endDate) {
      const end = new Date(endDate);
      end.setHours(23, 59, 59, 999);
      query.date.$lte = end;
    }
  }

  return query;
};

// Hàm tính toán thống kê theo trạng thái
const calculateStatusCounts = (attendances) => {
  const statusCounts = {
    [ATTENDANCE_STATUS.PRESENT]: 0,
    [ATTENDANCE_STATUS.ABSENT]: 0,
    [ATTENDANCE_STATUS.LATE]: 0,
    [ATTENDANCE_STATUS.REJECTED]: 0,
    [ATTENDANCE_STATUS.EXCUSED]: 0,
    total: attendances.length
  };

  attendances.forEach(attendance => {
    if (statusCounts[attendance.status] !== undefined) {
      statusCounts[attendance.status]++;
    }
  });

  return statusCounts;
};

// Hàm tính toán thống kê theo học sinh
const calculateStudentStats = (attendances) => {
  const studentStats = {};

  attendances.forEach(attendance => {
    const studentId = attendance.student._id.toString();

    if (!studentStats[studentId]) {
      studentStats[studentId] = {
        student: {
          id: studentId,
          name: attendance.student.name,
          studentId: attendance.student.studentId
        },
        [ATTENDANCE_STATUS.PRESENT]: 0,
        [ATTENDANCE_STATUS.ABSENT]: 0,
        [ATTENDANCE_STATUS.LATE]: 0,
        [ATTENDANCE_STATUS.REJECTED]: 0,
        [ATTENDANCE_STATUS.EXCUSED]: 0,
        total: 0,
        morningCount: 0,
        afternoonCount: 0,
        attendanceRate: 0
      };
    }

    studentStats[studentId][attendance.status]++;
    studentStats[studentId].total++;

    if (attendance.session === SESSION.MORNING) {
      studentStats[studentId].morningCount++;
    } else if (attendance.session === SESSION.AFTERNOON) {
      studentStats[studentId].afternoonCount++;
    }
  });

  // Tính tỷ lệ đi học cho mỗi học sinh
  Object.values(studentStats).forEach(stat => {
    const totalSessions = stat.morningCount + stat.afternoonCount;
    if (totalSessions > 0) {
      stat.attendanceRate = ((stat[ATTENDANCE_STATUS.PRESENT] + stat[ATTENDANCE_STATUS.EXCUSED]) / totalSessions * 100).toFixed(2);
    }
  });

  return studentStats;
};

// Hàm tính toán thống kê theo thời gian
const calculateTimeStats = (attendances, period) => {
  const timeStats = {};

  attendances.forEach(attendance => {
    const date = new Date(attendance.date);
    const timeKey = getTimeKey(date, period);

    if (!timeStats[timeKey]) {
      timeStats[timeKey] = {
        period: timeKey,
        periodName: getPeriodName(timeKey, period),
        [ATTENDANCE_STATUS.PRESENT]: 0,
        [ATTENDANCE_STATUS.ABSENT]: 0,
        [ATTENDANCE_STATUS.LATE]: 0,
        [ATTENDANCE_STATUS.REJECTED]: 0,
        [ATTENDANCE_STATUS.EXCUSED]: 0,
        total: 0,
        attendanceRate: 0
      };
    }

    timeStats[timeKey][attendance.status]++;
    timeStats[timeKey].total++;
  });

  // Tính tỷ lệ đi học cho mỗi kỳ
  Object.values(timeStats).forEach(stat => {
    if (stat.total > 0) {
      stat.attendanceRate = ((stat[ATTENDANCE_STATUS.PRESENT] + stat[ATTENDANCE_STATUS.EXCUSED]) / stat.total * 100).toFixed(2);
    }
  });

  return timeStats;
};

// Hàm tính toán thống kê tổng quan
const calculateOverviewStats = (attendances, totalStudents, statusCounts, startDate, endDate, session) => {
  const totalAttendanceRecords = attendances.length;
  const uniqueStudentsWithAttendance = new Set(attendances.map(a => a.student._id.toString())).size;
  const overallAttendanceRate = totalAttendanceRecords > 0
    ? ((statusCounts[ATTENDANCE_STATUS.PRESENT] + statusCounts[ATTENDANCE_STATUS.EXCUSED]) / totalAttendanceRecords * 100).toFixed(2)
    : 0;

  // Tính tổng lượt điểm danh dự kiến
  let totalExpectedAttendanceRecords = totalStudents;
  
  if (startDate && endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const daysDiff = Math.ceil((end - start) / (1000 * 60 * 60 * 24)) + 1; // +1 để bao gồm cả ngày cuối
    
    // Mỗi ngày chỉ cần điểm danh 1 lần (sáng hoặc chiều)
    totalExpectedAttendanceRecords = totalStudents * daysDiff;
  }

  return {
    totalAttendanceRecords,
    totalExpectedAttendanceRecords,
    studentsAttended: uniqueStudentsWithAttendance,
    overallAttendanceRate: parseFloat(overallAttendanceRate),
    studentsNotAttended: totalStudents - uniqueStudentsWithAttendance,
    statusBreakdown: statusCounts
  };
};

// Hàm lấy key thời gian theo period
const getTimeKey = (date, period) => {
  switch (period) {
    case 'day':
      return date.toISOString().split('T')[0];
    case 'week':
      const weekStart = new Date(date);
      weekStart.setDate(date.getDate() - date.getDay());
      return weekStart.toISOString().split('T')[0];
    case 'month':
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
    case 'year':
      return date.getFullYear().toString();
    default:
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
  }
};

// Hàm lấy tên hiển thị cho kỳ thống kê
const getPeriodName = (timeKey, period) => {
  switch (period) {
    case 'day':
      return new Date(timeKey).toLocaleDateString('vi-VN', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' });
    case 'week':
      const weekStart = new Date(timeKey);
      const weekEnd = new Date(timeKey);
      weekEnd.setDate(weekStart.getDate() + 6);
      return `Tuần ${weekStart.toLocaleDateString('vi-VN', { day: 'numeric', month: 'numeric' })} - ${weekEnd.toLocaleDateString('vi-VN', { day: 'numeric', month: 'numeric', year: 'numeric' })}`;
    case 'month':
      const [year, month] = timeKey.split('-');
      return new Date(year, month - 1).toLocaleDateString('vi-VN', { month: 'long', year: 'numeric' });
    case 'year':
      return `Năm ${timeKey}`;
    default:
      return timeKey;
  }
};

// @desc    Kiểm tra và cập nhật trạng thái điểm danh
// @route   GET /api/attendance/check-status
// @access  Private (học sinh)
exports.checkAttendanceStatus = asyncHandler(async (req, res) => {
  // Chỉ cho phép học sinh sử dụng API này
  if (!req.user.role.includes(ROLES.STUDENT)) {
    return res.status(403).json({
      success: false,
      msg: MESSAGES.ERROR.FORBIDDEN
    });
  }

  const studentId = req.user.id;
  const classId = req.user.class;

  // Lấy ngày hiện tại
  const today = new Date();
  const startOfDay = new Date(today);
  startOfDay.setHours(0, 0, 0, 0);

  const endOfDay = new Date(today);
  endOfDay.setHours(23, 59, 59, 999);

  // Xác định buổi học hiện tại (sáng/chiều)
  const currentHour = today.getHours();
  const currentSession = currentHour < 12 ? SESSION.MORNING : SESSION.AFTERNOON;

  // Lấy cấu hình điểm danh
  const config = await AttendanceConfig.findOne({ isActive: true }) || {
    morningStartHour: 7,
    morningStartMinute: 30,
    morningEndHour: 11,
    morningEndMinute: 30,
    afternoonStartHour: 13,
    afternoonStartMinute: 30,
    afternoonEndHour: 17,
    afternoonEndMinute: 0
  };

  // Kiểm tra xem buổi học đã kết thúc chưa
  let sessionEnded = false;

  if (currentSession === SESSION.MORNING) {
    const endTime = new Date(today);
    endTime.setHours(config.morningEndHour, config.morningEndMinute, 0, 0);
    sessionEnded = today >= endTime;
  } else {
    const endTime = new Date(today);
    endTime.setHours(config.afternoonEndHour, config.afternoonEndMinute, 0, 0);
    sessionEnded = today >= endTime;
  }

  // Nếu buổi học đã kết thúc, kiểm tra xem học sinh đã điểm danh chưa
  if (sessionEnded) {
    // Kiểm tra xem học sinh đã điểm danh cho buổi học này chưa
    const existingAttendance = await Attendance.findOne({
      student: studentId,
      class: classId,
      date: { $gte: startOfDay, $lte: endOfDay },
      session: currentSession
    });

    // Kiểm tra xem học sinh có đơn xin phép được phê duyệt không
    const approvedLeaveRequest = await LeaveRequest.findOne({
      student: studentId,
      status: 'approved',
      $and: [
        { startDate: { $lte: today } },
        { endDate: { $gte: today } }
      ],
      sessions: { $in: [currentSession, 'all-day'] } // 'all-day' should also be a constant
    });

    // Nếu học sinh chưa điểm danh và không có đơn xin phép, tự động đánh dấu vắng mặt
    if (!existingAttendance && !approvedLeaveRequest) {
      // Tạo bản ghi điểm danh với trạng thái "absent"
      await Attendance.create({
        student: studentId,
        class: classId,
        date: today,
        session: currentSession,
        status: ATTENDANCE_STATUS.ABSENT,
        location: 'Không có mặt (tự động)',
        notes: DEFAULT_NOTES.AUTO_ABSENT,
        createdBy: studentId
      });

      return res.json({
        success: true,
        message: `Buổi ${currentSession === SESSION.MORNING ? DISPLAY_SESSION[SESSION.MORNING] : DISPLAY_SESSION[SESSION.AFTERNOON]} đã kết thúc và bạn chưa điểm danh. Hệ thống đã tự động đánh dấu bạn vắng mặt.`,
        sessionEnded: true,
        attendanceCreated: true
      });
    } else if (existingAttendance) {
      return res.json({
        success: true,
        message: `Buổi ${currentSession === SESSION.MORNING ? DISPLAY_SESSION[SESSION.MORNING] : DISPLAY_SESSION[SESSION.AFTERNOON]} đã kết thúc và bạn đã điểm danh với trạng thái: ${DISPLAY_STATUS[existingAttendance.status] || existingAttendance.status}`,
        sessionEnded: true,
        attendanceCreated: false,
        attendanceStatus: existingAttendance.status
      });
    } else {
      return res.json({
        success: true,
        message: `Buổi ${currentSession === SESSION.MORNING ? DISPLAY_SESSION[SESSION.MORNING] : DISPLAY_SESSION[SESSION.AFTERNOON]} đã kết thúc nhưng bạn có đơn xin phép được phê duyệt.`,
        sessionEnded: true,
        attendanceCreated: false,
        leaveRequestApproved: true
      });
    }
  }

  // Nếu buổi học chưa kết thúc, trả về thông tin về thời gian kết thúc
  let endTime;
  if (currentSession === SESSION.MORNING) {
    endTime = new Date(today);
    endTime.setHours(config.morningEndHour, config.morningEndMinute, 0, 0);
  } else {
    endTime = new Date(today);
    endTime.setHours(config.afternoonEndHour, config.afternoonEndMinute, 0, 0);
  }

  return res.json({
    success: true,
    message: `Buổi ${currentSession === SESSION.MORNING ? DISPLAY_SESSION[SESSION.MORNING] : DISPLAY_SESSION[SESSION.AFTERNOON]} chưa kết thúc. Bạn vẫn có thể điểm danh.`,
    sessionEnded: false,
    currentSession,
    endTime
  });
});

// @desc    Thống kê điểm danh của một học sinh
// @route   GET /api/attendance/statistics/student/:studentId
// @access  Private (giáo viên, admin, học sinh)
exports.getStudentAttendanceStatistics = asyncHandler(async (req, res) => {
  const { studentId } = req.params;
  const { startDate, endDate, session } = req.query;

  // Kiểm tra quyền truy cập
  if (req.user.role.includes(ROLES.STUDENT) && req.user.id !== studentId) {
    return res.status(403).json({
      success: false,
      msg: MESSAGES.ERROR.FORBIDDEN
    });
  }

  // Xây dựng query
  const query = {
    student: studentId
  };

  // Thêm điều kiện buổi học nếu có
  if (session && Object.values(SESSION).includes(session)) {
    query.session = session;
  }

  // Thêm điều kiện thời gian nếu có
  if (startDate || endDate) {
    query.date = {};

    if (startDate) {
      const start = new Date(startDate);
      start.setHours(0, 0, 0, 0);
      query.date.$gte = start;
    }

    if (endDate) {
      const end = new Date(endDate);
      end.setHours(23, 59, 59, 999);
      query.date.$lte = end;
    }
  }

  // Lấy danh sách điểm danh
  const attendances = await Attendance.find(query)
    .populate('class', 'name')
    .populate('leaveRequest', 'reason')
    .sort({ date: 1 });

  // Thống kê theo trạng thái
  const statusCounts = {
    [ATTENDANCE_STATUS.PRESENT]: 0,
    [ATTENDANCE_STATUS.ABSENT]: 0,
    [ATTENDANCE_STATUS.LATE]: 0,
    [ATTENDANCE_STATUS.REJECTED]: 0,
    [ATTENDANCE_STATUS.EXCUSED]: 0, // Thêm trạng thái vắng có phép
    total: attendances.length
  };

  attendances.forEach(attendance => {
    if (statusCounts[attendance.status] !== undefined) {
      statusCounts[attendance.status]++;
    }
  });

  // Thống kê theo buổi học
  const sessionStats = {
    morning: {
      present: 0,
      absent: 0,
      late: 0,
      rejected: 0,
      excused: 0, // Thêm trạng thái vắng có phép
      total: 0
    },
    afternoon: {
      present: 0,
      absent: 0,
      late: 0,
      rejected: 0,
      excused: 0, // Thêm trạng thái vắng có phép
      total: 0
    }
  };

  attendances.forEach(attendance => {
    if (attendance.session && sessionStats[attendance.session]) {
      sessionStats[attendance.session][attendance.status]++;
      sessionStats[attendance.session].total++;
    }
  });

  // Thống kê theo tháng
  const monthStats = {};
  attendances.forEach(attendance => {
    const date = new Date(attendance.date);
    const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

    if (!monthStats[monthKey]) {
      monthStats[monthKey] = {
        month: monthKey,
        monthName: new Date(date.getFullYear(), date.getMonth(), 1).toLocaleString('vi-VN', { month: 'long', year: 'numeric' }),
        present: 0,
        absent: 0,
        late: 0,
        rejected: 0,
        excused: 0,
        total: 0,
        morningCount: 0,
        afternoonCount: 0
      };
    }

    monthStats[monthKey][attendance.status]++;
    monthStats[monthKey].total++;

    if (attendance.session === 'morning') {
      monthStats[monthKey].morningCount++;
    } else if (attendance.session === 'afternoon') {
      monthStats[monthKey].afternoonCount++;
    }
  });

  // Tính tỷ lệ điểm danh
  const attendanceRate = statusCounts.total > 0
    ? (statusCounts.present / statusCounts.total * 100).toFixed(2)
    : 0;

  res.json({
    success: true,
    data: {
      statusCounts,
      attendanceRate,
      sessionStats,
      monthStats: Object.values(monthStats).sort((a, b) => a.month.localeCompare(b.month)),
      recentAttendances: attendances.slice(0, 10).map(a => {
        const result = {
          id: a._id,
          date: a.date,
          session: a.session,
          sessionName: a.session === 'morning' ? 'Sáng' : 'Chiều',
          class: a.class.name,
          status: a.status,
          location: a.location
        };

        // Thêm thông tin về lý do nghỉ học nếu là vắng có phép
        if (a.status === 'excused' && a.leaveRequest) {
          result.excuseReason = a.leaveRequest.reason;
        }

        return result;
      }),
      totalAttendances: attendances.length
    }
  });
});

// @desc    Lấy danh sách điểm danh
// @route   GET /api/attendance
// @access  Private (học sinh chỉ xem của mình, giáo viên và admin xem tất cả)
exports.getAttendances = asyncHandler(async (req, res) => {
  const { classId, date, session, studentId } = req.query;
  
  let query = {};
  
  // Lọc theo role của người dùng
  if (req.user.role.includes('student')) {
    // Học sinh chỉ xem điểm danh của chính mình
    query.student = req.user.id;
    
    // Nếu có studentId trong query và khác với user hiện tại, báo lỗi
    if (studentId && studentId !== req.user.id) {
      return res.status(403).json({
        success: false,
        msg: MESSAGES.ERROR.FORBIDDEN
      });
    }
  } else if (req.user.role.includes('teacher') || req.user.role.includes('admin')) {
    // Giáo viên và admin có thể xem tất cả
    if (studentId) {
      query.student = studentId;
    }
  }

  // Lọc theo lớp
  if (classId) {
    query.class = classId;
  }

  // Lọc theo ngày
  if (date) {
    const startDate = new Date(date);
    startDate.setHours(0, 0, 0, 0);
    const endDate = new Date(date);
    endDate.setHours(23, 59, 59, 999);
    
    query.date = {
      $gte: startDate,
      $lte: endDate
    };
  }

  // Lọc theo buổi
  if (session && ['morning', 'afternoon'].includes(session)) {
    query.session = session;
  }

  const attendances = await Attendance.find(query)
    .populate('student', 'name studentId class')
    .populate('class', 'name group')
    .sort({ date: -1, session: 1 });

  res.json({
    success: true,
    count: attendances.length,
    data: attendances
  });
});

// @desc    Tạo hoặc cập nhật điểm danh
// @route   POST /api/attendance
// @access  Private (học sinh tự điểm danh, giáo viên và admin điểm danh cho học sinh)
exports.createOrUpdateAttendance = asyncHandler(async (req, res) => {
  const { studentId, classId, date, session, status, location, coordinates, notes } = req.body;

  // Kiểm tra dữ liệu đầu vào
  if (!classId || !date || !session) {
    return res.status(400).json({
      success: false,
      msg: MESSAGES.ERROR.VALIDATION_FAILED
    });
  }

  let targetStudentId = studentId;

  // Nếu là học sinh, chỉ được điểm danh cho chính mình
  if (req.user.role.includes('student')) {
    targetStudentId = req.user.id;
    
    // Nếu có studentId trong request và khác với user hiện tại, báo lỗi
    if (studentId && studentId !== req.user.id) {
      return res.status(403).json({
        success: false,
        msg: MESSAGES.ERROR.FORBIDDEN
      });
    }
  }

  // Kiểm tra buổi học hợp lệ
  if (session && !Object.values(SESSION).includes(session)) {
    return res.status(400).json({
      success: false,
      msg: `Buổi học không hợp lệ. Chỉ chấp nhận "${SESSION.MORNING}" hoặc "${SESSION.AFTERNOON}"`
    });
  }

  // Kiểm tra học sinh có tồn tại không
  const student = await User.findById(targetStudentId);
  if (!student || !student.role.includes('student')) {
    return res.status(404).json({
      success: false,
      msg: MESSAGES.STUDENT.NOT_FOUND
    });
  }

  // Kiểm tra lớp có tồn tại không
  const classObj = await Class.findById(classId);
  if (!classObj) {
    return res.status(404).json({
      success: false,
      msg: MESSAGES.CLASS.NOT_FOUND
    });
  }

  // Nếu là học sinh tự điểm danh, kiểm tra học sinh có thuộc lớp không
  if (req.user.role.includes('student')) {
    if (student.class && student.class.toString() !== classId) {
      return res.status(403).json({
        success: false,
        msg: MESSAGES.ERROR.FORBIDDEN
      });
    }
  }

  // Xác định ngày điểm danh
  const attendanceDate = date ? new Date(date) : new Date();
  const startOfDay = new Date(attendanceDate);
  startOfDay.setHours(0, 0, 0, 0);

  const endOfDay = new Date(attendanceDate);
  endOfDay.setHours(23, 59, 59, 999);

  // Xác định buổi học (sáng/chiều) nếu không được cung cấp
  const currentSession = session || (() => {
    const currentHour = new Date().getHours();
    return currentHour < 12 ? SESSION.MORNING : SESSION.AFTERNOON;
  })();

  // Kiểm tra thời gian điểm danh để xác định trạng thái (đúng giờ hay đi muộn)
  const determineAttendanceStatus = async () => {
    // Nếu giáo viên đã chỉ định trạng thái, sử dụng trạng thái đó
    const validStatuses = [
      ATTENDANCE_STATUS.PRESENT,
      ATTENDANCE_STATUS.ABSENT,
      ATTENDANCE_STATUS.LATE,
      ATTENDANCE_STATUS.REJECTED
    ];

    if (status && validStatuses.includes(status)) {
      return status;
    }

    // Lấy cấu hình điểm danh từ database
    let config = await AttendanceConfig.findOne({ isActive: true });

    // Nếu không có cấu hình nào, sử dụng cấu hình mặc định
    if (!config) {
      config = new AttendanceConfig();
    }

    const now = new Date();
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();

    // Lấy thời gian bắt đầu buổi học từ cấu hình
    let sessionStartHour, sessionStartMinute;
    if (currentSession === SESSION.MORNING) {
      sessionStartHour = config.morningStartHour;
      sessionStartMinute = config.morningStartMinute;
    } else {
      sessionStartHour = config.afternoonStartHour;
      sessionStartMinute = config.afternoonStartMinute;
    }

    // Tính số phút trễ
    const sessionStartMinutes = sessionStartHour * 60 + sessionStartMinute;
    const currentTimeMinutes = currentHour * 60 + currentMinute;
    const minutesLate = currentTimeMinutes - sessionStartMinutes;

    // Kiểm tra xem có đi muộn không
    if (minutesLate > config.lateThreshold) {
      return ATTENDANCE_STATUS.LATE;
    }

    // Kiểm tra xem có quá thời gian tối đa cho phép điểm danh không
    if (minutesLate > config.maxAttendanceTime) {
      return ATTENDANCE_STATUS.ABSENT;
    }

    return ATTENDANCE_STATUS.PRESENT;
  };

  // Xác định trạng thái điểm danh
  const attendanceStatus = req.user.role.includes(ROLES.STUDENT) ? await determineAttendanceStatus() : (status || ATTENDANCE_STATUS.PRESENT);

  // Lấy thông tin vị trí từ Zalo nếu là học sinh tự điểm danh và không có thông tin vị trí
  let locationInfo = {
    location: location || DEFAULT_NOTES.NO_LOCATION,
    coordinates: coordinates || null
  };
  if (req.user.role.includes(ROLES.STUDENT) && (!req.body.location || !req.body.coordinates)) {
    try {
      // Sử dụng token và accessToken từ request body
      if (token && accessToken) {
        // Gọi hàm getLocationWithAddress để lấy thông tin vị trí và địa chỉ
        const locationData = await getLocationWithAddress(token, accessToken);

        if (locationData.success) {
          locationInfo = {
            location: locationData.location || DEFAULT_NOTES.NO_LOCATION,
            coordinates: locationData.coordinates || null
          };
        }
      }
    } catch (error) {
      console.error('Lỗi khi lấy thông tin vị trí từ Zalo:', error.message);
      // Không trả về lỗi, tiếp tục xử lý với thông tin vị trí hiện có
    }
  }

  // Kiểm tra xem học sinh đã điểm danh cho buổi học này trong ngày chưa
  const existingAttendance = await Attendance.findOne({
    student: targetStudentId,
    class: classId,
    date: { $gte: startOfDay, $lte: endOfDay },
    session: currentSession
  });

  // Kiểm tra xem học sinh có yêu cầu xin phép nghỉ học được phê duyệt không
  const approvedLeaveRequest = await LeaveRequest.findOne({
    student: targetStudentId,
    class: classId,
    status: 'approved',
    $and: [
      { startDate: { $lte: attendanceDate } },
      { endDate: { $gte: attendanceDate } }
    ],
    sessions: { $in: [currentSession, 'all-day'] }
  });

  if (existingAttendance) {
    // Nếu là học sinh tự điểm danh và đã điểm danh rồi, thông báo
    if (req.user.role.includes('student')) {
      return res.status(400).json({
        success: false,
        msg: MESSAGES.ERROR.VALIDATION_FAILED
      });
    }

    // Nếu là giáo viên, cho phép cập nhật điểm danh
    existingAttendance.status = status || (req.user.role.includes(ROLES.STUDENT) ? attendanceStatus : existingAttendance.status);
    existingAttendance.location = locationInfo.location || existingAttendance.location;
    existingAttendance.coordinates = locationInfo.coordinates || existingAttendance.coordinates;
    existingAttendance.notes = notes || existingAttendance.notes;
    existingAttendance.updatedAt = Date.now();
    existingAttendance.updatedBy = req.user.id;

    await existingAttendance.save();

    return res.status(200).json({
      success: true,
      data: existingAttendance,
      message: MESSAGES.ATTENDANCE.UPDATED
    });
  }

  // Nếu học sinh có yêu cầu xin phép nghỉ học được phê duyệt
  if (approvedLeaveRequest) {
    // Tạo điểm danh với trạng thái "excused" (được phép nghỉ)
    const attendance = new Attendance({
      student: targetStudentId,
      class: classId,
      date: attendanceDate,
      session: currentSession,
      status: ATTENDANCE_STATUS.EXCUSED,
      location: locationInfo.location || DEFAULT_NOTES.NO_LOCATION,
      coordinates: locationInfo.coordinates,
      notes: notes || DEFAULT_NOTES[ATTENDANCE_STATUS.EXCUSED],
      leaveRequest: approvedLeaveRequest._id,
      createdBy: creatorId
    });

    await attendance.save();

    return res.status(201).json({
      success: true,
      data: attendance,
      message: 'Học sinh có đơn xin phép nghỉ học được phê duyệt'
    });
  }

  // Tạo điểm danh mới
  const attendance = new Attendance({
    student: targetStudentId,
    class: classId,
    date: attendanceDate,
    session: currentSession,
    status: attendanceStatus,
    location: locationInfo.location || DEFAULT_NOTES.NO_LOCATION,
    coordinates: locationInfo.coordinates,
    notes,
    createdBy: creatorId
  });

  await attendance.save();

  res.status(201).json({
    success: true,
    data: attendance
  });
});

// @desc    Lấy thống kê điểm danh của một học sinh
// @route   GET /api/attendance/student/:studentId/stats
// @access  Private (học sinh chỉ xem của mình, giáo viên và admin xem tất cả)
exports.getStudentAttendanceStats = asyncHandler(async (req, res) => {
  const { studentId } = req.params;
  
  // Kiểm tra quyền truy cập
  if (req.user.role.includes(ROLES.STUDENT) && req.user.id !== studentId) {
    return res.status(403).json({
      success: false,
      msg: MESSAGES.ERROR.FORBIDDEN
    });
  }

  // Xây dựng query
  const query = {
    student: studentId
  };

  // Thêm điều kiện thời gian nếu có
  const { startDate, endDate } = req.query;
  if (startDate || endDate) {
    query.date = {};

    if (startDate) {
      const start = new Date(startDate);
      start.setHours(0, 0, 0, 0);
      query.date.$gte = start;
    }

    if (endDate) {
      const end = new Date(endDate);
      end.setHours(23, 59, 59, 999);
      query.date.$lte = end;
    }
  }

  // Lấy danh sách điểm danh
  const attendances = await Attendance.find(query)
    .populate('class', 'name')
    .populate('leaveRequest', 'reason')
    .sort({ date: 1 });

  // Thống kê theo trạng thái
  const statusCounts = {
    [ATTENDANCE_STATUS.PRESENT]: 0,
    [ATTENDANCE_STATUS.ABSENT]: 0,
    [ATTENDANCE_STATUS.LATE]: 0,
    [ATTENDANCE_STATUS.REJECTED]: 0,
    [ATTENDANCE_STATUS.EXCUSED]: 0, // Thêm trạng thái vắng có phép
    total: attendances.length
  };

  attendances.forEach(attendance => {
    if (statusCounts[attendance.status] !== undefined) {
      statusCounts[attendance.status]++;
    }
  });

  // Thống kê theo buổi học
  const sessionStats = {
    morning: {
      present: 0,
      absent: 0,
      late: 0,
      rejected: 0,
      excused: 0, // Thêm trạng thái vắng có phép
      total: 0
    },
    afternoon: {
      present: 0,
      absent: 0,
      late: 0,
      rejected: 0,
      excused: 0, // Thêm trạng thái vắng có phép
      total: 0
    }
  };

  attendances.forEach(attendance => {
    if (attendance.session && sessionStats[attendance.session]) {
      sessionStats[attendance.session][attendance.status]++;
      sessionStats[attendance.session].total++;
    }
  });

  // Thống kê theo tháng
  const monthStats = {};
  attendances.forEach(attendance => {
    const date = new Date(attendance.date);
    const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

    if (!monthStats[monthKey]) {
      monthStats[monthKey] = {
        month: monthKey,
        monthName: new Date(date.getFullYear(), date.getMonth(), 1).toLocaleString('vi-VN', { month: 'long', year: 'numeric' }),
        present: 0,
        absent: 0,
        late: 0,
        rejected: 0,
        excused: 0,
        total: 0,
        morningCount: 0,
        afternoonCount: 0
      };
    }

    monthStats[monthKey][attendance.status]++;
    monthStats[monthKey].total++;

    if (attendance.session === 'morning') {
      monthStats[monthKey].morningCount++;
    } else if (attendance.session === 'afternoon') {
      monthStats[monthKey].afternoonCount++;
    }
  });

  // Tính tỷ lệ điểm danh
  const attendanceRate = statusCounts.total > 0
    ? (statusCounts.present / statusCounts.total * 100).toFixed(2)
    : 0;

  res.json({
    success: true,
    data: {
      statusCounts,
      attendanceRate,
      sessionStats,
      monthStats: Object.values(monthStats).sort((a, b) => a.month.localeCompare(b.month)),
      recentAttendances: attendances.slice(0, 10).map(a => {
        const result = {
          id: a._id,
          date: a.date,
          session: a.session,
          sessionName: a.session === 'morning' ? 'Sáng' : 'Chiều',
          class: a.class.name,
          status: a.status,
          location: a.location
        };

        // Thêm thông tin về lý do nghỉ học nếu là vắng có phép
        if (a.status === 'excused' && a.leaveRequest) {
          result.excuseReason = a.leaveRequest.reason;
        }

        return result;
      }),
      totalAttendances: attendances.length
    }
  });
});

// @desc    Cập nhật trạng thái điểm danh
// @route   PUT /api/attendance/:id
// @access  Private (giáo viên và admin)
exports.updateAttendanceStatus = asyncHandler(async (req, res) => {
  const { id } = req.params;
  const { status, notes } = req.body;

  // Kiểm tra quyền cập nhật
  if (!req.user.role.includes(ROLES.TEACHER) && !req.user.role.includes(ROLES.ADMIN)) {
    return res.status(403).json({
      success: false,
      msg: MESSAGES.ERROR.FORBIDDEN
    });
  }

  let attendance = await Attendance.findById(id);

  if (!attendance) {
    return res.status(404).json({
      success: false,
      msg: MESSAGES.ATTENDANCE.NOT_FOUND
    });
  }

  // Cập nhật thông tin
  attendance = await Attendance.findByIdAndUpdate(
    id,
    {
      status,
      notes,
      updatedAt: Date.now(),
      updatedBy: req.user.id
    },
    { new: true, runValidators: true }
  );

  res.json({
    success: true,
    data: attendance
  });
});

// @desc    Xóa bản ghi điểm danh
// @route   DELETE /api/attendance/:id
// @access  Private (giáo viên và admin)
exports.deleteAttendance = asyncHandler(async (req, res) => {
  const { id } = req.params;

  // Kiểm tra quyền xóa
  if (!req.user.role.includes(ROLES.TEACHER) && !req.user.role.includes(ROLES.ADMIN)) {
    return res.status(403).json({
      success: false,
      msg: MESSAGES.ERROR.FORBIDDEN
    });
  }

  const attendance = await Attendance.findById(id);

  if (!attendance) {
    return res.status(404).json({
      success: false,
      msg: MESSAGES.ATTENDANCE.NOT_FOUND
    });
  }

  await attendance.remove();

  res.json({
    success: true,
    data: {}
  });
});

// @desc    Lấy thống kê điểm danh của lớp
// @route   GET /api/attendance/class/:classId/stats
// @access  Private (giáo viên và admin)
exports.getClassAttendanceStats = asyncHandler(async (req, res) => {
  const { classId } = req.params;

  // Kiểm tra quyền truy cập
  if (!req.user.role.includes(ROLES.TEACHER) && !req.user.role.includes(ROLES.ADMIN)) {
    return res.status(403).json({
      success: false,
      msg: MESSAGES.ERROR.FORBIDDEN
    });
  }

  // Lấy thông tin lớp
  const classInfo = await Class.findById(classId);
  if (!classInfo) {
    return res.status(404).json({
      success: false,
      msg: MESSAGES.CLASS.NOT_FOUND
    });
  }

  // Lấy số lượng học sinh từ StudentEnrollment
  const StudentEnrollmentService = require('../services/studentEnrollmentService');
  const totalStudents = await StudentEnrollmentService.getStudentCount(
    classId,
    classInfo.schoolYear
  );

  // Xây dựng query
  const query = {
    class: classId
  };

  // Thêm điều kiện thời gian nếu có
  const { startDate, endDate } = req.query;
  if (startDate || endDate) {
    query.date = {};

    if (startDate) {
      const start = new Date(startDate);
      start.setHours(0, 0, 0, 0);
      query.date.$gte = start;
    }

    if (endDate) {
      const end = new Date(endDate);
      end.setHours(23, 59, 59, 999);
      query.date.$lte = end;
    }
  }

  // Lấy danh sách điểm danh
  const attendances = await Attendance.find(query)
    .populate('student', 'name studentId')
    .sort({ date: 1 });

  // Thống kê theo trạng thái
  const statusCounts = {
    [ATTENDANCE_STATUS.PRESENT]: 0,
    [ATTENDANCE_STATUS.ABSENT]: 0,
    [ATTENDANCE_STATUS.LATE]: 0,
    [ATTENDANCE_STATUS.REJECTED]: 0,
    total: attendances.length
  };

  attendances.forEach(attendance => {
    if (statusCounts[attendance.status] !== undefined) {
      statusCounts[attendance.status]++;
    }
  });

  // Thống kê theo học sinh
  const studentStats = {};
  attendances.forEach(attendance => {
    const studentId = attendance.student._id.toString();

    if (!studentStats[studentId]) {
      studentStats[studentId] = {
        student: {
          id: studentId,
          name: attendance.student.name,
          studentId: attendance.student.studentId
        },
        [ATTENDANCE_STATUS.PRESENT]: 0,
        [ATTENDANCE_STATUS.ABSENT]: 0,
        [ATTENDANCE_STATUS.LATE]: 0,
        [ATTENDANCE_STATUS.REJECTED]: 0,
        total: 0,
        morningCount: 0,
        afternoonCount: 0
      };
    }

    studentStats[studentId][attendance.status]++;
    studentStats[studentId].total++;

    // Đếm theo buổi
    if (attendance.session === SESSION.MORNING) {
      studentStats[studentId].morningCount++;
    } else if (attendance.session === SESSION.AFTERNOON) {
      studentStats[studentId].afternoonCount++;
    }
  });

  // Thống kê theo ngày và buổi
  const dateStats = {};
  attendances.forEach(attendance => {
    const dateStr = attendance.date.toISOString().split('T')[0];
    const sessionKey = `${dateStr}_${attendance.session}`;

    if (!dateStats[sessionKey]) {
      dateStats[sessionKey] = {
        date: dateStr,
        session: attendance.session,
        sessionName: attendance.session === SESSION.MORNING ? DISPLAY_SESSION[SESSION.MORNING] : DISPLAY_SESSION[SESSION.AFTERNOON],
        [ATTENDANCE_STATUS.PRESENT]: 0,
        [ATTENDANCE_STATUS.ABSENT]: 0,
        [ATTENDANCE_STATUS.LATE]: 0,
        [ATTENDANCE_STATUS.REJECTED]: 0,
        total: 0
      };
    }

    dateStats[sessionKey][attendance.status]++;
    dateStats[sessionKey].total++;
  });

  // Tính toán thống kê tổng quan
  const totalAttendanceRecords = attendances.length;
  const uniqueStudentsWithAttendance = new Set(attendances.map(a => a.student._id.toString())).size;
  const attendanceRate = totalStudents > 0 ? ((uniqueStudentsWithAttendance / totalStudents) * 100).toFixed(2) : 0;

  res.json({
    success: true,
    data: {
      class: {
        id: classInfo._id,
        name: classInfo.name,
        schoolYear: classInfo.schoolYear,
        totalStudents: totalStudents
      },
      overview: {
        totalAttendanceRecords,
        studentsAttended: uniqueStudentsWithAttendance,
        attendanceRate: parseFloat(attendanceRate),
        studentsNotAttended: totalStudents - uniqueStudentsWithAttendance
      },
      students: Object.values(studentStats),
      sessions: Object.values(dateStats).sort((a, b) => {
        // Sắp xếp theo ngày và buổi (sáng trước, chiều sau)
        const dateCompare = new Date(a.date) - new Date(b.date);
        if (dateCompare !== 0) return dateCompare;
        return a.session === SESSION.MORNING ? -1 : 1;
      })
    }
  });
});

// @desc    Lấy thông tin vị trí để điểm danh
// @route   GET /api/attendance/location
// @access  Private (học sinh)
exports.getAttendanceLocation = asyncHandler(async (req, res) => {
  // Chỉ học sinh mới cần thông tin vị trí điểm danh
  if (!req.user.role.includes(ROLES.STUDENT)) {
    return res.status(403).json({
      success: false,
      msg: MESSAGES.ERROR.FORBIDDEN
    });
  }

  // ... existing code ...
});

// @desc    Lấy lịch sử điểm danh của học sinh trong một khoảng thời gian
// @route   GET /api/attendance/student/:studentId/history
// @access  Private (học sinh chỉ xem của mình, giáo viên và admin xem tất cả)
exports.getStudentAttendanceHistory = asyncHandler(async (req, res) => {
  const { studentId } = req.params;
  
  // Kiểm tra quyền truy cập
  if (req.user.role.includes(ROLES.STUDENT) && req.user.id !== studentId) {
    return res.status(403).json({
      success: false,
      msg: MESSAGES.ERROR.FORBIDDEN
    });
  }

  // ... existing code ...
});


