// cronjobs/examTimeLimitChecker.js
const cron = require('node-cron');
const ExamAttempt = require('../models/ExamAttempt');
const Exam = require('../models/Exam');
const { processExamSubmission } = require('../utils/examUtils');

// Hàm xử lý kiểm tra bài thi hết hạn
const processExamTimeLimits = async () => {
  const uncompletedAttempts = await ExamAttempt.find({ isCompleted: false });
  
  // Nếu không có bài thi nào chưa hoàn thành, thoát sớm
  if (uncompletedAttempts.length === 0) {
    return;
  }
  
  for (const attempt of uncompletedAttempts) {
    try {
      // Lấy thông tin đề thi
      const exam = await Exam.findById(attempt.exam);
      if (!exam) {
        console.log(`Không tìm thấy đề thi cho attempt ${attempt._id}`);
        continue;
      }
      
      // Tính thời gian kết thúc
      const endTime = new Date(attempt.startTime.getTime() + exam.timeLimit * 60000);
      const now = new Date();
      
      // Nếu đã hết thời gian
      if (now > endTime) {
        console.log(`Tự động nộp bài thi ${attempt._id} của học sinh ${attempt.student}`);
        
        // Đặt thời gian kết thúc đúng bằng hạn chót
        attempt.endTime = endTime;
        
        // Xử lý nộp bài bằng hàm tái sử dụng
        await processExamSubmission(attempt);
        
        console.log(`Đã hoàn thành tự động nộp bài cho attempt ${attempt._id}`);
      } else {
        const timeRemaining = Math.floor((endTime - now) / 60000);
        console.log(`Bài thi ${attempt._id} còn ${timeRemaining} phút`);
      }
    } catch (innerErr) {
      console.error(`Lỗi khi xử lý attempt ${attempt._id}:`, innerErr);
      continue;
    }
  }
};

// Định nghĩa cron job chạy mỗi phút
const initExamTimeLimitChecker = () => {
  console.log('🕒 Exam Time Limit Checker started - checking every minute');
  
  cron.schedule('* * * * *', async () => {
    try {
      await processExamTimeLimits();
    } catch (err) {
      console.error('❌ Error in Exam Time Limit Checker:', err);
    }
  });
};

module.exports = initExamTimeLimitChecker;