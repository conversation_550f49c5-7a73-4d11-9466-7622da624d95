const { body, validationResult } = require('express-validator');

exports.validateExamSupervision = [
  body('examType')
    .isIn(['CUOI_HK1', 'CUOI_HK2', 'THI_TIEP_CAN_LAN_1', 'THI_TIEP_CAN_LAN_2', 'THI_TIEP_CAN_LAN_3'])
    .withMessage('Loại kỳ thi không hợp lệ'),
  body('schoolYear')
    .matches(/^\d{4}-\d{4}$/)
    .withMessage('Năm học phải có định dạng YYYY-YYYY'),
  body('teacher')
    .isMongoId()
    .withMessage('ID giáo viên không hợp lệ'),
  body('sessions')
    .isArray({ min: 1 })
    .withMessage('<PERSON>ải có ít nhất 1 buổi coi thi'),
  body('sessions.*.date')
    .isISO8601()
    .withMessage('<PERSON><PERSON><PERSON> không hợp lệ'),
  body('sessions.*.timeSlot')
    .isIn(['morning', 'afternoon'])
    .withMessage('Ca thi không hợp lệ'),
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  },
];

exports.validateChangeRequest = [
  body('supervisionId')
    .isMongoId()
    .withMessage('ID phân công không hợp lệ'),
  body('sessionId')
    .isMongoId()
    .withMessage('ID buổi thi không hợp lệ'),
  body('reason')
    .isLength({ min: 10 })
    .withMessage('Lý do phải có ít nhất 10 ký tự'),
  body('proposedNewDate')
    .optional()
    .isISO8601()
    .withMessage('Ngày đề xuất không hợp lệ'),
  body('proposedNewTimeSlot')
    .optional()
    .isIn(['morning', 'afternoon'])
    .withMessage('Ca thi đề xuất không hợp lệ'),
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  },
];

exports.validateApproval = [
  body('status')
    .isIn(['APPROVED', 'REJECTED'])
    .withMessage('Trạng thái phê duyệt không hợp lệ'),
  body('approvalNotes')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Ghi chú không được quá 500 ký tự'),
  body('newDate')
    .if(body('status').equals('APPROVED'))
    .optional()
    .isISO8601()
    .withMessage('Ngày mới không hợp lệ'),
  body('newTimeSlot')
    .if(body('status').equals('APPROVED'))
    .optional()
    .isIn(['morning', 'afternoon'])
    .withMessage('Ca thi mới không hợp lệ'),
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  },
]; 