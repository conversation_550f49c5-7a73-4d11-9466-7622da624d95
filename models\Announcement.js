const mongoose = require('mongoose');

const announcementSchema = new mongoose.Schema({
  sender: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User', // Người gửi thông báo
    required: true,
  },

  // Tiêu đề thông báo
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200,
  },

  // Loại thông báo
  type: {
    type: String,
    enum: ['teacher_to_student', 'principal_to_teacher', 'head_to_teacher', 'admin_to_all', 'mutual_communication'],
    required: true,
  },

  // Đ<PERSON>i tượng nhận thông báo
  recipients: {
    // Cho thông báo teacher_to_student
    class: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Class',
    },
    subject: {
      type: String, // Môn học cụ thể
    },

    // Cho thông báo principal_to_teacher, head_to_teacher
    teachers: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    }],

    // Cho thông báo theo bộ môn
    department: {
      type: String, // Tên bộ môn: "Toán", "<PERSON>ý", "Hóa"...
    },

    // Cho thông báo toàn trường
    schoolWide: {
      type: Boolean,
      default: false,
    },

    // Cho thông báo mutual_communication (gửi cho user cụ thể)
    specificUsers: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    }]
  },

  content: {
    type: String,
    required: true,
  },

  // Cấu hình thông báo Zalo
  zaloConfig: {
    enabled: {
      type: Boolean,
      default: false,
    },
    groupId: {
      type: String, // ID nhóm Zalo
    },
    groupName: {
      type: String, // Tên nhóm Zalo
    },
    scheduledTime: {
      type: Date, // Thời gian lên lịch gửi
    },
    sent: {
      type: Boolean,
      default: false,
    },
    sentAt: {
      type: Date,
    },
    lastError: {
      type: String, // Lỗi gần nhất khi gửi Zalo
    },
    lastErrorAt: {
      type: Date, // Thời gian lỗi gần nhất
    }
  },

  // Trạng thái thông báo
  status: {
    type: String,
    enum: ['draft', 'scheduled', 'sent'],
    default: 'draft',
  },

  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

// Virtual field để đếm số lượng người đã đọc
announcementSchema.virtual('readCount', {
  ref: 'AnnouncementRead',
  localField: '_id',
  foreignField: 'announcement',
  count: true,
  match: { isRead: true }
});

// Virtual field để lấy danh sách người đã đọc
announcementSchema.virtual('readBy', {
  ref: 'AnnouncementRead',
  localField: '_id',
  foreignField: 'announcement',
  match: { isRead: true }
});

// Method để kiểm tra user đã đọc thông báo chưa
announcementSchema.methods.isReadByUser = async function(userId) {
  const AnnouncementRead = mongoose.model('AnnouncementRead');
  const readRecord = await AnnouncementRead.findOne({
    announcement: this._id,
    user: userId,
    isRead: true
  });
  return !!readRecord;
};

// Method để đánh dấu đã đọc bởi user
announcementSchema.methods.markAsReadByUser = async function(userId) {
  const AnnouncementRead = mongoose.model('AnnouncementRead');
  return await AnnouncementRead.findOneAndUpdate(
    {
      announcement: this._id,
      user: userId
    },
    {
      announcement: this._id,
      user: userId,
      isRead: true,
      readAt: new Date()
    },
    {
      upsert: true,
      new: true
    }
  );
};

// Method để tạo thông báo mutual communication
announcementSchema.statics.createMutualCommunication = async function(data) {
  const {
    senderId,
    recipientIds,
    title,
    content
  } = data;

  return await this.create({
    sender: senderId,
    title,
    content,
    type: 'mutual_communication',
    recipients: {
      specificUsers: recipientIds
    },
    status: 'sent'
  });
};

// Method để lấy thông báo mutual communication theo user
announcementSchema.statics.findMutualCommunicationsByUser = async function(userId) {
  return await this.find({
    type: 'mutual_communication',
    'recipients.specificUsers': userId
  })
    .populate('sender', 'name email role')
    .populate('recipients.specificUsers', 'name email role')
    .sort({ createdAt: -1 });
};

// Đảm bảo virtual fields được bao gồm khi convert sang JSON
announcementSchema.set('toJSON', { virtuals: true });
announcementSchema.set('toObject', { virtuals: true });

// Index để tối ưu truy vấn
announcementSchema.index({ type: 1, createdAt: -1 });
announcementSchema.index({ 'recipients.class': 1, type: 1 });
announcementSchema.index({ 'recipients.department': 1 });
announcementSchema.index({ 'zaloConfig.scheduledTime': 1, status: 1 });
announcementSchema.index({ 'zaloConfig.enabled': 1, 'zaloConfig.sent': 1 });
announcementSchema.index({ title: 'text', content: 'text' }); // Text search index

// Index cho mutual communication
announcementSchema.index({ 'recipients.specificUsers': 1, type: 1, createdAt: -1 });

module.exports = mongoose.model('Announcement', announcementSchema);