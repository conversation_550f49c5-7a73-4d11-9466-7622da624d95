{"name": "school-management-system", "version": "1.0.0", "description": "<PERSON><PERSON> thống quản lý trường học", "main": "server.js", "scripts": {"start": "node server.js", "server": "nodemon server.js", "client": "npm start --prefix ../frontend", "dev": "concurrently \"npm run server\" \"npm run client\""}, "dependencies": {"axios": "^1.9.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "express-async-handler": "^1.2.0", "express-validator": "^7.2.1", "form-data": "^4.0.3", "jsonwebtoken": "^9.0.2", "mongoose": "^7.5.2", "multer": "^1.4.5-lts.2", "node-cron": "^3.0.3"}, "devDependencies": {"concurrently": "^8.2.1", "nodemon": "^3.0.1"}}