// src/middlewares/auth.js
// (<PERSON><PERSON> đảm bảo chúng ta đang sửa đúng file mà các routes đang import)
const jwt = require('jsonwebtoken');
const config = require('../config/default');
const User = require('../models/User');
const mongoose = require('mongoose');
const ROLES = require('../constants/roleConstants');

// Middleware bảo vệ các route yêu cầu xác thực
exports.protect = async (req, res, next) => {
  const token = req.header('x-auth-token');
  console.log('Token:', token); // Debug

  if (!token) {
    return res.status(401).json({ msg: 'Không có token, truy cập bị từ chối' });
  }

  try {
    const decoded = jwt.verify(token, config.jwtSecret);
    console.log('Decoded JWT:', decoded); // Debug
    console.log('User ID from token:', decoded.user.id); // Debug ID cụ thể

    // Kiểm tra định dạng ID hợp lệ
    if (!mongoose.Types.ObjectId.isValid(decoded.user.id)) {
      console.error('Invalid ObjectId format:', decoded.user.id);
      return res.status(401).json({ msg: 'ID người dùng không hợp lệ' });
    }

    const user = await User.findById(decoded.user.id).select('-password');
    console.log('User từ database:', user ? user.name : 'Không tìm thấy'); // Debug chi tiết hơn

    // Thử query trực tiếp nếu không tìm thấy bằng findById
    if (!user) {
      console.log('Thử tìm bằng query trực tiếp...');
      const directUser = await User.findOne({ _id: decoded.user.id }).select('-password');
      console.log('Kết quả tìm trực tiếp:', directUser);

      if (!directUser) {
        return res.status(401).json({ msg: 'Người dùng không tồn tại' });
      }
      req.user = directUser;
      next();
      return;
    }

    req.user = user;
    next();
  } catch (err) {
    console.error('JWT Error:', err.message);
    res.status(401).json({ msg: 'Token không hợp lệ' });
  }
};

// Middleware kiểm tra vai trò người dùng
// Cho phép sử dụng như authorize('admin', 'teacher')
// và authorize["admin"] (để backward compatibility)
exports.authorize = function () {
  // Xử lý cả hai trường hợp: authorize('admin', 'teacher') - arguments là các string roles
  let roles = [];

  // Trường hợp 1: authorize('admin', 'teacher') - arguments là các string roles
  if (arguments.length > 0) {
    roles = Array.from(arguments);
  }

  // Trường hợp 2: bỏ qua - phòng trường hợp gọi authorize["admin"]
  // trong trường hợp này, không có arguments nào được truyền vào

  return (req, res, next) => {
    // Kiểm tra đã đăng nhập chưa
    if (!req.user) {
      return res.status(401).json({
        msg: 'Vui lòng đăng nhập trước'
      });
    }

    // Nếu không có roles nào được chỉ định (trường hợp authorize["admin"]),
    // tạm thời cho phép mọi request (để tránh lỗi)
    if (roles.length === 0) {
      console.warn('WARNING: authorize được gọi không đúng cách. Vui lòng sử dụng authorize("role") thay vì authorize["role"]');
      return next();
    }

    // Kiểm tra vai trò - hỗ trợ cả role string (cũ) và role array (mới)
    const userRoles = Array.isArray(req.user.role) ? req.user.role : [req.user.role];
    const hasRequiredRole = roles.some(role => userRoles.includes(role));

    if (!hasRequiredRole) {
      return res.status(403).json({
        msg: `Vai trò ${userRoles.join(', ')} không có quyền truy cập`
      });
    }

    next();
  };
};

// Bổ sung index interface để hỗ trợ cú pháp authorize["admin"]
// Đây là giải pháp tạm thời, nên sửa code gốc để sử dụng authorize('admin')
exports.authorize.admin = function (req, res, next) {
  if (!req.user) {
    return res.status(401).json({
      msg: 'Vui lòng đăng nhập trước'
    });
  }

  const userRoles = Array.isArray(req.user.role) ? req.user.role : [req.user.role];
  if (!userRoles.includes(ROLES.ADMIN)) {
    return res.status(403).json({
      msg: `Vai trò ${userRoles.join(', ')} không có quyền truy cập`
    });
  }

  next();
};

// Thêm các roles khác nếu cần
exports.authorize.teacher = function (req, res, next) {
  if (!req.user) {
    return res.status(401).json({
      msg: 'Vui lòng đăng nhập trước'
    });
  }

  const userRoles = Array.isArray(req.user.role) ? req.user.role : [req.user.role];
  if (!userRoles.includes(ROLES.TEACHER)) {
    return res.status(403).json({
      msg: `Vai trò ${userRoles.join(', ')} không có quyền truy cập`
    });
  }

  next();
};

exports.authorize.student = function (req, res, next) {
  if (!req.user) {
    return res.status(401).json({
      msg: 'Vui lòng đăng nhập trước'
    });
  }

  const userRoles = Array.isArray(req.user.role) ? req.user.role : [req.user.role];
  if (!userRoles.includes(ROLES.STUDENT)) {
    return res.status(403).json({
      msg: `Vai trò ${userRoles.join(', ')} không có quyền truy cập`
    });
  }

  next();
};

// Alias cho protect nếu có code đang sử dụng auth thay vì protect
exports.auth = exports.protect;

// Middleware kiểm tra giáo viên có quyền với lớp
exports.checkClassAccess = async (req, res, next) => {
  // Nếu là admin, luôn cho phép
  const userRoles = Array.isArray(req.user.role) ? req.user.role : [req.user.role];
  if (userRoles.includes(ROLES.ADMIN)) {
    return next();
  }

  // Lấy classId từ body hoặc query
  const classId = req.body.classId || req.query.classId || req.params.classId;

  if (!classId) {
    return res.status(400).json({
      msg: 'Thiếu thông tin lớp học'
    });
  }

  try {
    // Cần kiểm tra xem giáo viên này có phải là giáo viên của lớp hoặc giáo viên chủ nhiệm không
    const Class = require('../models/Class');
    const classExists = await Class.findById(classId);

    if (!classExists) {
      return res.status(404).json({
        msg: 'Không tìm thấy lớp học'
      });
    }

    // Kiểm tra nếu là giáo viên chủ nhiệm
    if (classExists.homeroomTeacher &&
      classExists.homeroomTeacher.toString() === req.user._id.toString()) {
      return next();
    }

    // Tạm thời cho phép mọi giáo viên truy cập
    if (userRoles.includes(ROLES.TEACHER)) {
      return next();
    }

    return res.status(403).json({
      msg: 'Bạn không có quyền với lớp học này'
    });
  } catch (err) {
    console.error('Error in checkClassAccess:', err);
    return res.status(500).json({
      msg: 'Lỗi máy chủ'
    });
  }
};

// Middleware kiểm tra học sinh chỉ xem được điểm của mình
exports.checkStudentOwnership = async (req, res, next) => {
  try {
    // Nếu là admin hoặc giáo viên, luôn cho phép
    const userRoles = Array.isArray(req.user.role) ? req.user.role : [req.user.role];
    if (userRoles.includes(ROLES.ADMIN) || userRoles.includes(ROLES.TEACHER)) {
      return next();
    }

    // Lấy studentId từ params hoặc body hoặc query
    const studentId = req.params.studentId || req.body.studentId || req.query.studentId;

    if (!studentId) {
      return res.status(400).json({
        msg: 'Thiếu thông tin học sinh'
      });
    }

    // Học sinh chỉ được xem điểm của chính mình
    if (userRoles.includes(ROLES.STUDENT) && req.user._id.toString() !== studentId) {
      return res.status(403).json({
        msg: 'Bạn chỉ có thể xem điểm của chính mình'
      });
    }

    next();
  } catch (err) {
    console.error('Error in checkStudentOwnership:', err);
    return res.status(500).json({
      msg: 'Lỗi máy chủ'
    });
  }
};