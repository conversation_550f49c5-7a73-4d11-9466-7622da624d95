// src/controllers/scheduleController.js
const Schedule = require('../models/Schedule');
const User = require('../models/User');
const Class = require('../models/Class');
const { VALID_DAYS } = require('../helper/constants'); // Import enum
const MESSAGES = require('../constants/messages');


// @desc    Lấy lịch học của học sinh đang đăng nhập
// @route   GET /api/schedules
// @access  Private (Student only)
exports.getSchedules = async (req, res) => {
  try {
    const user = await User.findById(req.user.id).populate('class');
    if (!user || user.role !== 'student') {
      return res.status(403).json({ msg: MESSAGES.ERROR.FORBIDDEN });
    }

    const { schoolYear } = req.query;
    const query = {
      class: user.class._id,
      schoolYear: schoolYear || user.class.schoolYear,
    };

    const schedules = await Schedule.find(query)
      .populate('class', 'name')
      .populate('periods.teacher', 'name')
      .sort({ day: 1 });

    res.json(schedules);
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ msg: MESSAGES.ERROR.GENERAL });
  }
};

// @desc    Lấy thời khóa biểu của lớp học sinh
// @route   GET /api/schedules/my-class
// @access  Private (Student only)
exports.getMyClassSchedule = async (req, res) => {
  try {
    const user = await User.findById(req.user.id).populate('class');
    if (!user) {
      return res.status(401).json({ msg: MESSAGES.USER.NOT_FOUND });
    }

    console.log(`user: ${user}`);
    // Chỉ học sinh được truy cập
    if (!user.role || (Array.isArray(user.role) ? !user.role.includes('student') : user.role !== 'student')) {
      return res.status(403).json({ msg: MESSAGES.ERROR.FORBIDDEN });
    }

    // Kiểm tra học sinh có thuộc lớp nào không
    if (!user.class) {
      return res.status(400).json({ msg: MESSAGES.ERROR.VALIDATION_FAILED });
    }

    const { schoolYear } = req.query;

    // Xây dựng query
    const query = { class: user.class._id };
    if (schoolYear) {
      query.schoolYear = schoolYear;
    } else {
      query.schoolYear = user.class.schoolYear; // Mặc định lấy schoolYear từ lớp học
    }

    const schedules = await Schedule.find(query)
      .populate('class', 'name')
      .populate('periods.teacher', 'name')
      .sort({ day: 1 })
      .lean(); // Tối ưu hiệu suất

    if (schedules.length === 0) {
      return res.status(404).json({ msg: MESSAGES.ERROR.NOT_FOUND });
    }

    res.json(schedules);
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ msg: MESSAGES.ERROR.GENERAL });
  }
};

// @desc    Lấy thời khóa biểu của một lớp hoặc tất cả lớp (admin only)
// @route   GET /api/schedules/class/:classId?
// @access  Private (Admin only)
exports.getSchedulesForAdmin = async (req, res) => {
  try {
    const user = await User.findById(req.user.id);
    if (!user || user.role !== 'admin') {
      return res.status(403).json({ msg: MESSAGES.ERROR.FORBIDDEN });
    }

    const { classId } = req.params;
    const { schoolYear, grade, day } = req.query;

    // Xây dựng query
    const query = {};

    // Nếu có classId, kiểm tra và thêm vào query
    if (classId) {
      if (!mongoose.Types.ObjectId.isValid(classId)) {
        return res.status(400).json({ msg: MESSAGES.ERROR.VALIDATION_FAILED });
      }
      query.class = classId;
    }

    // Thêm bộ lọc schoolYear nếu có
    if (schoolYear) {
      query.schoolYear = schoolYear;
    }

    // Thêm bộ lọc grade nếu có
    if (grade) {
      const classes = await Class.find({ grade }).select('_id');
      if (classes.length === 0) {
        return res.status(404).json({ msg: MESSAGES.CLASS.NOT_FOUND });
      }
      query.class = { $in: classes.map(c => c._id) };
    }

    // Thêm bộ lọc day nếu có
    if (day) {
      // Sử dụng VALID_DAYS từ constants
      if (!VALID_DAYS.includes(day)) {
        return res.status(400).json({ msg: MESSAGES.ERROR.VALIDATION_FAILED });
      }
      query.day = day;
    }

    const schedules = await Schedule.find(query)
      .populate('class', 'name grade')
      .populate('periods.teacher', 'name')
      .sort({ 'class.name': 1, day: 1, 'periods.periodNumber': 1 }) // Sắp xếp thêm periodNumber
      .lean();

    if (schedules.length === 0) {
      return res.status(404).json({ msg: MESSAGES.ERROR.NOT_FOUND });
    }

    res.json(schedules);
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ msg: MESSAGES.ERROR.GENERAL });
  }
};

// @desc    Lấy thời khóa biểu của lớp học sinh
// @route   GET /api/schedules/my-class
// @access  Private (Student only)
exports.getMyClassSchedule = async (req, res) => {
  try {
    const user = await User.findById(req.user.id);
    if (!user) {
      return res.status(401).json({ msg: MESSAGES.USER.NOT_FOUND });
    }

    // Chỉ học sinh được truy cập
    if (!user.role || (Array.isArray(user.role) ? !user.role.includes('student') : user.role !== 'student')) {
      return res.status(403).json({ msg: MESSAGES.ERROR.FORBIDDEN });
    }

    // Lấy lớp hiện tại từ StudentEnrollment
    const StudentEnrollment = require('../models/StudentEnrollment');
    const enrollment = await StudentEnrollment.findOne({
      student: user._id,
      status: 'active'
    }).populate('class', 'name schoolYear');

    if (!enrollment) {
      return res.status(400).json({ msg: 'Học sinh chưa được ghi danh vào lớp nào' });
    }

    const { schoolYear, day } = req.query;

    // Xây dựng query
    const query = { class: enrollment.class._id };
    if (schoolYear) {
      query.schoolYear = schoolYear;
    } else {
      query.schoolYear = enrollment.class.schoolYear; // Mặc định lấy schoolYear từ lớp học
    }
    if (day) {
      // Sử dụng VALID_DAYS từ constants
      if (!VALID_DAYS.includes(day)) {
        return res.status(400).json({ msg: MESSAGES.ERROR.VALIDATION_FAILED });
      }
      query.day = day;
    }

    const schedules = await Schedule.find(query)
      .populate('class', 'name')
      .populate({
        path: 'periods.teacher',
        select: 'name',
        match: { _id: { $exists: true } }
      })
      .sort({ day: 1, 'periods.periodNumber': 1 })
      .lean();

    if (schedules.length === 0) {
      return res.status(404).json({ msg: MESSAGES.ERROR.NOT_FOUND });
    }

    res.json(schedules);
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ msg: MESSAGES.ERROR.GENERAL });
  }
};

// @desc    Tạo thời khóa biểu mới
// @route   POST /api/schedules
// @access  Private (Admin only)
exports.createSchedule = async (req, res) => {
  const { classId, schoolYear, day, periods } = req.body;

  try {
    const user = await User.findById(req.user.id);
    if (!user || user.role !== 'admin') {
      return res.status(403).json({ msg: MESSAGES.ERROR.FORBIDDEN });
    }

    const classExists = await Class.findById(classId);
    if (!classExists) {
      return res.status(404).json({ msg: MESSAGES.CLASS.NOT_FOUND });
    }

    let schedule = await Schedule.findOne({ class: classId, schoolYear, day });
    if (schedule) {
      return res.status(400).json({ msg: MESSAGES.ERROR.VALIDATION_FAILED });
    }

    schedule = new Schedule({
      class: classId,
      schoolYear,
      day,
      periods,
    });

    await schedule.save();
    await schedule.populate([
      { path: 'class', select: 'name' },
      { path: 'periods.teacher', select: 'name' },
    ]);
    res.json(schedule);
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ msg: MESSAGES.ERROR.GENERAL });
  }
};

// @desc    Cập nhật thời khóa biểu
// @route   PUT /api/schedules/:id
// @access  Private (Admin only)
exports.updateSchedule = async (req, res) => {
  const { classId, schoolYear, day, periods } = req.body;

  try {
    const user = await User.findById(req.user.id);
    if (!user || user.role !== 'admin') {
      return res.status(403).json({ msg: MESSAGES.ERROR.FORBIDDEN });
    }

    let schedule = await Schedule.findById(req.params.id);
    if (!schedule) {
      return res.status(404).json({ msg: MESSAGES.ERROR.NOT_FOUND });
    }

    const scheduleFields = {};
    if (classId) scheduleFields.class = classId;
    if (schoolYear) scheduleFields.schoolYear = schoolYear;
    if (day) scheduleFields.day = day;

    // Nếu có periods mới, thêm vào mảng periods hiện tại
    let updateQuery = { $set: scheduleFields };
    if (periods && Array.isArray(periods)) {
      updateQuery.$push = { periods: { $each: periods } }; // Thêm từng period mới
    }

    schedule = await Schedule.findByIdAndUpdate(
      req.params.id,
      updateQuery,
      { new: true }
    )
      .populate('class', 'name')
      .populate('periods.teacher', 'name');

    res.json(schedule);
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ msg: MESSAGES.ERROR.GENERAL });
  }
};

// @desc    Xóa thời khóa biểu
// @route   DELETE /api/schedules/:id
// @access  Private (Admin only)
exports.deleteSchedule = async (req, res) => {
  try {
    const user = await User.findById(req.user.id);
    if (!user || user.role !== 'admin') {
      return res.status(403).json({ msg: MESSAGES.ERROR.FORBIDDEN });
    }

    const schedule = await Schedule.findById(req.params.id);
    if (!schedule) {
      return res.status(404).json({ msg: MESSAGES.ERROR.NOT_FOUND });
    }

    await Schedule.findByIdAndDelete(req.params.id);
    res.json({ msg: MESSAGES.ERROR.GENERAL });
  } catch (err) {
    console.error(err.message);
    res.status(500).json({ msg: MESSAGES.ERROR.GENERAL });
  }
};