// tests/violation.test.js
const request = require('supertest');
const mongoose = require('mongoose');
const app = require('../server');
const User = require('../models/User');
const Class = require('../models/Class');
const Violation = require('../models/Violation');
const StudentConduct = require('../models/StudentConduct');
const { VIOLATION_TYPES, VIOLATION_STATUS } = require('../constants/violationConstants');
const { ROLES } = require('../constants/roleConstants');

describe('Violation System Tests', () => {
  let adminToken, teacherToken, studentToken;
  let adminUser, teacherUser, studentUser;
  let testClass, testViolation;

  beforeAll(async () => {
    // Kết nối database test
    await mongoose.connect(process.env.MONGO_URI_TEST || 'mongodb://localhost:27017/school_test');
    
    // Tạo test users
    adminUser = await User.create({
      name: 'Admin Test',
      email: '<EMAIL>',
      password: 'password123',
      role: [ROLES.ADMIN],
      phoneNumber: '0123456789'
    });

    teacherUser = await User.create({
      name: 'Teacher Test',
      email: '<EMAIL>',
      password: 'password123',
      role: [ROLES.TEACHER],
      phoneNumber: '0123456788'
    });

    studentUser = await User.create({
      name: 'Student Test',
      email: '<EMAIL>',
      password: 'password123',
      role: [ROLES.STUDENT],
      studentId: 'ST001',
      phoneNumber: '0123456787'
    });

    // Tạo test class
    testClass = await Class.create({
      name: '10A1',
      schoolYear: '2024-2025',
      homeroomTeacher: teacherUser._id,
      totalStudents: 30
    });

    // Đăng nhập để lấy token
    const adminLogin = await request(app)
      .post('/api/auth/login')
      .send({ email: '<EMAIL>', password: 'password123' });
    adminToken = adminLogin.body.token;

    const teacherLogin = await request(app)
      .post('/api/auth/login')
      .send({ email: '<EMAIL>', password: 'password123' });
    teacherToken = teacherLogin.body.token;

    const studentLogin = await request(app)
      .post('/api/auth/login')
      .send({ email: '<EMAIL>', password: 'password123' });
    studentToken = studentLogin.body.token;
  });

  afterAll(async () => {
    // Cleanup
    await User.deleteMany({});
    await Class.deleteMany({});
    await Violation.deleteMany({});
    await StudentConduct.deleteMany({});
    await mongoose.connection.close();
  });

  describe('POST /api/violations', () => {
    test('Tạo vi phạm thành công bởi teacher', async () => {
      const violationData = {
        studentId: studentUser._id,
        classId: testClass._id,
        schoolYear: '2024-2025',
        violationType: VIOLATION_TYPES.LATE,
        description: 'Đi học muộn 15 phút',
        violationDate: new Date()
      };

      const response = await request(app)
        .post('/api/violations')
        .set('x-auth-token', teacherToken)
        .send(violationData);

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data.violation.violationType).toBe(VIOLATION_TYPES.LATE);
      expect(response.body.data.remainingPoints).toBeLessThan(50);

      testViolation = response.body.data.violation;
    });

    test('Không thể tạo vi phạm với dữ liệu không hợp lệ', async () => {
      const invalidData = {
        studentId: studentUser._id,
        // Thiếu classId
        schoolYear: '2024-2025',
        violationType: VIOLATION_TYPES.LATE
      };

      const response = await request(app)
        .post('/api/violations')
        .set('x-auth-token', teacherToken)
        .send(invalidData);

      expect(response.status).toBe(400);
    });

    test('Student không thể tạo vi phạm', async () => {
      const violationData = {
        studentId: studentUser._id,
        classId: testClass._id,
        schoolYear: '2024-2025',
        violationType: VIOLATION_TYPES.LATE,
        description: 'Test'
      };

      const response = await request(app)
        .post('/api/violations')
        .set('x-auth-token', studentToken)
        .send(violationData);

      expect(response.status).toBe(403);
    });
  });

  describe('GET /api/violations', () => {
    test('Lấy danh sách vi phạm thành công', async () => {
      const response = await request(app)
        .get('/api/violations')
        .set('x-auth-token', teacherToken);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(Array.isArray(response.body.data.violations)).toBe(true);
    });

    test('Filter vi phạm theo lớp', async () => {
      const response = await request(app)
        .get(`/api/violations?classId=${testClass._id}`)
        .set('x-auth-token', teacherToken);

      expect(response.status).toBe(200);
      expect(response.body.data.violations.length).toBeGreaterThan(0);
    });
  });

  describe('PUT /api/violations/:id', () => {
    test('Cập nhật vi phạm thành công', async () => {
      const updateData = {
        description: 'Đi học muộn 20 phút (đã cập nhật)',
        status: VIOLATION_STATUS.PROCESSED
      };

      const response = await request(app)
        .put(`/api/violations/${testViolation._id}`)
        .set('x-auth-token', teacherToken)
        .send(updateData);

      expect(response.status).toBe(200);
      expect(response.body.data.description).toBe(updateData.description);
    });
  });

  describe('POST /api/violations/:id/appeal', () => {
    test('Student khiếu nại vi phạm thành công', async () => {
      const appealData = {
        appealReason: 'Tôi có lý do chính đáng cho việc đi muộn'
      };

      const response = await request(app)
        .post(`/api/violations/${testViolation._id}/appeal`)
        .set('x-auth-token', studentToken)
        .send(appealData);

      expect(response.status).toBe(200);
      expect(response.body.data.status).toBe(VIOLATION_STATUS.APPEALED);
    });
  });

  describe('POST /api/violations/:id/process-appeal', () => {
    test('Teacher xử lý khiếu nại thành công', async () => {
      const processData = {
        result: 'approved'
      };

      const response = await request(app)
        .post(`/api/violations/${testViolation._id}/process-appeal`)
        .set('x-auth-token', teacherToken)
        .send(processData);

      expect(response.status).toBe(200);
    });
  });

  describe('GET /api/violations/stats', () => {
    test('Lấy thống kê vi phạm thành công', async () => {
      const response = await request(app)
        .get('/api/violations/stats')
        .set('x-auth-token', teacherToken);

      expect(response.status).toBe(200);
      expect(response.body.data.byType).toBeDefined();
      expect(response.body.data.overall).toBeDefined();
    });
  });

  describe('GET /api/violations/stats/timeline', () => {
    test('Lấy thống kê timeline thành công', async () => {
      const response = await request(app)
        .get('/api/violations/stats/timeline?period=month')
        .set('x-auth-token', teacherToken);

      expect(response.status).toBe(200);
      expect(Array.isArray(response.body.data)).toBe(true);
    });
  });

  describe('GET /api/violations/student/:studentId', () => {
    test('Lấy vi phạm của học sinh thành công', async () => {
      const response = await request(app)
        .get(`/api/violations/student/${studentUser._id}`)
        .set('x-auth-token', teacherToken);

      expect(response.status).toBe(200);
      expect(response.body.data.violations).toBeDefined();
      expect(response.body.data.conduct).toBeDefined();
    });

    test('Student chỉ xem được vi phạm của chính mình', async () => {
      const response = await request(app)
        .get(`/api/violations/student/${studentUser._id}`)
        .set('x-auth-token', studentToken);

      expect(response.status).toBe(200);
    });
  });

  describe('GET /api/violations/conduct/:studentId', () => {
    test('Lấy điểm thi đua học sinh thành công', async () => {
      const response = await request(app)
        .get(`/api/violations/conduct/${studentUser._id}`)
        .set('x-auth-token', teacherToken);

      expect(response.status).toBe(200);
      expect(response.body.data.currentPoints).toBeDefined();
      expect(response.body.data.totalViolations).toBeDefined();
    });
  });

  describe('PUT /api/violations/conduct/:studentId', () => {
    test('Cập nhật điểm thi đua thành công', async () => {
      const updateData = {
        schoolYear: '2024-2025',
        points: 5,
        reason: 'Thưởng vì tham gia hoạt động tích cực',
        type: 'add'
      };

      const response = await request(app)
        .put(`/api/violations/conduct/${studentUser._id}`)
        .set('x-auth-token', teacherToken)
        .send(updateData);

      expect(response.status).toBe(200);
      expect(response.body.data.currentPoints).toBeGreaterThan(0);
    });
  });

  describe('DELETE /api/violations/:id', () => {
    test('Admin xóa vi phạm thành công', async () => {
      const response = await request(app)
        .delete(`/api/violations/${testViolation._id}`)
        .set('x-auth-token', adminToken);

      expect(response.status).toBe(200);
    });

    test('Teacher không thể xóa vi phạm', async () => {
      // Tạo vi phạm mới để test
      const newViolation = await Violation.create({
        student: studentUser._id,
        class: testClass._id,
        schoolYear: '2024-2025',
        violationType: VIOLATION_TYPES.UNIFORM,
        description: 'Test delete',
        reportedBy: teacherUser._id
      });

      const response = await request(app)
        .delete(`/api/violations/${newViolation._id}`)
        .set('x-auth-token', teacherToken);

      expect(response.status).toBe(403);
    });
  });
});
