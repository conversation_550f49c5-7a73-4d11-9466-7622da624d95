const mongoose = require('mongoose');

const ExamChangeRequestSchema = new mongoose.Schema({
  supervision: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ExamSupervision',
    required: true,
  },
  sessionId: {
    type: mongoose.Schema.Types.ObjectId,
    required: true,
  },
  teacher: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  reason: {
    type: String,
    required: true,
    trim: true,
  },
  originalDate: {
    type: Date,
    required: true,
  },
  originalTimeSlot: {
    type: String,
    enum: ['morning', 'afternoon'],
    required: true,
  },
  proposedNewDate: {
    type: Date,
    required: false,
  },
  proposedNewTimeSlot: {
    type: String,
    enum: ['morning', 'afternoon'],
    required: false,
  },
  status: {
    type: String,
    enum: ['PENDING', 'APPROVED', 'REJECTED'],
    default: 'PENDING',
  },
  approvedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
  approvalDate: {
    type: Date,
  },
  approvalNotes: {
    type: String,
    trim: true,
  },
}, { timestamps: true });

module.exports = mongoose.model('ExamChangeRequest', ExamChangeRequestSchema); 