const express = require('express');
const router = express.Router();

// Import controller functions
const {
  createChangeRequest,
  getChangeRequests,
  getMyChangeRequests,
  approveChangeRequest
} = require('../controllers/examChangeRequestController');

// Import middleware
const { protect, authorize } = require('../middlewares/auth');
const { validateChangeRequest, validateApproval } = require('../middlewares/examValidation');

// @desc    Tạo yêu cầu đổi buổi coi thi
// @route   POST /api/exam-change-requests
// @access  Private (Teacher only)
router.post('/', protect, authorize('teacher'), validateChangeRequest, createChangeRequest);

// @desc    Lấy danh sách yêu cầu đổi buổi (Admin)
// @route   GET /api/exam-change-requests
// @access  Private (Admin only)
router.get('/', protect, authorize('admin'), getChangeRequests);

// @desc    Lấy yêu cầu đổi buổi của gi<PERSON>o viên
// @route   GET /api/exam-change-requests/my-requests
// @access  Private (Teacher only)
router.get('/my-requests', protect, authorize('teacher'), getMyChangeRequests);

// @desc    Phê duyệt/Từ chối yêu cầu đổi buổi
// @route   PUT /api/exam-change-requests/:id/approve
// @access  Private (Admin only)
router.put('/:id/approve', protect, authorize('admin'), validateApproval, approveChangeRequest);

module.exports = router; 