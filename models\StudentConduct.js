// models/StudentConduct.js
const mongoose = require('mongoose');
const { 
  DEFAULT_CONDUCT_POINTS, 
  MIN_CONDUCT_POINTS, 
  MAX_CONDUCT_POINTS,
  CONDUCT_CLASSIFICATION 
} = require('../constants/violationConstants');

const StudentConductSchema = new mongoose.Schema({
  // <PERSON><PERSON><PERSON> sinh
  student: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  // Năm học
  schoolYear: {
    type: String,
    required: true,
    trim: true // Ví dụ: "2024-2025"
  },
  
  // Lớp hiện tại của học sinh trong năm học này
  class: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Class',
    required: true
  },
  
  // Điểm thi đua ban đầu
  initialPoints: {
    type: Number,
    required: true,
    default: DEFAULT_CONDUCT_POINTS,
    min: MIN_CONDUCT_POINTS,
    max: MAX_CONDUCT_POINTS
  },
  
  // Điểm thi đua hiện tại
  currentPoints: {
    type: Number,
    required: true,
    default: DEFAULT_CONDUCT_POINTS,
    min: MIN_CONDUCT_POINTS,
    max: MAX_CONDUCT_POINTS
  },
  
  // Tổng số lần vi phạm
  totalViolations: {
    type: Number,
    default: 0,
    min: 0
  },
  
  // Tổng điểm đã bị trừ
  totalPointsDeducted: {
    type: Number,
    default: 0,
    min: 0
  },
  
  // Tổng điểm đã được cộng (nếu có chế độ thưởng)
  totalPointsAdded: {
    type: Number,
    default: 0,
    min: 0
  },
  
  // Lịch sử thay đổi điểm
  pointHistory: [{
    date: {
      type: Date,
      default: Date.now
    },
    type: {
      type: String,
      enum: ['deduction', 'addition', 'adjustment'],
      required: true
    },
    points: {
      type: Number,
      required: true
    },
    reason: {
      type: String,
      required: true,
      trim: true
    },
    violation: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Violation'
    },
    processedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    previousPoints: {
      type: Number,
      required: true
    },
    newPoints: {
      type: Number,
      required: true
    }
  }],
  
  // Xếp loại hạnh kiểm hiện tại
  currentClassification: {
    type: String,
    enum: ['excellent', 'good', 'fair', 'average', 'weak'],
    default: 'excellent'
  },
  
  // Ghi chú
  notes: {
    type: String,
    trim: true
  },
  
  // Metadata
  createdAt: {
    type: Date,
    default: Date.now
  },
  
  updatedAt: {
    type: Date,
    default: Date.now
  },
  
  lastUpdatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
});

// Index để tối ưu hiệu suất truy vấn
StudentConductSchema.index({ student: 1, schoolYear: 1 }, { unique: true });
StudentConductSchema.index({ class: 1, schoolYear: 1 });
StudentConductSchema.index({ currentPoints: -1 });
StudentConductSchema.index({ totalViolations: -1 });
StudentConductSchema.index({ currentClassification: 1 });

// Middleware để cập nhật updatedAt
StudentConductSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Virtual để lấy tên hiển thị của xếp loại hạnh kiểm
StudentConductSchema.virtual('classificationLabel').get(function() {
  const classification = Object.values(CONDUCT_CLASSIFICATION)
    .find(c => this.currentPoints >= c.min && this.currentPoints <= c.max);
  return classification ? classification.label : 'Không xác định';
});

// Virtual để tính phần trăm điểm thi đua
StudentConductSchema.virtual('pointsPercentage').get(function() {
  return Math.round((this.currentPoints / MAX_CONDUCT_POINTS) * 100);
});

// Static method để lấy điểm thi đua của học sinh
StudentConductSchema.statics.getStudentConduct = function(studentId, schoolYear) {
  return this.findOne({
    student: studentId,
    schoolYear: schoolYear
  })
  .populate('student', 'name studentId')
  .populate('class', 'name')
  .populate('lastUpdatedBy', 'name');
};

// Static method để tạo hoặc lấy điểm thi đua của học sinh
StudentConductSchema.statics.getOrCreateStudentConduct = async function(studentId, schoolYear, classId) {
  let conduct = await this.findOne({
    student: studentId,
    schoolYear: schoolYear
  });
  
  if (!conduct) {
    conduct = new this({
      student: studentId,
      schoolYear: schoolYear,
      class: classId,
      initialPoints: DEFAULT_CONDUCT_POINTS,
      currentPoints: DEFAULT_CONDUCT_POINTS
    });
    await conduct.save();
  }
  
  return conduct;
};

// Static method để lấy thống kê điểm thi đua của lớp
StudentConductSchema.statics.getClassConductStats = function(classId, schoolYear) {
  return this.aggregate([
    {
      $match: {
        class: mongoose.Types.ObjectId(classId),
        schoolYear: schoolYear
      }
    },
    {
      $group: {
        _id: '$currentClassification',
        count: { $sum: 1 },
        avgPoints: { $avg: '$currentPoints' },
        minPoints: { $min: '$currentPoints' },
        maxPoints: { $max: '$currentPoints' }
      }
    },
    {
      $sort: { _id: 1 }
    }
  ]);
};

// Instance method để trừ điểm
StudentConductSchema.methods.deductPoints = function(points, reason, violationId, processedBy) {
  const previousPoints = this.currentPoints;
  this.currentPoints = Math.max(MIN_CONDUCT_POINTS, this.currentPoints - points);
  this.totalPointsDeducted += points;
  this.totalViolations += 1;
  this.lastUpdatedBy = processedBy;
  
  // Cập nhật xếp loại
  this.updateClassification();
  
  // Thêm vào lịch sử
  this.pointHistory.push({
    type: 'deduction',
    points: -points,
    reason: reason,
    violation: violationId,
    processedBy: processedBy,
    previousPoints: previousPoints,
    newPoints: this.currentPoints
  });
  
  return this.save();
};

// Instance method để cộng điểm
StudentConductSchema.methods.addPoints = function(points, reason, processedBy) {
  const previousPoints = this.currentPoints;
  this.currentPoints = Math.min(MAX_CONDUCT_POINTS, this.currentPoints + points);
  this.totalPointsAdded += points;
  this.lastUpdatedBy = processedBy;
  
  // Cập nhật xếp loại
  this.updateClassification();
  
  // Thêm vào lịch sử
  this.pointHistory.push({
    type: 'addition',
    points: points,
    reason: reason,
    processedBy: processedBy,
    previousPoints: previousPoints,
    newPoints: this.currentPoints
  });
  
  return this.save();
};

// Instance method để điều chỉnh điểm
StudentConductSchema.methods.adjustPoints = function(newPoints, reason, processedBy) {
  const previousPoints = this.currentPoints;
  const difference = newPoints - this.currentPoints;
  
  this.currentPoints = Math.max(MIN_CONDUCT_POINTS, Math.min(MAX_CONDUCT_POINTS, newPoints));
  this.lastUpdatedBy = processedBy;
  
  // Cập nhật xếp loại
  this.updateClassification();
  
  // Thêm vào lịch sử
  this.pointHistory.push({
    type: 'adjustment',
    points: difference,
    reason: reason,
    processedBy: processedBy,
    previousPoints: previousPoints,
    newPoints: this.currentPoints
  });
  
  return this.save();
};

// Instance method để cập nhật xếp loại hạnh kiểm
StudentConductSchema.methods.updateClassification = function() {
  const classification = Object.entries(CONDUCT_CLASSIFICATION)
    .find(([key, value]) => this.currentPoints >= value.min && this.currentPoints <= value.max);
  
  if (classification) {
    this.currentClassification = classification[0];
  }
};

// Instance method để hoàn tác vi phạm (khi khiếu nại được chấp nhận)
StudentConductSchema.methods.revertViolation = function(violationId, processedBy) {
  // Tìm lịch sử trừ điểm của vi phạm này
  const historyEntry = this.pointHistory.find(
    entry => entry.violation && entry.violation.toString() === violationId.toString()
  );
  
  if (historyEntry && historyEntry.type === 'deduction') {
    const pointsToRestore = Math.abs(historyEntry.points);
    const previousPoints = this.currentPoints;
    
    this.currentPoints = Math.min(MAX_CONDUCT_POINTS, this.currentPoints + pointsToRestore);
    this.totalPointsDeducted = Math.max(0, this.totalPointsDeducted - pointsToRestore);
    this.totalViolations = Math.max(0, this.totalViolations - 1);
    this.lastUpdatedBy = processedBy;
    
    // Cập nhật xếp loại
    this.updateClassification();
    
    // Thêm vào lịch sử
    this.pointHistory.push({
      type: 'addition',
      points: pointsToRestore,
      reason: `Hoàn tác vi phạm do khiếu nại được chấp nhận`,
      violation: violationId,
      processedBy: processedBy,
      previousPoints: previousPoints,
      newPoints: this.currentPoints
    });
    
    return this.save();
  }
  
  return Promise.resolve(this);
};

module.exports = mongoose.model('StudentConduct', StudentConductSchema);
